package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.core.ParameterizedTypeReference;

import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExecutionParamService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.VariableService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

@ExtendWith(MockitoExtension.class)
public class ExecutionServiceImplTest {

  @Mock
  private ExecutionRepository executionRepository;
  @Mock
  private ExecutionParamService executionParamService;
  @Mock
  private VariableService variableService;
  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private RestTemplate restTemplateKeycloakCentralized;

  @InjectMocks
  private ExecutionServiceImpl service;

  @BeforeEach
  void setup() {

  }

  @Test
  void testGetRepository() {
    assertEquals(executionRepository, service.getRepository());
  }

  @Test
  void testFindAllByIdIn_callsRepository() {
    List<String> ids = List.of("id1", "id2");
    List<ExecutionEntity> dummyList = List.of(new ExecutionEntity());
    when(executionRepository.findAllByIdIn(ids)).thenReturn(dummyList);

    List<ExecutionEntity> result = service.findAllByIdIn(ids);

    assertSame(dummyList, result);
    verify(executionRepository).findAllByIdIn(ids);
  }

  @Test
  void testBuildParams_emptyParamNames_returnsEmptyList() {
    ExecutionEntity exec = new ExecutionEntity();
    when(executionParamService.findAllByExecutionId(anyString())).thenReturn(List.of());

    List<ExecuteScriptParamModel> result = service.buildParams(exec);

    assertTrue(result.isEmpty());
  }

  @Test
  void testBuildParams_withParamsAndVariables_returnsMappedList() {
    ExecutionEntity exec = new ExecutionEntity();
    exec.setId("execId");

    ExecutionParamEntity param1 = new ExecutionParamEntity();
    param1.setName("param1");
    ExecutionParamEntity param2 = new ExecutionParamEntity();
    param2.setName("param2");

    when(executionParamService.findAllByExecutionId("execId"))
            .thenReturn(List.of(param1, param2));

    VariableEntity var1 = new VariableEntity();
    var1.setName("param1");
    var1.setHidden(false);
    var1.setValue("val1");

    VariableEntity var2 = new VariableEntity();
    var2.setName("param2");
    var2.setHidden(true);
    var2.setValue("encryptedVal");

    when(variableService.findAllByNameIn(List.of("param1", "param2")))
            .thenReturn(List.of(var1, var2));

    // Mock KanbanEncryptorUtils.decrypt to return "decryptedVal"
    try (MockedStatic<vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils> utilities = Mockito.mockStatic(vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils.class)) {
      utilities.when(() -> vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils.decrypt("encryptedVal")).thenReturn("decryptedVal");

      List<ExecuteScriptParamModel> result = service.buildParams(exec);

      assertEquals(2, result.size());

      ExecuteScriptParamModel m1 = result.get(0);
      assertEquals("param1", m1.getName());
      assertEquals("val1", m1.getValue());
      assertFalse(m1.isHidden());

      ExecuteScriptParamModel m2 = result.get(1);
      assertEquals("param2", m2.getName());
      assertEquals("decryptedVal", m2.getValue());
      assertTrue(m2.isHidden());
    }
  }

//  @Test
//  void testValidateExecutionRunningRequest_allValid_returnsTrue() throws Exception {
//    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
//    param.setName("param");
//    param.setValue("value");
//
//    var method = ExecutionServiceImpl.class.getDeclaredMethod("validateExecutionRunningRequest", List.class);
//    method.setAccessible(true);
//
//    boolean result = (boolean) method.invoke(service, List.of(param));
//    assertTrue(result);
//  }

//  @Test
//  void testValidateExecutionRunningRequest_invalidNameOrNullValue_returnsFalse() throws Exception {
//    ExecuteScriptParamModel param1 = new ExecuteScriptParamModel();
//    param1.setName(null);
//    param1.setValue("value");
//
//    ExecuteScriptParamModel param2 = new ExecuteScriptParamModel();
//    param2.setName("param");
//    param2.setValue(null);
//
//    var method = ExecutionServiceImpl.class.getDeclaredMethod("validateExecutionRunningRequest", List.class);
//    method.setAccessible(true);
//
//    boolean result1 = (boolean) method.invoke(service, List.of(param1));
//    boolean result2 = (boolean) method.invoke(service, List.of(param2));
//
//    assertFalse(result1);
//    assertFalse(result2);
//  }

  @Test
  void testProcess_SQLType_doesNotCallRestTemplateOrKafka() {
    ExecutionEntity exec = new ExecutionEntity();
    exec.setType(ExecutionTypeEnum.SQL);
    exec.setName("execName");

    // Spy on service to call real buildParams and validateExecutionRunningRequest methods
    ExecutionServiceImpl spyService = Mockito.spy(service);

    // Mock buildParams to return valid params
//    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
//    param.setName("param");
//    param.setValue("value");
//    doReturn(List.of(param)).when(spyService).buildParams(exec);

    spyService.process(exec, "configName");

    verifyNoInteractions(restTemplateKeycloakCentralized);
//    verifyNoInteractions(sysLogKafkaProducerService);
  }

  @Test
  void testProcess_PythonType_successfulCall() {
    ExecutionEntity exec = new ExecutionEntity();
    exec.setType(ExecutionTypeEnum.PYTHON);
    exec.setName("execName");

    ExecutionServiceImpl spyService = Mockito.spy(service);
    Environment mockEnv = Mockito.mock(Environment.class);
    Mockito.when(mockEnv.getProperty(anyString(), anyString())).thenReturn("mockValue");
    ReflectionTestUtils.setField(KanbanPropertyConfigUtils.class, "env", mockEnv);
//    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
//    param.setName("param");
//    param.setValue("value");
//    doReturn(List.of(param)).when(spyService).buildParams(exec);
    ReflectionTestUtils.setField(spyService, "executionRepository", executionRepository);
    ReflectionTestUtils.setField(spyService, "executionParamService", executionParamService);
    ReflectionTestUtils.setField(spyService, "variableService", variableService);
    ReflectionTestUtils.setField(spyService, "sysLogKafkaProducerService", sysLogKafkaProducerService);
    ReflectionTestUtils.setField(spyService, "restTemplateKeycloakCentralized", restTemplateKeycloakCentralized);

    when(restTemplateKeycloakCentralized.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            any(ParameterizedTypeReference.class)
    )).thenReturn(ResponseEntity.ok(new ResponseData<>()));

    spyService.process(exec, "configName");

    verify(restTemplateKeycloakCentralized).exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            any(ParameterizedTypeReference.class)
    );

    verify(sysLogKafkaProducerService, never()).send(any(), any(), any(), any());
  }

  @Test
  void testProcess_PythonType_restTemplateThrowsException_logsAndSendsKafka() {
    ExecutionEntity exec = new ExecutionEntity();
    exec.setType(ExecutionTypeEnum.PYTHON);
    exec.setName("execName");
    Environment mockEnv = Mockito.mock(Environment.class);
    ReflectionTestUtils.setField(KanbanPropertyConfigUtils.class, "env", mockEnv);
    ExecutionServiceImpl spyService = Mockito.spy(service);

    RestTemplate mockRestTemplate = Mockito.mock(RestTemplate.class);
    ReflectionTestUtils.setField(spyService, "restTemplateKeycloakCentralized", mockRestTemplate);

//    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
//    param.setName("param");
//    param.setValue("value");
//    doReturn(List.of(param)).when(spyService).buildParams(exec);

    when(mockRestTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            any(ParameterizedTypeReference.class)
    )).thenThrow(new RuntimeException("HTTP error"));

    spyService.process(exec, "configName");

    verify(sysLogKafkaProducerService).send(
            eq(LogActionEnum.ERROR_TRIGGER),
            eq("configName"),
            eq("execName"),
            contains("HTTP error")
    );
  }


//  @Test
//  void testProcess_invalidParams_sendsDenyTriggerKafka() {
//    ExecutionEntity exec = new ExecutionEntity();
//    exec.setType(ExecutionTypeEnum.PYTHON);
//    exec.setName("execName");
//
//    ExecutionServiceImpl spyService = Mockito.spy(service);
//
//    spyService.process(exec, "configName");
//
//    verify(sysLogKafkaProducerService).send(
//            eq(LogActionEnum.DENY_TRIGGER),
//            eq("configName")
//    );
//
//    verifyNoInteractions(restTemplateKeycloakCentralized);
//  }
}
