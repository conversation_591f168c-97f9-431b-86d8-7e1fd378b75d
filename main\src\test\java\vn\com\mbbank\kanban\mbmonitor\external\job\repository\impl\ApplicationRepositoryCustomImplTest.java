package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.ApplicationRepositoryCustomImpl;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith({MockitoExtension.class})
public class ApplicationRepositoryCustomImplTest {
  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;

  @InjectMocks
  ApplicationRepositoryCustomImpl customApplicationRepository;

}