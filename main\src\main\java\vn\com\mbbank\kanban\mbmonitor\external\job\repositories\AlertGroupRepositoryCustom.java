package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;


/**
 * AlertGroupRepositoryCustom.
 */
public interface AlertGroupRepositoryCustom {

  /**
   * find all alert by paginationRequest.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list of recipient
   */
  List<String> findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum alertGroupStatus);
}
