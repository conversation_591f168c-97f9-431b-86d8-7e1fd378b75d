package vn.com.mbbank.kanban.mbmonitor.external.job.configs;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.enums.BrowserEnum;

/**
 * Manages the lifecycle of Playwright and browser instances for web monitoring jobs.
 * Helps reduce resource usage by reusing launched browser instances across multiple job executions.
 *
 * <AUTHOR>
 * @created_date 05/05/2025
 */
@Component
@Slf4j
public class PlaywrightManager {
  private final Map<BrowserEnum, Browser> browserMap = new ConcurrentHashMap<>();
  private final String executablePathChromium;
  private final String executablePathFirefox;
  private final String executablePathDriver;
  private final Boolean enableFirefox;
  private Playwright playwright;
  
  /**
   * Constructor for PlaywrightManager.
   * Injects browser executable paths from the application's configuration properties.
   *
   * @param executablePathChromium Path to the Chromium-based browser executable.
   * @param executablePathFirefox Path to the Firefox browser executable.
   * @param executablePathDriver Path to the driver executable.
   * @param enableFirefox enable firefox or not.
   */
  public PlaywrightManager(
      @Value("${mbmonitor.monitor.web.executable.path.chromium}") String executablePathChromium,
      @Value("${mbmonitor.monitor.web.executable.path.firefox}") String executablePathFirefox,
      @Value("${mbmonitor.monitor.web.executable.path.driver}") String executablePathDriver,
      @Value("${mbmonitor.monitor.web.firefox.enabled}") Boolean enableFirefox
  ) {
    log.info("Executing PlaywrightManager");
    this.executablePathChromium = executablePathChromium;
    this.executablePathFirefox = executablePathFirefox;
    this.executablePathDriver = executablePathDriver;
    this.enableFirefox = enableFirefox;
  }
  
  /**
   * Initializes the Playwright instance after the bean is constructed.
   * Environment variables are set to:
   * - Skip downloading the browser since it is pre-installed.
   * - Prevent automatic garbage collection of browser binaries.
   */
  @PostConstruct
  public void init() {
    try {
      Map<String, String> env = Map.of(
          "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD", "1",
          "PLAYWRIGHT_SKIP_BROWSER_GC", "1"
      );
      log.info("Initializing Playwright with env: {}", env);
      
      Playwright.CreateOptions options = new Playwright.CreateOptions();
      options.env = env;
      
      System.setProperty("playwright.cli.dir", executablePathDriver);
      this.playwright = Playwright.create(options);
      log.info("Playwright initialized successfully");
    } catch (Exception e) {
      log.error("Failed to initialize Playwright. Reason: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to initialize Playwright", e);
    }
  }
  
  /**
   * Creates a new page from the browser corresponding to the provided browser type.
   * If the browser has not been launched yet, it will be launched and cached for reuse.
   *
   * @param browserEnum The type of browser to use (e.g., CHROME, FIREFOX)
   * @return A new {@link Page} instance for interacting with the web
   */
  public BrowserContext createPageWithCustomBrowser(BrowserEnum browserEnum) {
    log.info("createPageWithCustomBrowser {}", browserEnum);
    Browser browser = browserMap.computeIfAbsent(browserEnum, this::launchBrowser);
    return browser.newContext(new Browser.NewContextOptions()
        .setIgnoreHTTPSErrors(true));
  }
  
  private Browser launchBrowser(BrowserEnum browserEnum) {
    String executablePath;
    BrowserType browserType;
    
    if (BrowserEnum.FIREFOX.equals(browserEnum) && enableFirefox) {
      executablePath = executablePathFirefox;
      browserType = playwright.firefox();
    } else {
      executablePath = executablePathChromium;
      browserType = playwright.chromium();
    }

    Path path = Paths.get(executablePath);
    if (!path.toFile().exists()) {
      log.error("Path {} does not exist", executablePath);
      throw new RuntimeException("Browser executable not found at: " + executablePath);
    }
    
    return browserType.launch(new BrowserType.LaunchOptions()
      .setHeadless(true)
      .setArgs(List.of(
        "--disable-infobars",
        "--disable-notifications",
        "--disable-save-password-bubble",
        "--no-default-browser-check",
        "--no-first-run",
        "--mute-audio"
      ))
      .setExecutablePath(path)
    );
  }
  
  /**
   * Shuts down all active browsers and closes the Playwright instance.
   * Should be called when the application stops or when full cleanup is required.
   */
  @PreDestroy
  public void close() {
    browserMap.values().forEach(browser -> {
      if (browser != null) {
        browser.close();
      }
    });
    playwright.close();
  }
}
