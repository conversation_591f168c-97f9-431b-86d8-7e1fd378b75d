package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ModifyField;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ModifyAlertModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.AlertLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertModifyFieldEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ModifyAlertConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.AlertLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigModifyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;

@Service
@RequiredArgsConstructor
public class ModifyAlertConfigServiceImpl extends BaseServiceImpl<ModifyAlertConfigEntity, Long>
    implements ModifyAlertConfigService {

  private final ModifyAlertConfigRepository modifyAlertConfigRepository;
  private final CustomObjectService customObjectService;
  private final ModifyAlertConfigModifyService modifyAlertConfigModifyService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final ModifyAlertConfigDependencyService modifyAlertConfigDependencyService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final AlertLogModelMapper alertLogModelMapper = AlertLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<ModifyAlertConfigEntity, Long> getRepository() {
    return modifyAlertConfigRepository;
  }

  @Override
  public List<AlertBaseModel> updateAlertsForModify(List<AlertBaseModel> alertRaws) {
    if (KanbanCommonUtil.isEmpty(alertRaws)) {
      return Collections.emptyList();
    }
    List<ModifyAlertConfigEntity> configs =
        modifyAlertConfigRepository.findAllByActiveOrderByPosition(true);
    if (configs.isEmpty()) {
      alertRaws.forEach(a -> {
        a.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
      });
      return alertRaws;
    }

    List<Long> configIds = configs.stream()
        .map(ModifyAlertConfigEntity::getId)
        .collect(Collectors.toList());

    List<ModifyAlertConfigModifyEntity> modifies = modifyAlertConfigModifyService
        .findAllByModifyAlertConfigIdIn(configIds);

    Map<Long, List<ModifyAlertConfigModifyEntity>> modifiesMap = modifies.stream()
        .collect(Collectors.groupingBy(ModifyAlertConfigModifyEntity::getModifyAlertConfigId));

    var priorityIds = modifies.stream()
        .filter(modify -> AlertModifyFieldEnum.PRIORITY.name().equals(modify.getFieldName().toUpperCase()))
        .map(modify -> Long.parseLong(modify.getFieldValue())).collect(
            Collectors.toSet());
    priorityIds.add(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
    var priorityMap = alertPriorityConfigService.findAllById(priorityIds).stream()
        .collect(Collectors.toMap(AlertPriorityConfigEntity::getId, Function.identity()));

    Map<Long, List<ModifyAlertConfigDependencyEntity>> dependenciesMap =
        modifyAlertConfigDependencyService
            .findAllByModifyAlertConfigIdIn(configIds)
            .stream()
            .collect(
                Collectors.groupingBy(ModifyAlertConfigDependencyEntity::getModifyAlertConfigId));

    List<AlertBaseModel> modifiedAlerts = new ArrayList<>();

    for (AlertBaseModel alertRaw : alertRaws) {
      ModifyAlertModel alertWithModifyField = new ModifyAlertModel();
      alertWithModifyField.setPriority(alertRaw.getPriorityRaw());
      alertWithModifyField.setContent(alertRaw.getContent());
      alertWithModifyField.setRecipient(alertRaw.getRecipient());
      var alertInput = alertLogModelMapper.map(alertRaw);
      Set<AlertModifyFieldEnum> modifiedFields = new HashSet<>();
      var effectedConfigs = new ArrayList<ModifyAlertConfigEntity>();

      for (ModifyAlertConfigEntity config : configs) {
        if (dependenciesMatch(config, alertRaw, dependenciesMap)
            &&
            config.getRuleGroup().check(alertWithModifyField,
                obj -> customObjectService.calculatorCustomObjectValue(alertRaw.getContent(),
                    obj.toString()))) {

          List<ModifyAlertConfigModifyEntity> modifyEntities = modifiesMap
              .getOrDefault(config.getId(), Collections.emptyList())
              .stream()
              .sorted(Comparator.comparingLong(ModifyAlertConfigModifyEntity::getId))
              .collect(Collectors.toList());

          applyModifications(modifiedFields, alertRaw, modifyEntities);
          effectedConfigs.add(config);
          if (modifiedFields.size() == AlertModifyFieldEnum.values().length) {
            break;
          }
        }
      }

      if (!modifiedFields.contains(AlertModifyFieldEnum.PRIORITY)) {
        alertRaw.setAlertPriorityConfigId(AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID);
      }
      sendLog(alertInput, alertRaw, priorityMap, effectedConfigs);
      modifiedAlerts.add(alertRaw);
    }
    return modifiedAlerts;
  }

  private boolean dependenciesMatch(ModifyAlertConfigEntity config, AlertEntity alertRaw,
                                    Map<Long, List<ModifyAlertConfigDependencyEntity>> dependenciesMap) {
    List<ModifyAlertConfigDependencyEntity> dependencyEntities = dependenciesMap
        .getOrDefault(config.getId(), Collections.emptyList());
    if (KanbanCommonUtil.isEmpty(dependencyEntities)) {
      return true;
    }
    Set<String> dependencyApplications = dependencyEntities.stream()
        .filter(e -> e.getType() == ModifyAlertConfigDependencyTypeEnum.APPLICATION)
        .map(ModifyAlertConfigDependencyEntity::getDependencyId)
        .collect(Collectors.toSet());

    Set<String> dependencyServices = dependencyEntities.stream()
        .filter(e -> e.getType() == ModifyAlertConfigDependencyTypeEnum.SERVICE)
        .map(ModifyAlertConfigDependencyEntity::getDependencyId)
        .collect(Collectors.toSet());

    Set<String> dependencyServiceWithAllApp = dependencyEntities.stream()
        .filter(
            e -> e.getType() == ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION)
        .map(ModifyAlertConfigDependencyEntity::getDependencyId)
        .collect(Collectors.toSet());

    return (dependencyApplications.contains(alertRaw.getApplicationId())
        && dependencyServices.contains(alertRaw.getServiceId()))
        || dependencyServiceWithAllApp.contains(alertRaw.getServiceId());
  }


  private void applyModifications(Set<AlertModifyFieldEnum> modifiedFields,
                                  @ModifyField AlertEntity alertRaw,
                                  List<ModifyAlertConfigModifyEntity> modifyEntities) {
    for (ModifyAlertConfigModifyEntity modifyConfig : modifyEntities) {
      String fieldValue = modifyConfig.getFieldValue();
      AlertModifyFieldEnum fieldEnum =
          AlertModifyFieldEnum.valueOf(modifyConfig.getFieldName().toUpperCase());
      if (modifiedFields.add(fieldEnum)) {
        switch (fieldEnum) {
          case CONTENT:
            alertRaw.setContent(
                customObjectService.replaceCustomObjectIdsWithCustomObjectValues(
                    alertRaw.getContent(), fieldValue));
            break;
          case PRIORITY:
            alertRaw.setAlertPriorityConfigId(Long.parseLong(fieldValue));
            break;
          case CONTACT:
            alertRaw.setRecipient(fieldValue);
            break;
          default:
            break;
        }
      }
    }
  }

  protected void sendLog(AlertLogModel input, AlertBaseModel alertRaw, Map<Long, AlertPriorityConfigEntity> priorityMap,
                         List<ModifyAlertConfigEntity> effectedConfigs) {
    if (CollectionUtils.isEmpty(effectedConfigs)) {
      return;
    }
    var priority = priorityMap.getOrDefault(alertRaw.getAlertPriorityConfigId(), null);
    var output = alertLogModelMapper.map(alertRaw);
    output.setPriority(Objects.nonNull(priority) ? priority.getName() : StringUtils.EMPTY);
    output.setContact(alertRaw.getRecipient());
    output.setContent(alertRaw.getContent());
    sysLogKafkaProducerService.send(LogActionEnum.MODIFY_ALERT, input, output,
        effectedConfigs.stream().map(ModifyAlertConfigEntity::getName).collect(
            Collectors.joining(CommonConstants.DEFAULT_DELIMITER)));
  }

}
