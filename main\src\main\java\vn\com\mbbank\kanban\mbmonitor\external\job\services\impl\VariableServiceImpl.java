package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.VariableRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.VariableService;

@Service
@RequiredArgsConstructor
public class VariableServiceImpl extends BaseServiceImpl<VariableEntity, String>
    implements VariableService {
  private final VariableRepository variableRepository;

  @Override
  protected JpaCommonRepository<VariableEntity, String> getRepository() {
    return variableRepository;
  }


  @Override
  public List<VariableEntity> findAllByNameIn(List<String> names) {
    return variableRepository.findAllByNameIn(names);
  }

}
