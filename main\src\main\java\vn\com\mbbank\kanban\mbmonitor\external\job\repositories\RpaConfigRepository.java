package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.RpaConfigEntity;

/**
 * Repository table Rpa Config.
 */
@Repository
public interface RpaConfigRepository extends JpaCommonRepository<RpaConfigEntity, String> {
  
  /**
   * Returns the lastest of rpa config.
   *
   * @return the lastest RpaConfigEntity entries
   */
  Optional<RpaConfigEntity> findFirstByOrderByCreatedDateDesc();
  
}
