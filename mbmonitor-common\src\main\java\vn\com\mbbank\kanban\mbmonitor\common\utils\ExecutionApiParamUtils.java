package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashSet;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.KanbanRegexContants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 24/7/2025
 */
public class ExecutionApiParamUtils {
  private static final Logger logger = LoggerFactory.getLogger(ExecutionApiParamUtils.class);

  /**
   * extract template param.
   *
   * @param input Object
   * @return set params
   */
  public static Set<String> extractTemplateParam(Object input) {
    Set<String> result = new HashSet<>();
    Set<Object> visited = Collections.newSetFromMap(new IdentityHashMap<>());
    traverseObjectGetSet(input, result, visited);
    return result;
  }

  /**
   * Recursively traverses an object graph to extract template variables from strings.
   *
   * <p>This method explores the given {@code value} recursively, handling common data structures
   * such as Strings, Maps, Iterables, arrays, and user-defined objects (POJOs). When it encounters
   * a {@link String} that matches a specific {@code TEMPLATE_PATTERN}, it extracts the matched
   * variables (e.g., placeholders like <code>${var}</code>) and adds them to the {@code result} set.</p>
   *
   * <p>To avoid infinite recursion caused by cyclic references, a {@code visited} set is used to
   * track already-visited objects.</p>
   *
   * @param value   the object to traverse, which may contain nested fields or collections
   * @param result  a set to collect all template variable names found (from matched patterns)
   * @param visited a set to track visited objects and prevent infinite recursion
   */
  private static void traverseObjectGetSet(Object value, Set<String> result, Set<Object> visited) {
    if (value == null || visited.contains(value)) {
      return;
    }
    // Mark this object as visited
    visited.add(value);
    Object clone = KanbanMapperUtils.objectToObject(value, value.getClass());


    if (clone instanceof String str) {
      Pattern pattern = Pattern.compile(KanbanRegexContants.EXECUTION_PARAM_PATTERN);
      Matcher matcher = pattern.matcher(str);
      while (matcher.find()) {
        result.add(matcher.group(1));
      }
    } else if (clone instanceof Map<?, ?> map) {
      for (Map.Entry<?, ?> entry : map.entrySet()) {
        traverseObjectGetSet(entry.getKey(), result, visited);
        traverseObjectGetSet(entry.getValue(), result, visited);
      }
    } else if (clone instanceof Iterable<?> iterable) {
      for (Object item : iterable) {
        traverseObjectGetSet(item, result, visited);
      }
    } else if (clone.getClass().isArray()) {
      for (int i = 0; i < java.lang.reflect.Array.getLength(clone); i++) {
        traverseObjectGetSet(java.lang.reflect.Array.get(clone, i), result, visited);
      }
    } else if (KanbanCommonUtil.isPrimitiveTypeOrWrapper(clone.getClass())) {
      // do nothing
    } else {
      for (Field field : clone.getClass().getDeclaredFields()) {
        field.setAccessible(true);
        try {
          Object fieldValue = field.get(clone);
          traverseObjectGetSet(fieldValue, result, visited);
        } catch (IllegalAccessException ignored) {
          logger.info("Failed traverse object");
        }
      }
    }
  }

  /**
   * Fills in all template strings in the provided {@code target} object using the given parameters.
   *
   * <p>This method recursively traverses the {@code target}'s fields and replaces any
   * <code>${var}</code>-style templates found in strings with corresponding values from {@code params}.
   * Complex object graphs with cycles are safely handled via a visited set.</p>
   *
   * @param target the root object to apply template replacements on
   * @param params the list of parameters used to replace template variables
   */
  public static void fillTemplateInObject(Object target, List<ExecuteScriptParamModel> params) {
    if (target == null || params == null) {
      return;
    }
    Set<Object> visited = Collections.newSetFromMap(new IdentityHashMap<>());
    traverseFillObject(target, params, visited);
  }

  /**
   * Recursively traverses the object graph starting from {@code obj}, replacing template
   * strings using the given parameters.
   *
   * @param obj     the object to traverse
   * @param params  the list of parameters for template replacement
   * @param visited a set of visited objects to prevent infinite loops in case of circular references
   */
  private static void traverseFillObject(Object obj, List<ExecuteScriptParamModel> params, Set<Object> visited) {
    if (obj == null || visited.contains(obj)) {
      return;
    }
    visited.add(obj);

    Class<?> clazz = obj.getClass();
    for (Field field : clazz.getDeclaredFields()) {
      field.setAccessible(true);
      try {
        Object value = field.get(obj);

        if (value instanceof String strValue) {
          String newValue = replaceTemplate(strValue, params);
          field.set(obj, newValue);
        } else if (value instanceof Map<?, ?> map) {
          for (Object entryVal : map.values()) {
            traverseFillObject(entryVal, params, visited);
          }
        } else if (value instanceof Iterable<?> iterable) {
          for (Object item : iterable) {
            traverseFillObject(item, params, visited);
          }
        } else if (!KanbanCommonUtil.isPrimitiveTypeOrWrapper(field.getType())) {
          traverseFillObject(value, params, visited);
        }
      } catch (IllegalAccessException ignored) {
        logger.info("Failed fill traverse object");
      }
    }
  }

  /**
   * Replaces all template variables in a string with values from the provided parameters.
   *
   * <p>Template format is assumed to be <code>${varName}</code>. If no matching parameter is found,
   * the original placeholder is left unchanged.</p>
   *
   * @param template the input string containing template variables
   * @param params   the list of parameters used to resolve variable names
   * @return a new string with resolved template variables
   */
  private static String replaceTemplate(String template, List<ExecuteScriptParamModel> params) {
    Pattern pattern = Pattern.compile(KanbanRegexContants.EXECUTION_PARAM_PATTERN);
    Matcher matcher = pattern.matcher(template);
    StringBuilder sb = new StringBuilder();
    while (matcher.find()) {
      String key = matcher.group(1);
      String replacement = params.stream()
              .filter(p -> key.equals(p.getName()))
              .map(ExecuteScriptParamModel::getValue)
              .findFirst()
              .orElse(matcher.group(0));
      matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
    }
    matcher.appendTail(sb);
    return sb.toString();
  }

}
