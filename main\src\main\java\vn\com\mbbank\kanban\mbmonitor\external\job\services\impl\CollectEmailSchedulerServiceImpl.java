package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;


import jakarta.mail.Flags;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Store;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.search.FlagTerm;
import java.io.IOException;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ModifyField;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertBaseModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.EmailConfigModelEmailConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.EmailConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models.EmailModel;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CollectEmailSchedulerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.EmailUtils;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CollectEmailSchedulerServiceImpl implements CollectEmailSchedulerService {
  private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);
  EmailService emailService;
  EmailConfigService emailConfigService;
  CollectEmailConfigRepository collectEmailConfigRepository;
  AlertService alertService;
  CommonRawAlertService commonRawAlertService;
  CustomObjectService customObjectService;
  AlertPriorityConfigService alertPriorityConfigService;

  @Qualifier(BeanNameConstants.COMMON_TASK_EXECUTOR)
  Executor commonTaskExecutor;
  MaintenanceTimeConfigService maintenanceTimeConfigService;
  FilterAlertConfigService filterAlertConfigService;
  ModifyAlertConfigService modifyAlertConfigService;

  @Override
  public void fetchAndProcessEmails(Long emailConfigId) throws BusinessException {
    var emailConfigEntity = emailConfigService.findById(emailConfigId);
    if (ObjectUtils.isEmpty(emailConfigEntity) || !emailConfigEntity.isActive()) {
      return;
    }
    var emailConfigModel = mapAndDecryptEmailConfig(emailConfigEntity);
    List<CollectEmailConfigEntity> collectEmailConfigs = collectEmailConfigRepository
        .findByEmailConfigIdAndActiveIsTrue(emailConfigId);
    if (collectEmailConfigs.isEmpty()) {
      return;
    }
    try (Store store = emailService.connectImapServer(emailConfigModel)) {
      processFolders(store, emailConfigModel, collectEmailConfigs);
      if (!emailConfigEntity.isExecuted()) {
        emailConfigEntity.setExecuted(true);
        emailConfigService.save(emailConfigEntity);
      }
    } catch (Exception e) {
      throw new BusinessException(ErrorCode.EMAIL_READ_MAIL_FAIL, e);
    }
  }

  private EmailConfigModel mapAndDecryptEmailConfig(EmailConfigEntity emailConfigEntity) {
    var emailConfigModel = EmailConfigModelEmailConfigEntityMapper.INSTANCE.map(emailConfigEntity);
    emailConfigModel.setPassword(KanbanEncryptorUtils.decrypt(emailConfigModel.getPassword()));
    return emailConfigModel;
  }

  void processFolders(Store store,
                      EmailConfigModel emailConfigModel,
                      List<CollectEmailConfigEntity> collectEmailConfigs)
      throws MessagingException {
    long currentTime = System.currentTimeMillis();
    logger.info("Start process collect email: {}", new Date(currentTime));
    Folder rootFolder = store.getFolder(EmailConfigConstants.DEFAULT_READ_FOLDER);
    Deque<Folder> stack = new ArrayDeque<>();
    stack.push(rootFolder);
    while (!stack.isEmpty()) {
      Folder currentFolder = stack.pop();
      try {
        if ((currentFolder.getType() & Folder.HOLDS_MESSAGES) != 0) {
          processMessagesInFolder(currentFolder, emailConfigModel, collectEmailConfigs);
        }
        for (Folder subFolder : currentFolder.list()) {
          stack.push(subFolder);
        }
      } catch (MessagingException e) {
        logger.error("Error processing folder: {}", currentFolder.getFullName(), e);
      }
    }
    //logic create alert type absence alert
    List<CollectEmailConfigEntity> absenceConfigs = collectEmailConfigs.stream()
        .filter(config -> {
          if (!CollectEmailConfigTypeEnum.ABSENCE_ALERT.equals(config.getType())) {
            return false;
          }
          Date lastReceivedEmailDate = config.getLastReceivedEmailDate();
          long timeSinceLastEmail = (currentTime - lastReceivedEmailDate.getTime()) / 1000;
          long timeSinceExceededCheckIn = timeSinceLastEmail - config.getAbsenceInterval();
          return timeSinceExceededCheckIn >= -1
              && timeSinceExceededCheckIn % config.getAlertRepeatInterval() < config.getIntervalTime();
        })
        .toList();
    var absenceAlerts = absenceConfigs.stream().map(this::createAlerts).toList();
    collectAlerts(absenceAlerts);
  }

  void processMessagesInFolder(Folder folder,
                               EmailConfigModel emailConfigModel,
                               @ModifyField List<CollectEmailConfigEntity> collectEmailConfigs)
      throws MessagingException {
    folder.open(Folder.READ_WRITE);
    // TODO: Triển khai sau golive 28/03/2025

    // Calendar calendar = Calendar.getInstance();
    // calendar.set(Calendar.HOUR_OF_DAY, 0);
    // calendar.set(Calendar.MINUTE, 0);
    // calendar.set(Calendar.SECOND, 0);
    // calendar.set(Calendar.MILLISECOND, 0);
    // Date todayStart = calendar.getTime();
    // SearchTerm dateTerm = new ReceivedDateTerm(ReceivedDateTerm.GE, todayStart);
    // SearchTerm notFlaggedTerm = new FlagTerm(new Flags(Flags.Flag.FLAGGED),
    // false);
    // SearchTerm combinedTerm = new AndTerm(notFlaggedTerm, dateTerm);
    // Message[] messages = folder.search(combinedTerm);
    Message[] messages = folder.search(new FlagTerm(new Flags(Flags.Flag.FLAGGED), false));
    if (!emailConfigModel.isExecuted()) {
      folder.setFlags(messages, new Flags(Flags.Flag.FLAGGED), true);
    } else {
      List<CompletableFuture<Void>> futures = new ArrayList<>();
      for (Message message : messages) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
          try {
            if (message instanceof MimeMessage) {
              var seen = message.isSet(Flags.Flag.SEEN);
              var email = EmailUtils.parseEmail(message);
              for (var config : collectEmailConfigs) {
                if (config.getRuleGroup().check(email, obj ->
                    customObjectService.calculatorCustomObjectValue(email.getTextBody(), obj.toString()))) {
                  if (CollectEmailConfigTypeEnum.EVENT_BASE_ALERT.equals(config.getType())) {
                    var alerts = createAlerts(email, config);
                    if (!KanbanCommonUtil.isEmpty(alerts)) {
                      collectAlerts(alerts);
                    }
                  } else {
                    long halfIntervalMillis = (emailConfigModel.getIntervalTime() * 1000L) / 2;
                    Date adjustedDate = new Date(System.currentTimeMillis() - halfIntervalMillis);
                    config.setLastReceivedEmailDate(adjustedDate);
                    collectEmailConfigRepository.save(config);
                  }
                }
              }
              message.setFlag(Flags.Flag.FLAGGED, true);
              message.setFlag(Flags.Flag.SEEN, seen);
            }
          } catch (MessagingException | IOException e) {
            throw new RuntimeException(e);
          }
        }, commonTaskExecutor).exceptionally(ex -> {
          logger.error("Error collect email config: {}", ex.getMessage(), ex);
          return null;
        });
        futures.add(future);
      }
      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
      folder.close();
    }
  }

  private AlertBaseModel configureAlertBaseModel(CollectEmailConfigEntity collectEmailConfig, String content) {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId(collectEmailConfig.getServiceId());
    alertBaseModel.setApplicationId(collectEmailConfig.getApplicationId());
    alertBaseModel.setRecipient(collectEmailConfig.getRecipient());
    alertBaseModel.setContentRaw(content);
    alertBaseModel.setContent(content);
    alertBaseModel.setStatus(AlertStatusEnum.NEW);
    alertBaseModel.setAlertGroupId(0L);
    alertBaseModel.setSource(AlertSourceTypeEnum.EMAIL);

    var priority = alertPriorityConfigService.findById(collectEmailConfig.getPriorityConfigId());
    alertBaseModel.setPriorityRaw(KanbanCommonUtil.isEmpty(priority)
        ? AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_NAME
        : priority.getName());

    return alertBaseModel;
  }

  List<AlertBaseModel> createAlerts(EmailModel mail, CollectEmailConfigEntity collectEmailConfig) {
    String textBody = mail.getTextBody();
    String content = CollectEmailAlertContentTypeEnum.CUSTOM_CONTENT.equals(collectEmailConfig.getContentType())
        ? customObjectService.replaceCustomObjectIdsWithCustomObjectValues(textBody,
        collectEmailConfig.getContentValue()).replace("@subject", mail.getSubject())
        : textBody.substring(0, Math.min(AlertConstants.CONTENT_MAX_LENGTH, textBody.length()));
    if (KanbanCommonUtil.isEmpty(content)) {
      return Collections.emptyList();
    }
    AlertBaseModel alertBaseModel = configureAlertBaseModel(collectEmailConfig, content);
    return commonRawAlertService.createRawData(List.of(alertBaseModel), AlertSourceTypeEnum.EMAIL);
  }

  AlertBaseModel createAlerts(CollectEmailConfigEntity collectEmailConfig) {
    String content = collectEmailConfig.getContentValue();
    AlertBaseModel alertBaseModel = configureAlertBaseModel(collectEmailConfig, content);
    var alerts = commonRawAlertService.createRawData(List.of(alertBaseModel), AlertSourceTypeEnum.EMAIL);
    return alerts.isEmpty() ? null : alerts.get(0);
  }


  @Override
  public List<AlertBaseModel> filterAlerts(List<AlertBaseModel> alerts) {
    return filterAlertConfigService.updateAlertsForFilter(alerts);
  }

  @Override
  public List<AlertBaseModel> collectFilters(List<AlertBaseModel> alerts) {
    return alerts.stream().filter(obj -> obj.getIsValid()).toList();
  }

  @Override
  public List<AlertBaseModel> modifyAlerts(List<AlertBaseModel> alerts) {
    return modifyAlertConfigService.updateAlertsForModify(alerts);
  }

  @Override
  public List<AlertBaseModel> maintenanceAlerts(List<AlertBaseModel> alerts) {
    return maintenanceTimeConfigService.updateAlertsForMaintenance(alerts);
  }

  @Override
  public List<AlertBaseModel> saveAlerts(List<AlertBaseModel> alerts) {
    var datas = alertService.saveAll(AlertBaseModelToEntityMapper.INSTANCE.mapTo(alerts));
    return AlertBaseModelToEntityMapper.INSTANCE.map(datas);
  }
}
