package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MaintenanceTimeConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigDependencyService;

class MaintenanceTimeConfigServiceImplTest {

  @Mock
  private MaintenanceTimeConfigRepository maintenanceTimeConfigRepository;

  @Mock
  private RuleGroupType ruleGroup;

  @Mock
  private CustomObjectService customObjectService;

  @InjectMocks
  @Spy
  private MaintenanceTimeConfigServiceImpl service;

  @Mock
  private MaintenanceTimeConfigDependencyService maintenanceTimeConfigDependencyService;
  @Mock
  private ApplicationService applicationService;

  @Test
  void getRepository() {
    JpaCommonRepository<MaintenanceTimeConfigEntity, Long> result = service.getRepository();
    assertEquals(maintenanceTimeConfigRepository, result);
  }

  private MaintenanceTimeConfigEntity config;
  private MaintenanceTimeConfigDependencyEntity dependencyApp;
  private MaintenanceTimeConfigDependencyEntity dependencyService;
  private MaintenanceTimeConfigDependencyEntity dependencyOther;
  private ApplicationEntity application;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    // Mock dữ liệu config
    config = new MaintenanceTimeConfigEntity();
    config.setId(1L);

    // Mock dữ liệu dependencies
    dependencyApp = new MaintenanceTimeConfigDependencyEntity();
    dependencyApp.setDependencyId("app-1");
    dependencyApp.setType(MaintenanceTimeConfigDependencyTypeEnum.APPLICATION);

    dependencyService = new MaintenanceTimeConfigDependencyEntity();
    dependencyService.setDependencyId("service-1");
    dependencyService.setType(MaintenanceTimeConfigDependencyTypeEnum.SERVICE);

    dependencyOther = new MaintenanceTimeConfigDependencyEntity();
    dependencyOther.setDependencyId("service-2");
    dependencyOther.setType(null); // Trường hợp mặc định (cả service và application)

    // Mock dữ liệu ApplicationEntity
    application = new ApplicationEntity();
    application.setId("app-2");
  }

  @Test
  void getAlertValueMap_success() {
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var alert = new AlertEntity();
    alert.setContent("abc");
    alert.setAlertPriorityConfigId(1L);
    var res = CustomObjectUtils.getAlertConditionValueMap(alert, List.of(customObject));
    Assertions.assertNotNull(res);
  }

  @Test
  void alertsMatchingMaintenance() {
    Date now = new Date();
    AlertBaseModel alert1 = new AlertBaseModel();
    alert1.setContent("Critical Alert 1");
    alert1.setStatus(AlertStatusEnum.CLOSE);
    alert1.setServiceId("1L");
    alert1.setApplicationId("1L");
    List<AlertBaseModel> alerts = List.of(alert1);
    ruleGroup = new RuleGroupType();
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    MaintenanceTimeConfigEntity maintenanceConfig = new MaintenanceTimeConfigEntity();
    maintenanceConfig.setId(1L);
    maintenanceConfig.setRuleGroup(ruleGroup);
    maintenanceConfig.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    MaintenanceTimeConfigResponse maintenanceResponse = new MaintenanceTimeConfigResponse();
    maintenanceResponse.setRuleGroup(maintenanceConfig.getRuleGroup());
    maintenanceResponse.setServiceIds(List.of("1L"));
    maintenanceResponse.setApplicationIds(List.of("1L"));
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(maintenanceConfig));
    Mockito.doReturn(true)
        .when(service)
        .isEffectiveNow(Mockito.any(), Mockito.any(Date.class));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(1L))
        .thenReturn(Collections.emptyList());
    Mockito.doReturn(true)
        .when(service)
        .isConfigRelatedToAlert(Mockito.any(), Mockito.any());
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    assertNotNull(result);
    Assertions.assertEquals(AlertStatusEnum.MAINTENANCE, result.get(0).getStatus());
    Mockito.verify(maintenanceTimeConfigRepository).findAllByActive(true);
  }

  @Test
  void alertsMatchingMaintenance_effectFalse() {
    Date now = new Date();
    AlertBaseModel alert1 = new AlertBaseModel();
    alert1.setContent("Critical Alert 1");
    alert1.setStatus(AlertStatusEnum.NEW);
    alert1.setServiceId("1L");
    alert1.setApplicationId("1L");
    List<AlertBaseModel> alerts = List.of(alert1);
    ruleGroup = new RuleGroupType();
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    MaintenanceTimeConfigEntity maintenanceConfig = new MaintenanceTimeConfigEntity();
    maintenanceConfig.setId(1L);
    maintenanceConfig.setRuleGroup(ruleGroup);
    maintenanceConfig.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    MaintenanceTimeConfigResponse maintenanceResponse = new MaintenanceTimeConfigResponse();
    maintenanceResponse.setRuleGroup(maintenanceConfig.getRuleGroup());
    maintenanceResponse.setServiceIds(List.of("1L"));
    maintenanceResponse.setApplicationIds(List.of("1L"));
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(maintenanceConfig));
    Mockito.doReturn(false)
        .when(service)
        .isEffectiveNow(Mockito.any(), Mockito.any(Date.class));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(1L))
        .thenReturn(Collections.emptyList());
    Mockito.doReturn(true)
        .when(service)
        .isConfigRelatedToAlert(Mockito.any(), Mockito.any());
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    Assertions.assertNotNull(result);
    Assertions.assertEquals(AlertStatusEnum.NEW, result.get(0).getStatus());
    Mockito.verify(maintenanceTimeConfigRepository).findAllByActive(true);
  }

  @Test
  void alertsMatchingMaintenance_zzz() {
    Date now = new Date();
    AlertBaseModel alert1 = new AlertBaseModel();
    alert1.setContent("zzzzzzzzzz");
    alert1.setStatus(AlertStatusEnum.NEW);
    alert1.setServiceId("1L");
    alert1.setApplicationId("1L");
    List<AlertBaseModel> alerts = List.of(alert1);
    ruleGroup = new RuleGroupType();
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    MaintenanceTimeConfigEntity maintenanceConfig = new MaintenanceTimeConfigEntity();
    maintenanceConfig.setId(1L);
    maintenanceConfig.setRuleGroup(RuleGroupConverter.convertToRuleGroup(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));
    maintenanceConfig.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    MaintenanceTimeConfigResponse maintenanceResponse = new MaintenanceTimeConfigResponse();
    maintenanceResponse.setRuleGroup(maintenanceConfig.getRuleGroup());
    maintenanceResponse.setServiceIds(List.of("1L"));
    maintenanceResponse.setApplicationIds(List.of("1L"));
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(maintenanceConfig));
    Mockito.doReturn(true)
        .when(service)
        .isEffectiveNow(Mockito.any(), Mockito.any(Date.class));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(1L))
        .thenReturn(Collections.emptyList());
    Mockito.doReturn(true)
        .when(service)
        .isConfigRelatedToAlert(Mockito.any(), Mockito.any());
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    Assertions.assertNotNull(result);
    Assertions.assertEquals(AlertStatusEnum.NEW, result.get(0).getStatus());
    Mockito.verify(maintenanceTimeConfigRepository).findAllByActive(true);
  }

  @Test
  void alertsMatchingMaintenance_ruleGroupNull() throws BusinessException {
    Date now = new Date();
    AlertBaseModel alert1 = new AlertBaseModel();
    alert1.setContent("Critical Alert 1");
    alert1.setStatus(AlertStatusEnum.NEW);
    alert1.setServiceId("1L");
    alert1.setApplicationId("1L");
    List<AlertBaseModel> alerts = List.of(alert1);
    MaintenanceTimeConfigEntity maintenanceConfig = new MaintenanceTimeConfigEntity();
    maintenanceConfig.setId(1L);
    maintenanceConfig.setRuleGroup(null);
    maintenanceConfig.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    MaintenanceTimeConfigResponse maintenanceResponse = new MaintenanceTimeConfigResponse();
    maintenanceResponse.setRuleGroup(maintenanceConfig.getRuleGroup());
    maintenanceResponse.setServiceIds(List.of("1L"));
    maintenanceResponse.setApplicationIds(List.of("1L"));
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(maintenanceConfig));
    Mockito.doReturn(true)
        .when(service)
        .isEffectiveNow(Mockito.any(), Mockito.any(Date.class));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(1L))
        .thenReturn(Collections.emptyList());
    Mockito.doReturn(true)
        .when(service)
        .isConfigRelatedToAlert(Mockito.any(), Mockito.any());
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    Assertions.assertNotNull(result);
    Assertions.assertEquals(AlertStatusEnum.NEW, result.get(0).getStatus());
    Mockito.verify(maintenanceTimeConfigRepository).findAllByActive(true);
  }

  @Test
  void alertsMatchingMaintenance_NEW() throws Exception {
    Date now = new Date();
    AlertBaseModel alert1 = new AlertBaseModel();
    alert1.setContent("Critical Alert 1");
    alert1.setStatus(AlertStatusEnum.NEW);
    alert1.setServiceId("1L");
    alert1.setApplicationId("1L");
    List<AlertBaseModel> alerts = List.of(alert1);
    ruleGroup = new RuleGroupType();

    ruleGroup.setRules(List.of(new RuleGroupType()));
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    MaintenanceTimeConfigEntity maintenanceConfig = new MaintenanceTimeConfigEntity();
    maintenanceConfig.setId(1L);
    maintenanceConfig.setRuleGroup(ruleGroup);
    maintenanceConfig.setRuleGroup(RuleConverterUtils.convertStringToRuleGroupType(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));

    maintenanceConfig.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    MaintenanceTimeConfigResponse maintenanceResponse = new MaintenanceTimeConfigResponse();
    maintenanceResponse.setRuleGroup(maintenanceConfig.getRuleGroup());
    maintenanceResponse.setServiceIds(List.of("1L"));
    maintenanceResponse.setApplicationIds(List.of("1L"));
    Mockito.when(maintenanceTimeConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(maintenanceConfig));
    Mockito.doReturn(true)
        .when(service)
        .isEffectiveNow(Mockito.any(), Mockito.any(Date.class));
    Mockito.when(maintenanceTimeConfigDependencyService.findAllByMaintenanceTimeConfigId(1L))
        .thenReturn(Collections.emptyList());
    Mockito.doReturn(false)
        .when(service)
        .isConfigRelatedToAlert(Mockito.any(), Mockito.any());
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    Assertions.assertNotNull(result);
    Assertions.assertEquals(AlertStatusEnum.NEW, result.get(0).getStatus());
    Mockito.verify(maintenanceTimeConfigRepository).findAllByActive(true);
  }

  @Test
  void testFindConfigDependenciesById() {
    // Arrange
    Long configId = 1L;
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setId(configId);

    List<MaintenanceTimeConfigDependencyEntity> dependencies = List.of(
        createDependency("service-1", MaintenanceTimeConfigDependencyTypeEnum.SERVICE),
        createDependency("application-1", MaintenanceTimeConfigDependencyTypeEnum.APPLICATION),
        createDependency("service-2", MaintenanceTimeConfigDependencyTypeEnum.SERVICE),
        createDependency("application-2", MaintenanceTimeConfigDependencyTypeEnum.APPLICATION)
    );

    List<String> expectedServiceIds = List.of("service-1", "service-2");
    List<String> expectedApplicationIds = List.of("application-1", "application-2");

    MaintenanceTimeConfigResponse expectedResponse = new MaintenanceTimeConfigResponse();
    expectedResponse.setServiceIds(expectedServiceIds);
    expectedResponse.setApplicationIds(expectedApplicationIds);


    MaintenanceTimeConfigResponse result = service.mapDependenciesConfig(config, dependencies);

    // Assert
    Assertions.assertNotNull(result);
    assertEquals(expectedServiceIds, result.getServiceIds());
    assertEquals(expectedApplicationIds, result.getApplicationIds());


  }

  // Helper method to create mock dependencies
  private MaintenanceTimeConfigDependencyEntity createDependency(String id,
                                                                 MaintenanceTimeConfigDependencyTypeEnum type) {
    MaintenanceTimeConfigDependencyEntity dependency = new MaintenanceTimeConfigDependencyEntity();
    dependency.setDependencyId(id);
    dependency.setType(type);
    return dependency;
  }


  @Test
  void testIsConfigRelatedToAlert_ReturnsTrue_WhenConfigMatchesAlert() {
    // Arrange
    MaintenanceTimeConfigResponse config = new MaintenanceTimeConfigResponse();
    config.setServiceIds(List.of("service-1", "service-2"));
    config.setApplicationIds(List.of("app-1", "app-2"));

    AlertEntity alert = new AlertEntity();
    alert.setServiceId("service-1");
    alert.setApplicationId("app-1");

    // Act
    boolean result = service.isConfigRelatedToAlert(config, alert);

    // Assert
    Assertions.assertTrue(result, "Expected config to be related to alert");
  }

  @Test
  void testIsConfigRelatedToAlert_ReturnsFalse_WhenServiceIdDoesNotMatch() {
    // Arrange
    MaintenanceTimeConfigResponse config = new MaintenanceTimeConfigResponse();
    config.setServiceIds(List.of("service-2", "service-3"));
    config.setApplicationIds(List.of("app-1", "app-2"));

    AlertEntity alert = new AlertEntity();
    alert.setServiceId("service-1");
    alert.setApplicationId("app-1");

    // Act
    boolean result = service.isConfigRelatedToAlert(config, alert);

    // Assert
    Assertions.assertFalse(result,
        "Expected config not to be related to alert due to serviceId mismatch");
  }

  @Test
  void testIsConfigRelatedToAlert_ReturnsFalse_WhenApplicationIdDoesNotMatch() {
    // Arrange
    MaintenanceTimeConfigResponse config = new MaintenanceTimeConfigResponse();
    config.setServiceIds(List.of("service-1", "service-2"));
    config.setApplicationIds(List.of("app-2", "app-3"));

    AlertEntity alert = new AlertEntity();
    alert.setServiceId("service-1");
    alert.setApplicationId("app-1");

    // Act
    boolean result = service.isConfigRelatedToAlert(config, alert);

    // Assert
    Assertions.assertFalse(result,
        "Expected config not to be related to alert due to applicationId mismatch");
  }

  @Test
  void testIsConfigRelatedToAlert_ReturnsFalse_WhenBothIdsDoNotMatch() {
    // Arrange
    MaintenanceTimeConfigResponse config = new MaintenanceTimeConfigResponse();
    config.setServiceIds(List.of("service-2", "service-3"));
    config.setApplicationIds(List.of("app-2", "app-3"));

    AlertEntity alert = new AlertEntity();
    alert.setServiceId("service-1");
    alert.setApplicationId("app-1");

    // Act
    boolean result = service.isConfigRelatedToAlert(config, alert);

    // Assert
    Assertions.assertFalse(result,
        "Expected config not to be related to alert due to both serviceId and applicationId mismatches");
  }


  @Test
  public void testIsEffectiveNowWithNullType() {
    // Arrange
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(null); // Giá trị null cho type
    Date now = new Date();

    // Act
    boolean result = service.isEffectiveNow(config, now);

    // Assert
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_StartTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setNextTime(5);
    config.setUnit(MaintenanceTimeUnitEnum.MINUTE);
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_UnitNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setNextTime(5);
    config.setStartTime(new Date());
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_NextTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setStartTime(new Date());
    config.setUnit(MaintenanceTimeUnitEnum.MINUTE);
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_StartTimeAndUnitNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setNextTime(5);
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }


  @Test
  void isNextTimeEffective_StartTimeAndNextTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setUnit(MaintenanceTimeUnitEnum.MINUTE);
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_UnitAndNextTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setStartTime(new Date());
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isNextTimeEffective_AllNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    Date now = new Date(System.currentTimeMillis() + 2 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertFalse(result);
  }

  @Test
  void isEffectiveNow_WithNextTime_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.NEXT_TIME);
    config.setNextTime(3);
    config.setUnit(MaintenanceTimeUnitEnum.MINUTE);
    config.setStartTime(new Date());
    Date now = new Date(System.currentTimeMillis() + 4 * 60 * 1000);
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void isEffectiveNow_WithCronJob() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.CRON_JOB);
    config.setCronExpression("* * * * *");
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isTrue();
  }

  @Test
  void isEffectiveNow_WithCronJob_ExpressionNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.CRON_JOB);
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void isEffectiveNow_WithFromTimeToTime() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    config.setStartTime(new Date(System.currentTimeMillis() - 1000 * 60));
    config.setEndTime(new Date(System.currentTimeMillis() + 1000 * 60));
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isTrue();
  }

  @Test
  void isEffectiveNow_WithFromTimeToTime_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    config.setStartTime(new Date(System.currentTimeMillis() - 2000 * 60));
    config.setEndTime(new Date(System.currentTimeMillis() - 1000 * 60));
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void isEffectiveNow_WithFromTimeToTime_ReturnsFalse2() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    config.setStartTime(new Date(System.currentTimeMillis() + 2000 * 60));
    config.setEndTime(new Date(System.currentTimeMillis() + 3000 * 60));
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void isEffectiveNow_WithFromTimeToTime_StartTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    config.setEndTime(new Date(System.currentTimeMillis() + 1000 * 60));
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void isEffectiveNow_WithFromTimeToTime_EndTimeNull_ReturnsFalse() {
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setType(MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME);
    config.setStartTime(new Date(System.currentTimeMillis() + 1000 * 60));
    Date now = new Date();
    boolean result = service.isEffectiveNow(config, now);
    assertThat(result).isFalse();
  }

  @Test
  void shouldReturnFalseAndLogWarningForInvalidCronExpression() {
    // Given
    MaintenanceTimeConfigEntity config = new MaintenanceTimeConfigEntity();
    config.setCronExpression("invalid-cron-expression");

    // When
    boolean result = service.isCronJobEffective(config, new Date());

    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void updateAlerts_whenAlertsIsEmpty_shouldReturnEmptyList() {
    List<AlertBaseModel> alerts = Collections.emptyList();
    List<AlertBaseModel> result = service.updateAlertsForMaintenance(alerts);
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void findConfigDependenciesById() {
    when(applicationService.findAllByServiceIdInAndDeleted(List.of("service-2"), false))
        .thenReturn(Collections.singletonList(application));

    MaintenanceTimeConfigResponse actualResponse =
        service.mapDependenciesConfig(config,
            List.of(dependencyApp, dependencyService, dependencyOther));

    assertNotNull(actualResponse);
    assertEquals(List.of("service-1", "service-2"), actualResponse.getServiceIds());
    assertEquals(List.of("app-1", "app-2"), actualResponse.getApplicationIds());

    verify(applicationService).findAllByServiceIdInAndDeleted(List.of("service-2"), false);
  }
}
