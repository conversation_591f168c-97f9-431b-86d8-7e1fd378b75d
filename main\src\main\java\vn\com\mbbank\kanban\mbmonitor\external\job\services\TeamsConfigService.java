package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.Optional;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 11:37:47
 */
public interface TeamsConfigService extends BaseService<TeamsConfigEntity, String> {
  /**
   * find teams config.
   *
   * @return TeamsConfigEntity
   */
  Optional<TeamsConfigEntity> findAlertConfig();
}
