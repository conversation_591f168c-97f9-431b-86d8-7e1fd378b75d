package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;

/**
 * interface logic AlertGroupConfigDependencyService.
 */
public interface AlertGroupConfigDependencyService extends BaseService<AlertGroupConfigDependencyEntity, Long> {

  /**
   * find all AlertGroupConfigDependencyEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return list of AlertGroupConfigDependencyEntity
   */
  List<AlertGroupConfigDependencyEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

}
