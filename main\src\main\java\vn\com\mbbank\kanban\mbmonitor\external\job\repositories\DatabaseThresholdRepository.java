package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
public interface DatabaseThresholdRepository extends
    JpaCommonRepository<DatabaseThresholdConfigEntity, String> {
  /**
   * find all config active.
   *
   * @return lst
   */
  List<DatabaseThresholdConfigEntity> findAllByActiveTrue();
}
