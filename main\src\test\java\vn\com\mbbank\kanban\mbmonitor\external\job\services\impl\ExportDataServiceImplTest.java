package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataMessageModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileSourceEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ExportDataRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
public class ExportDataServiceImplTest {
  @InjectMocks
  ExportDataServiceImpl exportDataService;
  @Mock
  ExportDataRepository exportDataRepository;
  @Mock
  AlertService alertService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  FileStorageService fileStorageService;

  @BeforeEach
  void  setup(){
    ReflectionTestUtils.setField(exportDataService, "uploadDir", "/da");
    ReflectionTestUtils.setField(exportDataService, "fileExpiredDays", 7);
  }

  @TestForUser
   void exportFile_alert_success() throws BusinessException, IOException {
    var request = new ExportFileAlertRequest();
    request.setExportDataModel( ExportDataModel.builder().extension(ExportFileTypeEnum.CSV).build());
    var exportData = new ExportDataEntity();
    exportData.setExportedBy("123");
    exportData.setExtension(ExportFileTypeEnum.CSV);
    var file = new FileStorageEntity();
    file.setId(1L);
    when(exportDataRepository.findById(any())).thenReturn(Optional.of(exportData));
    when(alertService.exportFile(any(),any(),anyString())).thenReturn(file);
    doNothing().when(exportDataRepository).updateStatusAndFileStorageId(any(),anyString(),any());
     exportDataService.exportFile("1",request);
    verify(exportDataRepository,times(1)).findById(any());
    verify(alertService,times(1)).exportFile(any(),any(),anyString());
  }
  @TestForUser
   void exportFile_alert_notFoundExportData() {
    var request = new ExportFileAlertRequest();
     exportDataService.exportFile("1",request);
    doNothing().when(exportDataRepository).updateStatusById(any(),anyString());
    exportDataService.exportFile("1",request);
  }
  @TestForUser
  void exportFile_application_success() throws BusinessException, IOException {
    var request = new ExportFileApplicationRequest();
    request.setExportDataModel( ExportDataModel.builder().extension(ExportFileTypeEnum.CSV).build());
    var exportData = new ExportDataEntity();
    exportData.setExportedBy("123");
    var file = new FileStorageEntity();
    file.setId(1L);
    exportData.setExtension(ExportFileTypeEnum.CSV);
    var fileStorageEntity = new FileStorageEntity();
    fileStorageEntity.setId(1L);
    when(exportDataRepository.findById(any())).thenReturn(Optional.of(exportData));
    when(applicationService.exportFile(any(),any(),anyString())).thenReturn(fileStorageEntity);
    doNothing().when(exportDataRepository).updateStatusAndFileStorageId(any(),anyString(),any());
    exportDataService.exportFile("1",request);
    verify(exportDataRepository,times(1)).findById(any());
    verify(applicationService,times(1)).exportFile(any(),any(),anyString());
  }
  @TestForUser
  void exportFile_application_notFoundExportData() {
    var request = new ExportFileApplicationRequest();
    exportDataService.exportFile("1",request);
    doNothing().when(exportDataRepository).updateStatusById(any(),anyString());
    exportDataService.exportFile("1",request);
  }
  @TestForUser
  void exportFile_service_success() throws BusinessException, IOException {
    var request = new ExportFileServiceRequest();
    request.setExportDataModel( ExportDataModel.builder().extension(ExportFileTypeEnum.CSV).build());
    var exportData = new ExportDataEntity();
    exportData.setExportedBy("123");
    var file = new FileStorageEntity();
    file.setId(1L);
    exportData.setExtension(ExportFileTypeEnum.CSV);
    when(exportDataRepository.findById(any())).thenReturn(Optional.of(exportData));
    when(serviceService.exportFile(any(),any(),anyString())).thenReturn(file);
    doNothing().when(exportDataRepository).updateStatusAndFileStorageId(any(),anyString(),any());
    exportDataService.exportFile("1",request);
    verify(exportDataRepository,times(1)).findById(any());
    verify(serviceService,times(1)).exportFile(any(),any(),anyString());
  }
  @TestForUser
  void exportFile_service_error_withFileStorageIdNull() throws BusinessException, IOException {
    var request = new ExportFileServiceRequest();
    request.setExportDataModel( ExportDataModel.builder().extension(ExportFileTypeEnum.CSV).build());
    var exportData = new ExportDataEntity();
    exportData.setExportedBy("123");
    var file = new FileStorageEntity();
    exportData.setExtension(ExportFileTypeEnum.CSV);
    when(exportDataRepository.findById(any())).thenReturn(Optional.of(exportData));
    when(serviceService.exportFile(any(),any(),anyString())).thenReturn(file);
    doNothing().when(exportDataRepository).updateStatusById(any(),anyString());
    exportDataService.exportFile("1",request);
    verify(exportDataRepository,times(1)).findById(any());
    verify(serviceService,times(1)).exportFile(any(),any(),anyString());
  }
  @TestForUser
  void exportFile_service_notFoundExportData() {
    var request = new ExportFileServiceRequest();
    exportDataService.exportFile("1",request);
    doNothing().when(exportDataRepository).updateStatusById(any(),anyString());
    exportDataService.exportFile("1",request);
  }

  @TestForUser
  void deleteExpiredFile(){
    var fileExpired = new ExportDataEntity();
    fileExpired.setFileStorageId(1L);
    fileExpired.setFileName("123");
    when(exportDataRepository.findAllByCreatedDateBeforeAndStatus(any(),any())).thenReturn(List.of());
    doNothing().when(exportDataRepository).updateStatusByIdIn(any(), any());
    var fileStorage = new FileStorageEntity();
    fileStorage.setId(1L);
    fileStorage.setPath("123");
    when(fileStorageService.findByIdIn(any())).thenReturn(List.of(fileStorage));
    exportDataService.deleteExpiredFile();
    verify(exportDataRepository,times(1)).findAllByCreatedDateBeforeAndStatus(any() ,any());
    verify(exportDataRepository,times(1)).updateStatusByIdIn(any() ,any());
    verify(fileStorageService,times(1)).findByIdIn(any());
  }
  @TestForDev
  void isKafkaMultipleGroup_success(){
    assertFalse(exportDataService.isKafkaMultipleGroup());
  }
  @TestForDev
  void getRepository_success(){
    assertEquals(exportDataRepository, exportDataService.getRepository());
  }
  @TestForDev
  void getKafkaType_success(){
    assertEquals(KafkaTypeEnum.EXPORT_DATA, exportDataService.getKafkaType());
  }

  private <T> BaseKafkaModel<T> buildKafkaModel(T payload) throws Exception {
    BaseKafkaModel<T> kafkaModel = new BaseKafkaModel<>();
    kafkaModel.setValue((T) KanbanMapperUtils.objectToJson(payload));
    return kafkaModel;
  }

  private ExportDataEntity buildExportData(String id) {
    ExportDataEntity entity = new ExportDataEntity();
    entity.setId(id);
    entity.setExportedBy("test-user");
    return entity;
  }

  @TestForUser
  void kafkaExecute_alert() throws Exception {
    String id = "123";
    ExportFileAlertRequest request = new ExportFileAlertRequest();
    ExportDataMessageModel message = new ExportDataMessageModel();
    message.setId(id);
    message.setSource(ExportFileSourceEnum.ALERT);
    message.setRequest(request);
    ExportDataEntity entity = buildExportData(id);
    when(exportDataRepository.findById(id)).thenReturn(Optional.of(entity));
    exportDataService.kafkaExecute(buildKafkaModel(message));

  }

  @TestForUser
  void testKafkaExecute_service() throws Exception {
    String id = "456";
    ExportFileServiceRequest request = new ExportFileServiceRequest();
    ExportDataMessageModel message = new ExportDataMessageModel();
    message.setId(id);
    message.setSource(ExportFileSourceEnum.SERVICE);
    message.setRequest(request);
    ExportDataEntity entity = buildExportData(id);
    when(exportDataRepository.findById(id)).thenReturn(Optional.of(entity));
    exportDataService.kafkaExecute(buildKafkaModel(message));
  }

  @TestForUser
  void kafkaExecute_application() throws Exception {
    String id = "789";
    ExportFileApplicationRequest request = new ExportFileApplicationRequest();
    ExportDataMessageModel message = new ExportDataMessageModel();
    message.setId(id);
    message.setSource(ExportFileSourceEnum.APPLICATION);
    message.setRequest(request);
    ExportDataEntity entity = buildExportData(id);
    when(exportDataRepository.findById(id)).thenReturn(Optional.of(entity));
    exportDataService.kafkaExecute(buildKafkaModel(message));

  }
}
