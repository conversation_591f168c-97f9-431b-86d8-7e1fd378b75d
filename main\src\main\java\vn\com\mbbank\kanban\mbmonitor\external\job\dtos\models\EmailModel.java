package vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


/**
 * EmailModel for collect email config.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailModel {
  String subject;
  String textBody;
  String htmlBody;
  String sender;
  List<String> to;
  List<String> cc;
}
