package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventTargetEntity;

/**
 * Service for notification events.
 */
public interface NotificationEventTargetService extends BaseService<NotificationEventTargetEntity, String> {

  /**
   * find all by notificationEventId.
   *
   * @param id notificationEventId
   * @return list of NotificationEventTargetEntity
   */
  List<NotificationEventTargetEntity> findAllByNotificationEventId(String id);

  /**
   * find all by list notificationEventId.
   *
   * @param ids list notificationEventId
   * @return list of NotificationEventTargetEntity
   */
  List<NotificationEventTargetEntity> findAllByNotificationEventIdIn(List<String> ids);
}