package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsIntervalTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsCollectGroupService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 5/7/2025
 */
@Component(JobNameConstants.TEAMS_CONFIG_ALERT)
@RequiredArgsConstructor
public class TeamsAlertCollectGroupJob extends JobConfig implements CommonBaseConsumerService {
  private final TeamsConfigService teamsConfigService;
  private final TeamsCollectGroupService teamsCollectGroupService;

  @Override
  public void executeJob(JobExecutionContext context)
      throws BusinessRuntimeException {
    teamsCollectGroupService.collectGroup();
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    Long everyTime = getEveryMinutes();
    if (everyTime > 0) {
      return Map.of(JobNameConstants.TEAMS_CONFIG_ALERT, everyTime);
    }
    return Map.of();
  }

  @Override
  public Map<String, String> getMappingJobNameAndCronTime() {
    String cronTime = getCronTime();
    if (KanbanCommonUtil.isEmpty(cronTime)) {
      return Map.of();
    }
    return Map.of(JobNameConstants.TEAMS_CONFIG_ALERT, cronTime);
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.TEAMS_CONFIG_ALERT;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {

    TeamsConfigEntity teamsConfig = teamsConfigService.findAlertConfig().orElse(null);
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      return;
    }
    if (TeamsIntervalTypeEnum.EVERY_X_MINUTES.equals(teamsConfig.getIntervalType())) {
      var timeEvery = getEveryMinutes();
      changeConfigJob(true, true, JobNameConstants.TEAMS_CONFIG_ALERT, timeEvery);
    }
    if (TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME.equals(teamsConfig.getIntervalType())) {
      var cronTime = getCronTime();
      changeConfigJob(KafkaJobTypeEnum.NEW_OR_UPDATE, JobNameConstants.TEAMS_CONFIG_ALERT, cronTime);
    }
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.TEAMS_CONFIG_ALERT;
  }

  String getCronTime() {
    TeamsConfigEntity teamsConfig = teamsConfigService.findAlertConfig().orElse(null);
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      return null;
    }
    String cronTime = null;
    if (TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME.equals(teamsConfig.getIntervalType())) {
      cronTime = CronUtils.buildDailyCron(teamsConfig.getInterval());
    }
    return cronTime;
  }

  Long getEveryMinutes() {
    TeamsConfigEntity teamsConfig = teamsConfigService.findAlertConfig().orElse(null);
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      return 0L;
    }
    if (TeamsIntervalTypeEnum.EVERY_X_MINUTES.equals(teamsConfig.getIntervalType())) {
      return NumberUtils.toLong(teamsConfig.getInterval(), 0L) * 60 * 1000;
    }
    return 0L;
  }

  @Override
  public boolean isKafkaMultipleGroup() {
    return true;
  }
}
