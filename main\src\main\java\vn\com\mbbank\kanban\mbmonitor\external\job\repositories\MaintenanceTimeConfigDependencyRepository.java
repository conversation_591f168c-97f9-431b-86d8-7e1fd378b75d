package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;

/**
 * Repository table AlertGroupConfigDependencyRepository.
 */
@Repository
public interface MaintenanceTimeConfigDependencyRepository
    extends JpaCommonRepository<MaintenanceTimeConfigDependencyEntity, Long> {

  /**
   * find all MaintenanceTimeConfigDependencyEntity.
   *
   * @param maintenanceTimeConfigId alertGroupConfigId
   * @return list of MaintenanceTimeConfigDependencyEntity
   */
  List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigId(Long maintenanceTimeConfigId);

  /**
   * find all MaintenanceTimeConfigDependencyEntity in.
   *
   * @param maintenanceTimeConfigIds list of alertGroupConfigId
   * @return list of MaintenanceTimeConfigDependencyEntity
   */
  List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigIdIn(List<Long> maintenanceTimeConfigIds);
}
