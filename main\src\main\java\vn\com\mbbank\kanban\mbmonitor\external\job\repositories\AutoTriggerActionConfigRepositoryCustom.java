package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;

/**
 * AutoTriggerActionConfigRepositoryCustom interface.
 */
public interface AutoTriggerActionConfigRepositoryCustom {

  /**
   * find all auto trigger action config by active.
   *
   * @param triggerTypeEnum to get config
   * @return AutoTriggerActionConfigEntity.
   */
  List<AutoTriggerActionConfigEntity> findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum triggerTypeEnum);
}
