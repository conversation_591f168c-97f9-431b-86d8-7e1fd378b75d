package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@Repository
public interface DatabaseCollectTempRepository extends
    JpaCommonRepository<DatabaseCollectTempEntity, Long> {
  /**
   * find all by database collect id.
   *
   * @param databaseCollectId databaseCollectId
   * @return list DatabaseCollectTempEntity
   */
  List<DatabaseCollectTempEntity> findAllByDatabaseCollectIdOrderByAlertCollectDateDesc(Long databaseCollectId);

  /**
   * delete database temp by DatabaseCollectId.
   *
   * @param databaseCollectId databaseCollectId
   * @return total record
   */
  @Transactional
  Integer deleteAllByDatabaseCollectId(Long databaseCollectId);

}
