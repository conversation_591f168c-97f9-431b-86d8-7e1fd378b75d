package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.impl.CommonAlertPriorityConfigService;

/**
 * interface logic AlertPriorityConfigService.
 */
public interface AlertPriorityConfigService
    extends BaseSoftService<AlertPriorityConfigEntity, Long>,
    CommonAlertPriorityConfigService {
  /**
   * find all alert by paginationRequest.
   *
   * @param alertPriorityConfigId alertPriorityConfigId match condition.
   * @param alertContent          alert content contain condition.
   * @return number of AlertPriorityConfigResponse.
   */

  Long getPriorityConfig(Long alertPriorityConfigId,
                         String alertContent);

  /**
   * find all alert by paginationRequest.
   *
   * @param rawPriority     priority match condition.
   * @param rawAlertContent alert content contain condition.
   * @return a list of AlertPriorityConfigResponse.
   */
  List<AlertPriorityConfigEntity> findAllMatchPriorityConfig(String rawPriority,
                                                             String rawAlertContent);


  /**
   * Retrieves a list of names from the `ALERT_PRIORITY_CONFIG` table where the IDs match the provided list.
   *
   * @param ids a list of IDs to match against the `ID` field.
   * @return a list of names corresponding to the provided IDs.
   */
  List<String> findAllNameByIdIn(List<Long> ids);

  /**
   * find all PriorityConfig.
   *
   * @param ids priority config ids.
   * @return a list of AlertPriorityConfigEntity.
   */
  List<AlertPriorityConfigEntity> findAllByIdIn(List<Long> ids);
}
