package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseConnectionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectService;

class DatabaseConnectionServiceImplTest {

  @Spy
  @InjectMocks
  private DatabaseConnectionServiceImpl databaseConnectionService;
  @Mock
  private CommonDatabaseConnectionService commonDatabaseConnectionService;
  @Mock
  private DatabaseConnectionRepository databaseConnectionRepository;

  @Mock
  private DatabaseCollectService databaseCollectService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<DatabaseConnectionEntity, Long> result =
        databaseConnectionService.getRepository();
    assertEquals(databaseConnectionRepository, result);
  }


  @Test
  void checkConnection_Success() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("encryptedPassword");

    DatabaseConnectionEntity entityFromDb = new DatabaseConnectionEntity();
    entityFromDb.setPassword(KanbanEncryptorUtils.encrypt("realPassword"));

    when(databaseConnectionRepository.findById(any())).thenReturn(Optional.of(entityFromDb));
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    databaseConnectionService.checkConnection(request);
  }

  @Test
  void checkConnection_Success_emptypass() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");

    DatabaseConnectionEntity entityFromDb = new DatabaseConnectionEntity();
    entityFromDb.setPassword(KanbanEncryptorUtils.encrypt("realPassword"));

    when(databaseConnectionRepository.findById(any())).thenReturn(Optional.of(entityFromDb));
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    databaseConnectionService.checkConnection(request);
  }

  @Test
  void checkConnection_Success_create() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("encryptedPassword");

    DatabaseConnectionEntity entityFromDb = new DatabaseConnectionEntity();
    entityFromDb.setPassword(KanbanEncryptorUtils.encrypt("realPassword"));

    when(databaseConnectionRepository.findById(any())).thenReturn(Optional.of(entityFromDb));
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    databaseConnectionService.checkConnection(request);
  }

  @Test
  void checkConnection_ThrowsException_WhenConnectionFails() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("encryptedPassword");

    when(databaseConnectionRepository.findById(1L)).thenReturn(
        Optional.of(new DatabaseConnectionEntity()));
    doReturn(false).when(commonDatabaseConnectionService).testConnection(request);

    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.checkConnection(request));

  }

  @Test
  void checkConnection_ThrowsException_WhenEntityNotFound() {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);

    when(databaseConnectionRepository.findById(1L)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.checkConnection(request));

  }

  @Test
  void checkConnection_by_connection_Id() {
    when(databaseConnectionRepository.findById(anyLong())).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.checkConnection(1L));

  }


  @Test
  void checkConnection_by_connection_Id_success() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("encryptedPassword");

    DatabaseConnectionEntity entityFromDb = new DatabaseConnectionEntity();
    entityFromDb.setPassword(KanbanEncryptorUtils.encrypt("realPassword"));

    when(databaseConnectionRepository.findById(any())).thenReturn(Optional.of(entityFromDb));
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    databaseConnectionService.checkConnection(1L);
  }


  @Test
  void saveDataValid_success() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);

    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);
    when(databaseConnectionRepository.existsByNameAndIdNot(any(), anyLong())).thenReturn(
        false);
    // Mock save method
    when(databaseConnectionRepository.save(any(DatabaseConnectionEntity.class))).thenReturn(
        mappedEntity);

    // Act
    DatabaseConnectionEntity result = databaseConnectionService.saveDataValid(request);

    // Assert
    assertNotNull(result);
    verify(databaseConnectionRepository).save(any(DatabaseConnectionEntity.class));

  }

  @Test
  void saveDataValid_success_case_not_null() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);


    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);
    when(databaseConnectionRepository.existsByName(any())).thenReturn(
        false);
    // Mock save method
    when(databaseConnectionRepository.save(any(DatabaseConnectionEntity.class))).thenReturn(
        mappedEntity);

    // Act
    DatabaseConnectionEntity result = databaseConnectionService.saveDataValid(request);

    // Assert
    assertNotNull(result);
    verify(databaseConnectionRepository).save(any(DatabaseConnectionEntity.class));

  }

  @Test
  void saveDataValid_update_not_found() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    when(databaseConnectionRepository.findById(anyLong())).thenReturn(
        Optional.empty());
    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.saveDataValid(request));
  }

  @Test
  void saveDataValid_update_success() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);

    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);
    when(databaseConnectionRepository.findById(anyLong())).thenReturn(
        Optional.of(mappedEntity));
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    when(databaseConnectionRepository.existsByNameAndIdNot(any(), anyLong())).thenReturn(
        false);

    // Mock save method
    when(databaseConnectionRepository.save(any(DatabaseConnectionEntity.class))).thenReturn(
        mappedEntity);
    databaseConnectionService.saveDataValid(request);
  }

  @Test
  void saveDataValid_update_exists() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);

    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);
    when(databaseConnectionRepository.findById(anyLong())).thenReturn(
        Optional.of(mappedEntity));
    when(databaseConnectionRepository.existsByNameAndIdNot(any(), anyLong())).thenReturn(
        true);
    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.saveDataValid(request));
  }

  @Test
  void saveDataValid_update_name_exists() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);

    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);
    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);
    when(databaseConnectionRepository.findById(anyLong())).thenReturn(
        Optional.of(mappedEntity));
    when(databaseConnectionRepository.existsByNameAndIdNot(any(), anyLong())).thenReturn(
        true);
    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.saveDataValid(request));
  }

  @Test
  void saveDataValid_create_exists() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    when(databaseConnectionRepository.existsByName(any())).thenReturn(
        true);
    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.saveDataValid(request));
  }

  @Test
  void saveDataValid_create_false_exists() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(true);

    when(databaseConnectionRepository.existsByName(any())).thenReturn(
        false);
    databaseConnectionService.saveDataValid(request);
  }

  @Test
  void saveDataValid_connection_false() throws BusinessException {
// Arrange
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setHost("localhost");
    request.setUserName("user");
    request.setPassword("password");
    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
    DatabaseConnectionEntity mappedEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    mappedEntity.setPassword("encryptedPassword");
    mappedEntity.setIsActive(true);

    when(commonDatabaseConnectionService.testConnection(request)).thenReturn(false);

    BusinessException exception = assertThrows(BusinessException.class, () ->
        databaseConnectionService.saveDataValid(request));
  }

  @Test
  void setActive_success() throws BusinessException {
    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
    request.setId(1L);

    when(databaseConnectionRepository.setActiveById(anyLong(), anyBoolean())).thenReturn(1);
    var result = databaseConnectionService.setActiveById(1L, true);
    assertEquals(1, result);

  }
}
