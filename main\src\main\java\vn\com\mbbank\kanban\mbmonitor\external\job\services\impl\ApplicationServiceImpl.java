package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.FORMAT_YYYY_MM_DD_T_HH_MM_SS;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.convertDefaultStringDateToFormatedString;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ApplicationWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ApplicationRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.BatchFetcher;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.ExportFileProcessor;


@Service
@RequiredArgsConstructor
public class ApplicationServiceImpl extends BaseSoftServiceImpl<ApplicationEntity, String>
    implements ApplicationService {

  private final ApplicationRepository applicationRepository;
  private final ExportFileProcessor exportFileProcessor;

  @Override
  public Optional<ApplicationEntity> findFirstByNameIgnoreCaseAndServiceId(
      String applicationNameSource,
      String serviceId) {
    return applicationRepository.findFirstByNameIgnoreCaseAndServiceIdAndDeletedFalse(
        applicationNameSource,
        serviceId);
  }

  @Override
  public Page<ApplicationResponse> findAll(
      ApplicationPaginationRequest applicationPaginationRequest) {
    return applicationRepository.findAll(applicationPaginationRequest);
  }

  @Override
  public List<ApplicationEntity> findAllByServiceId(String serviceId) {
    return applicationRepository.findAllByServiceIdAndDeleted(serviceId, false);
  }


  @Override
  public List<ApplicationWithPriorityResponse> findApplicationWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    List<ApplicationWithPriorityModel> applications =
        applicationRepository.findApplicationWithPriorityByAlertGroupStatus(alertGroupStatus);
    Map<String, ApplicationWithPriorityResponse> results = new HashMap<>();

    for (ApplicationWithPriorityModel application : applications) {
      var applicationResponse = results.computeIfAbsent(application.getId(),
          id -> new ApplicationWithPriorityResponse(
              application.getId(),
              application.getName(),
              application.getServiceId(),
              0,
              new HashSet<>(Set.of(application.getAlertPriorityConfigId()))));
      applicationResponse.getAlertPriorityConfigIds().add(application.getAlertPriorityConfigId());
      applicationResponse.setAlertAmount(
          applicationResponse.getAlertAmount() + application.getAlertAmount());
    }
    return new ArrayList<>(results.values());
  }

  @Override
  public ApplicationResponse findApplicationById(String id) {
    return applicationRepository.findApplicationById(id);
  }


  @Override
  public boolean existByNameAndServiceId(String name, String serviceId) {
    long count = countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(name,
        serviceId);
    return count > 0;
  }


  @Override
  public boolean existByIdNotAndNameAndServiceId(String name, String id, String serviceId) {
    long count = countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(id, name,
        serviceId);
    return count > 0;
  }


  @Override
  protected BaseSoftRepository<ApplicationEntity, String> getRepository() {
    return applicationRepository;
  }

  @Override
  public String generateId() {
    Long seqValue = applicationRepository.getNextSequenceValue();
    return "A" + String.format("%05d", seqValue);
  }


  @Override
  public long countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String name, String serviceId) {
    return applicationRepository.countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(name,
        serviceId);
  }

  @Override
  public long countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String name, String id,
                                                                         String serviceId) {
    return applicationRepository.countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(id,
        name, serviceId);
  }

  protected String genTitleForFileExport(ApplicationPaginationRequest request) {
    StringBuilder title = new StringBuilder("Application - Filter by condition: ");
    if (!KanbanStringUtils.isNullOrEmpty(request.getSearch())) {
      title.append("ApplicationName/Description contain keyword ")
          .append(request.getSearch())
          .append(".");
    }
    return title.toString();
  }

  @Override
  public List<String> findAllNameByIdIn(List<String> ids) {
    if (!org.apache.commons.collections4.CollectionUtils.isEmpty(ids)) {
      return applicationRepository.findApplicationNameByIdIn(ids).stream().sorted().toList();
    }
    return Collections.emptyList();
  }

  @Override
  public List<ApplicationResponse> findAllByIdIn(List<String> ids) {
    return CollectionUtils.isEmpty(ids) ? Collections.emptyList() :
        applicationRepository.findAllByIdIn(ids);
  }

  @Override
  public List<ApplicationEntity> findAllByServiceIdInAndDeleted(List<String> serviceIds,
                                                                boolean deleted) {
    return applicationRepository.findAllByServiceIdInAndDeleted(serviceIds, deleted);
  }

  @Override
  public List<ApplicationEntity> findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
      List<String> applicationNames, List<String> applicationIds, List<String> serviceIds) {
    var lowerApplicationNames =
        Optional.ofNullable(applicationNames).orElse(new ArrayList<>()).stream()
            .map(String::toLowerCase).toList();

    return applicationRepository.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
        lowerApplicationNames, applicationIds, serviceIds);
  }

  @Override
  public FileStorageEntity exportFile(ExportFileApplicationRequest request, String userName, String filePath)
      throws IOException, BusinessException {
    ExportFileDto fileDto = ExportFileDto.builder()
        .attributes(request.getAttributes())
        .title(List.of(genTitleForFileExport(request.getPaginationRequest())))
        .build();
    BatchFetcher<ApplicationResponse> batchFetcher = (offset, limit) -> {
      request.getPaginationRequest().setPage(offset / limit);
      request.getPaginationRequest().setSize(limit);
      Page<ApplicationResponse> page = findAll(request.getPaginationRequest());
      List<ApplicationResponse> apps = page.getContent();
      apps.forEach(app -> app.setCreatedDate(
          convertDefaultStringDateToFormatedString(app.getCreatedDate(), FORMAT_YYYY_MM_DD_T_HH_MM_SS)
      ));
      return apps;
    };
    return exportFileProcessor.exportFileCommon(request, userName, filePath, fileDto, batchFetcher);
  }


}
