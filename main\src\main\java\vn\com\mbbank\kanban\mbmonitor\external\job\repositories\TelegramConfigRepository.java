package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Repository
public interface TelegramConfigRepository extends JpaCommonRepository<TelegramConfigEntity, String> {
  /**
   * Find first config by type.
   *
   * @param type TelegramConfigTypeEnum
   * @return telegram config
   */
  Optional<TelegramConfigEntity> findFirstByType(TelegramConfigTypeEnum type);
}
