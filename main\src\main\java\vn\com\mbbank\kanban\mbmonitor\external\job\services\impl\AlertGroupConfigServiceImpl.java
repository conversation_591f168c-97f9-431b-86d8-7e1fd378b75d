package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigService;

/**
 * Service Logic AlertGroupConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class AlertGroupConfigServiceImpl extends BaseSoftServiceImpl<AlertGroupConfigEntity, Long>
    implements AlertGroupConfigService {

  private final AlertGroupConfigRepository alertGroupConfigRepository;


  @Override
  protected BaseSoftRepository<AlertGroupConfigEntity, Long> getRepository() {
    return alertGroupConfigRepository;
  }

  @Override
  public List<AlertGroupConfigEntity> findAllWithDeletedAndSearch(Boolean withDeleted, String search) {
    if (withDeleted) {
      return alertGroupConfigRepository.findAllByDeletedAndSearch(null, search);
    }
    return alertGroupConfigRepository.findAllByDeletedAndSearch(false, search);
  }

  @Override
  public List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(String serviceId,
                                                                                            String applicationId,
                                                                                            Boolean deleted,
                                                                                            Boolean active) {
    return alertGroupConfigRepository.findAllByServiceIdAndApplicationIdAndDeletedAndActive(serviceId, applicationId,
        deleted, active);
  }


  @Override
  public List<AlertGroupConfigEntity> findAllByDeletedAndActive(Boolean deleted, Boolean active) {
    return alertGroupConfigRepository.findAllByDeletedAndActive(deleted, active);
  }

}
