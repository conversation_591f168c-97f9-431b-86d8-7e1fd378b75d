package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigConditionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigConditionService;


@Service
@RequiredArgsConstructor
public class AlertGroupConfigConditionServiceImpl extends BaseServiceImpl<AlertGroupConfigConditionEntity, Long>
    implements AlertGroupConfigConditionService {

  private final AlertGroupConfigConditionRepository alertGroupConfigConditionRepository;

  @Override
  protected JpaCommonRepository<AlertGroupConfigConditionEntity, Long> getRepository() {
    return alertGroupConfigConditionRepository;
  }

  @Override
  public List<AlertGroupConfigConditionEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId) {
    return alertGroupConfigConditionRepository.findAllByAlertGroupConfigId(alertGroupConfigId);
  }

  @Override
  public void deleteAllByAlertGroupConfigId(Long alertGroupConfigId) {
    alertGroupConfigConditionRepository.deleteAllByAlertGroupConfigId(alertGroupConfigId);
  }
}
