package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;

/**
 * Service interface for managing modify alert configurations dependency in the system.
 */
public interface ModifyAlertConfigDependencyService extends BaseService<ModifyAlertConfigDependencyEntity, Long> {
  /**
   * find all ModifyAlertConfigDependencyEntity.
   *
   * @param modifyAlertConfigId ModifyAlertConfigId
   * @return list of ModifyAlertConfigDependencyEntity
   */
  List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigId(Long modifyAlertConfigId);

  /**
   * find all ModifyAlertConfigDependencyEntity.
   *
   * @param modifyAlertConfigIds list id of modify config
   * @return list of ModifyAlertConfigDependencyEntity
   */
  List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigIdIn(List<Long> modifyAlertConfigIds);
}
