package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.CustomObjectRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomObjectServiceImpl extends BaseServiceImpl<CustomObjectEntity, Long>
    implements CustomObjectService {
  CustomObjectRepository customObjectRepository;

  @Override
  protected JpaCommonRepository<CustomObjectEntity, Long> getRepository() {
    return customObjectRepository;
  }

  @Override
  public String calculatorCustomObjectValue(String content, String id) {
    var customObject =
        findById(Long.valueOf(id));
    return CustomObjectUtils.evaluate(content, customObject);
  }

  @Override
  public List<CustomObjectEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId) {
    return customObjectRepository.findAllByAlertGroupConfigId(alertGroupConfigId);
  }

  @Override
  public List<CustomObjectEntity> findAllByDeletedIsFalse() {
    return customObjectRepository.findAllByDeletedIsFalse();
  }

  @Override
  public String replaceCustomObjectIdsWithCustomObjectValues(String data, String content) {
    Pattern pattern = Pattern.compile("@(\\d+)");
    Matcher matcher = pattern.matcher(content);
    if (!matcher.find()) {
      return content;
    }
    StringBuilder result = new StringBuilder();
    int lastEnd = 0;
    matcher.reset();
    while (matcher.find()) {
      result.append(content, lastEnd, matcher.start());
      String customObjectIdStr = matcher.group(1);
      String value = calculatorCustomObjectValue(data, customObjectIdStr);
      if (KanbanCommonUtil.isEmpty(value)) {
        value = "null";
      }
      result.append(value);
      lastEnd = matcher.end();
    }
    result.append(content.substring(lastEnd));
    return result
        .substring(0, Math.min(AlertConstants.CONTENT_MAX_LENGTH, result.toString().length()));
  }

}
