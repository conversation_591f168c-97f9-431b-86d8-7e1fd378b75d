package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.List;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigExecutionMapRepository;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionConfigExecutionMapServiceImplTest {

    @Mock
    private AutoTriggerActionConfigExecutionMapRepository repository;

    @InjectMocks
    private AutoTriggerActionConfigExecutionMapServiceImpl service;

    private AutoTriggerActionConfigExecutionMapEntity createTestExecutionMap(String id, String configId, String executionId) {
        AutoTriggerActionConfigExecutionMapEntity executionMap = new AutoTriggerActionConfigExecutionMapEntity();
        executionMap.setId(id);
        executionMap.setAutoTriggerActionConfigId(configId);
        executionMap.setExecutionId(executionId);
        return executionMap;
    }

    @BeforeEach
    void setUp() {
        // Setup any common test data if needed
    }

    @Test
    void testGetRepository_shouldReturnCorrectRepository() {
        // When & Then
        assertEquals(repository, service.getRepository());
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldReturnExecutionMaps_whenConfigIdExists() {
        // Given
        String configId = "config1";
        
        AutoTriggerActionConfigExecutionMapEntity map1 = createTestExecutionMap("map1", configId, "execution1");
        AutoTriggerActionConfigExecutionMapEntity map2 = createTestExecutionMap("map2", configId, "execution2");
        AutoTriggerActionConfigExecutionMapEntity map3 = createTestExecutionMap("map3", configId, "execution3");

        List<AutoTriggerActionConfigExecutionMapEntity> expectedMaps = List.of(map1, map2, map3);

        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(expectedMaps);

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(expectedMaps, result);
        
        // Verify all maps belong to the correct config
        assertTrue(result.stream().allMatch(map -> configId.equals(map.getAutoTriggerActionConfigId())));
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldReturnEmptyList_whenConfigIdNotExists() {
        // Given
        String configId = "nonexistent";
        
        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldHandleNullConfigId() {
        // Given
        String configId = null;
        
        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldHandleEmptyConfigId() {
        // Given
        String configId = "";
        
        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldReturnSingleExecutionMap() {
        // Given
        String configId = "config1";
        
        AutoTriggerActionConfigExecutionMapEntity map = createTestExecutionMap("map1", configId, "execution1");
        List<AutoTriggerActionConfigExecutionMapEntity> expectedMaps = List.of(map);

        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(expectedMaps);

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(map, result.get(0));
        assertEquals(configId, result.get(0).getAutoTriggerActionConfigId());
        assertEquals("execution1", result.get(0).getExecutionId());
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldReturnMultipleExecutionMaps() {
        // Given
        String configId = "config1";
        
        AutoTriggerActionConfigExecutionMapEntity map1 = createTestExecutionMap("map1", configId, "execution1");
        AutoTriggerActionConfigExecutionMapEntity map2 = createTestExecutionMap("map2", configId, "execution2");
        AutoTriggerActionConfigExecutionMapEntity map3 = createTestExecutionMap("map3", configId, "execution3");
        AutoTriggerActionConfigExecutionMapEntity map4 = createTestExecutionMap("map4", configId, "execution4");

        List<AutoTriggerActionConfigExecutionMapEntity> expectedMaps = List.of(map1, map2, map3, map4);

        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(expectedMaps);

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals(expectedMaps, result);
        
        // Verify all execution IDs are different
        List<String> executionIds = result.stream()
                .map(AutoTriggerActionConfigExecutionMapEntity::getExecutionId)
                .toList();
        assertEquals(4, executionIds.stream().distinct().count());
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldPassCorrectParameterToRepository() {
        // Given
        String configId = "test-config-id";
        
        when(repository.findAllByAutoTriggerActionConfigId(any())).thenReturn(Collections.emptyList());

        // When
        service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(eq(configId));
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldReturnCorrectExecutionIds() {
        // Given
        String configId = "config1";
        
        AutoTriggerActionConfigExecutionMapEntity map1 = createTestExecutionMap("map1", configId, "exec-001");
        AutoTriggerActionConfigExecutionMapEntity map2 = createTestExecutionMap("map2", configId, "exec-002");
        AutoTriggerActionConfigExecutionMapEntity map3 = createTestExecutionMap("map3", configId, "exec-003");

        List<AutoTriggerActionConfigExecutionMapEntity> expectedMaps = List.of(map1, map2, map3);

        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(expectedMaps);

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        List<String> executionIds = result.stream()
                .map(AutoTriggerActionConfigExecutionMapEntity::getExecutionId)
                .toList();
        
        assertTrue(executionIds.contains("exec-001"));
        assertTrue(executionIds.contains("exec-002"));
        assertTrue(executionIds.contains("exec-003"));
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }

    @Test
    void testFindAllByAutoTriggerActionConfigId_shouldHandleLargeResultSet() {
        // Given
        String configId = "config1";
        
        List<AutoTriggerActionConfigExecutionMapEntity> largeMaps = new java.util.ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            largeMaps.add(createTestExecutionMap("map" + i, configId, "execution" + i));
        }

        when(repository.findAllByAutoTriggerActionConfigId(configId)).thenReturn(largeMaps);

        // When
        List<AutoTriggerActionConfigExecutionMapEntity> result = service.findAllByAutoTriggerActionConfigId(configId);

        // Then
        assertNotNull(result);
        assertEquals(100, result.size());
        assertEquals(largeMaps, result);
        
        // Verify all maps belong to the correct config
        assertTrue(result.stream().allMatch(map -> configId.equals(map.getAutoTriggerActionConfigId())));
        
        verify(repository, times(1)).findAllByAutoTriggerActionConfigId(configId);
    }
}
