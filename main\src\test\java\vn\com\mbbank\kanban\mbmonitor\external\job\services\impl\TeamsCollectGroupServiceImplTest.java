package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsGroupChatModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsTokenModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsIntervalTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonTeamsService;
import vn.com.mbbank.kanban.mbmonitor.common.services.builder.CommonTeamsServiceBuilder;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupUserService;

@ExtendWith(MockitoExtension.class)
/**
 * Generate by K-tool
 * Create date: 2025-05-09
 */
class TeamsCollectGroupServiceImplTest {

  @Mock
  private TeamsGroupConfigService teamsGroupConfigService;

  @Mock
  private TeamsGroupUserService teamsGroupUserService;

  @Mock
  private TeamsConfigService teamsConfigService;

  @InjectMocks
  private TeamsCollectGroupServiceImpl teamsCollectGroupService;

  @Mock
  private CommonTeamsService commonTeamsService;
  private MockedStatic<KanbanApplicationConfigUtils> mockedUtils;
  @Mock
  CommonTeamsServiceBuilder commonTeamsServiceBuilder;

  @BeforeEach
  void setUp() {
    mockedUtils = mockStatic(KanbanApplicationConfigUtils.class);

    // Mock CommonTeamsService

    lenient().when(commonTeamsService.getToken()).thenReturn("abncasdas");
    TeamsTokenModel teamsTokenModel = new TeamsTokenModel();
    teamsTokenModel.setExpTime(12L);
    teamsTokenModel.setToken("kanban");
    lenient().when(commonTeamsService.getTokenFromTeams()).thenReturn(teamsTokenModel);
    lenient().when(commonTeamsServiceBuilder.getInstance()).thenReturn(commonTeamsService);

    mockedUtils.when(() -> KanbanApplicationConfigUtils.getBean(CommonTeamsService.class)).thenReturn(commonTeamsService);
  }

  @AfterEach
  void tearDown() {
    if (mockedUtils != null) {
      mockedUtils.close();
    }
  }


  @Test
  void collectGroup_ConfigNotFoundTest() {
    // Input: No TeamsConfigEntity found
    // Expected: No exceptions are thrown, and no other services are called

    when(teamsConfigService.findAlertConfig()).thenReturn(Optional.empty());

    assertDoesNotThrow(() -> teamsCollectGroupService.collectGroup());

    verify(teamsConfigService, times(1)).findAlertConfig();
    verify(teamsGroupConfigService, times(0)).deleteAllByTeamsConfigId(anyString());
    verify(teamsGroupUserService, times(0)).deleteAllByTeamsGroupChatIdIn(anyList());
    verify(teamsGroupConfigService, times(0)).saveAll(anyList());
    verify(teamsGroupUserService, times(0)).saveAll(anyList());
  }

  @Test
  void collectGroup_ConfigFoundNoGroupsTest() {
    // Input: TeamsConfigEntity found, but getAllGroupChat returns an empty list
    // Expected: Services are called to delete existing data, but no new data is saved

    TeamsConfigEntity config = new TeamsConfigEntity();
    config.setId("kanban");
    config.setTenantId("thangnv");
    config.setClientId("CMDB");
    config.setClientSecret("kanban");
    config.setEmail("abc");
    config.setPassword("thangnv");

    config.setType(TeamsConfigTypeEnum.ALERT);
    config.setMessageTemplate("thangnv");
    config.setInterval("CMDB");
    config.setIntervalType(TeamsIntervalTypeEnum.EVERY_X_MINUTES);
    config.setDescription("monitor");
    config.setCreatedDate(new Date());
    config.setCreatedBy("CMDB");
    config.setModifiedBy("abc");
    config.setModifiedDate(new Date());
    when(teamsConfigService.findAlertConfig()).thenReturn(Optional.of(config));

    assertDoesNotThrow(() -> teamsCollectGroupService.collectGroup());

    verify(teamsConfigService, times(1)).findAlertConfig();
    verify(teamsGroupConfigService, times(1)).deleteAllByTeamsConfigId("kanban");
    verify(teamsGroupUserService, times(1)).deleteAllByTeamsGroupChatIdIn(Collections.emptyList());
  }

  @Test
  void collectGroup_ConfigFoundWithGroupsTest() {
    // Input: TeamsConfigEntity found, and getAllGroupChat returns a list of groups
    // Expected: Services are called to delete existing data, and new data is saved

    TeamsConfigEntity config = new TeamsConfigEntity();
    config.setId("CMDB");
    config.setTenantId("CMDB");
    config.setClientId("monitor");
    config.setClientSecret("CMDB");
    config.setEmail("kanban");
    config.setPassword("kanban");

    config.setType(TeamsConfigTypeEnum.ALERT);
    config.setMessageTemplate("monitor");
    config.setInterval("abc");

    config.setIntervalType(TeamsIntervalTypeEnum.ONCE_DAILY_AT_TIME);
    config.setDescription("CMDB");
    config.setCreatedDate(new Date());
    config.setCreatedBy("CMDB");
    config.setModifiedBy("monitor");
    config.setModifiedDate(new Date());


    when(teamsConfigService.findAlertConfig()).thenReturn(Optional.of(config));

    //<EMAIL>
    List<TeamsGroupChatModel> groupChats = new ArrayList<>();
    groupChats.clear();
    TeamsGroupChatModel obj1 = new TeamsGroupChatModel();
    obj1.setGroupChatId("monitor");
    obj1.setGroupChatName("thangnv");
    obj1.setEmailInvalid(Arrays.asList("thangnv", "abc", "kanban"));
    obj1.setEmailValid(Arrays.asList("thangnv", "thangnv", "thangnv"));
    obj1.setNextUrl("kanban");
    groupChats.add(obj1);

    TeamsGroupChatModel obj2 = new TeamsGroupChatModel();
    obj2.setGroupChatId("CMDB");
    obj2.setGroupChatName("abc");
    obj2.setEmailInvalid(Arrays.asList("abc", "monitor", "CMDB"));
    obj2.setEmailValid(Arrays.asList("monitor", "CMDB", "thangnv"));
    obj2.setNextUrl("thangnv");
    groupChats.add(obj2);

    TeamsGroupChatModel obj3 = new TeamsGroupChatModel();
    obj3.setGroupChatId("CMDB");
    obj3.setGroupChatName("kanban");
    obj3.setEmailInvalid(Arrays.asList("monitor", "kanban", "CMDB"));
    obj3.setEmailValid(Arrays.asList("abc", "kanban", "monitor"));
    obj3.setNextUrl("abc");
    groupChats.add(obj3);

    when(commonTeamsService.getAllGroupChat()).thenReturn(groupChats);
    TeamsCollectGroupServiceImpl spyService = spy(teamsCollectGroupService);

    List<TeamsGroupConfigEntity> savedGroups = new ArrayList<>();
    savedGroups.clear();
    TeamsGroupConfigEntity teamsGroupConfigEntity = new TeamsGroupConfigEntity();
    teamsGroupConfigEntity.setId("abc");
    teamsGroupConfigEntity.setTeamsConfigId("monitor");
    teamsGroupConfigEntity.setTeamsGroupId("monitor");
    teamsGroupConfigEntity.setTeamsGroupName("thangnv");

    teamsGroupConfigEntity.setCreatedDate(new Date());
    teamsGroupConfigEntity.setCreatedBy("abc");
    teamsGroupConfigEntity.setModifiedBy("abc");
    teamsGroupConfigEntity.setModifiedDate(new Date());
    savedGroups.add(teamsGroupConfigEntity);

    TeamsGroupConfigEntity teamsGroupConfigEntity2 = new TeamsGroupConfigEntity();
    teamsGroupConfigEntity2.setId("CMDB");
    teamsGroupConfigEntity2.setTeamsConfigId("thangnv");
    teamsGroupConfigEntity2.setTeamsGroupId("CMDB");
    teamsGroupConfigEntity2.setTeamsGroupName("abc");
    teamsGroupConfigEntity2.setCreatedDate(new Date());
    teamsGroupConfigEntity2.setCreatedBy("thangnv");
    teamsGroupConfigEntity2.setModifiedBy("monitor");
    teamsGroupConfigEntity2.setModifiedDate(new Date());
    savedGroups.add(teamsGroupConfigEntity2);

    TeamsGroupConfigEntity teamsGroupConfigEntity3 = new TeamsGroupConfigEntity();
    teamsGroupConfigEntity3.setId("kanban");
    teamsGroupConfigEntity3.setTeamsConfigId("monitor");
    teamsGroupConfigEntity3.setTeamsGroupId("CMDB");
    teamsGroupConfigEntity3.setTeamsGroupName("kanban");
    teamsGroupConfigEntity3.setCreatedDate(new Date());
    teamsGroupConfigEntity3.setCreatedBy("CMDB");
    teamsGroupConfigEntity3.setModifiedBy("abc");
    teamsGroupConfigEntity3.setModifiedDate(new Date());
    savedGroups.add(teamsGroupConfigEntity3);


    when(teamsGroupConfigService.saveAll(anyList())).thenReturn(savedGroups);

    assertDoesNotThrow(() -> spyService.collectGroup());

    verify(teamsConfigService, times(1)).findAlertConfig();
    verify(teamsGroupConfigService, times(1)).deleteAllByTeamsConfigId(anyString());
    verify(teamsGroupUserService, times(1)).deleteAllByTeamsGroupChatIdIn(Arrays.asList("monitor", "CMDB", "CMDB"));
    verify(teamsGroupConfigService, times(1)).saveAll(anyList());
    verify(teamsGroupUserService, times(1)).saveAll(anyList());
  }

}