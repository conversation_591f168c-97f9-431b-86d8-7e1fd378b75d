package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class XlsxExporterTest {

  @InjectMocks
  private XlsxExporter xlsxExporter;

  @Mock
  private ExportFileDto exportFileDto;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }


  @Test
  void init_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(
        AttributeInfoDto.builder()
            .position(1)
            .attributeId("attr1")
            .attributeName("Name1")
            .value("value1")
            .build()
    );
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(Arrays.asList("Title1"));

    byte[] result = xlsxExporter.init(exportFileDto);

    assertNotNull(result);
    verify(exportFileDto, times(1)).getAttributes();
  }

  @Test
  void init_withoutTitle_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(
        AttributeInfoDto.builder()
            .position(1)
            .attributeId("attr1")
            .attributeName("Name1")
            .value("value1")
            .build()
    );
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);

    byte[] result = xlsxExporter.init(exportFileDto);

    assertNotNull(result);
    verify(exportFileDto, times(1)).getAttributes();
    verify(exportFileDto, times(1)).getTitle();
  }

  @Test
  void writeBatch_success() throws IOException {
    List<String> batchData = Arrays.asList("data1", "data2");
    List<AttributeInfoDto> attributes = Arrays.asList(
        AttributeInfoDto.builder()
            .position(1)
            .attributeId("attr1")
            .attributeName("Name1")
            .value("value1")
            .build()
    );
    List<List<AttributeInfoDto>> transformedData = Arrays.asList(attributes);

    // First init the exporter
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);
    xlsxExporter.init(exportFileDto);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(batchData, attributes))
          .thenReturn(transformedData);

      byte[] result = xlsxExporter.writeBatch(batchData, exportFileDto);

      assertNotNull(result);
      excelUtilsMock.verify(() -> ExcelUtils.transformDataExport(batchData, attributes));
    }
  }

  @Test
  void close_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(
        AttributeInfoDto.builder()
            .position(1)
            .attributeId("attr1")
            .attributeName("Name1")
            .value("value1")
            .build()
    );
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);

    // First init the exporter
    xlsxExporter.init(exportFileDto);

    byte[] result = xlsxExporter.close();

    assertNotNull(result);
    assertTrue(result.length > 0);
  }

  @Test
  void writeBatch_emptyData_success() throws IOException {
    List<String> batchData = Arrays.asList();
    List<AttributeInfoDto> attributes = Arrays.asList(
        AttributeInfoDto.builder()
            .position(1)
            .attributeId("attr1")
            .attributeName("Name1")
            .build()
    );

    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);
    xlsxExporter.init(exportFileDto);

    byte[] result = xlsxExporter.writeBatch(batchData, exportFileDto);

    assertNotNull(result);
    assertEquals(0, result.length);
  }
}

