package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.BaseCollectAlertService;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
public interface DatabaseThresholdService extends BaseService<DatabaseThresholdConfigEntity, String>,
    BaseCollectAlertService {
  /**
   * Collect database.
   *
   * @param id id collect config
   * @throws BusinessException ex
   */
  @Transactional(rollbackFor = {Exception.class})
  void collect(String id) throws BusinessException;


  /**
   * find all config active.
   *
   * @return lst
   */
  List<DatabaseThresholdConfigEntity> findAllByActiveTrue();
}