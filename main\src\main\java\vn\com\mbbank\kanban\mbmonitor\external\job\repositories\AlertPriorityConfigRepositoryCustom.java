package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;

/**
 * Custom Repository table AlertPriorityConfig.
 */
@Repository
public interface AlertPriorityConfigRepositoryCustom {
  /**
   * find all priority config match rawPriority or rawAlertContent.
   *
   * @param rawPriority     raw priority.
   * @param rawAlertContent raw alert content.
   * @return a list of AlertPriorityConfigResponse.
   */
  List<AlertPriorityConfigEntity> findAllMatchPriorityConfig(String rawPriority, String rawAlertContent);
}