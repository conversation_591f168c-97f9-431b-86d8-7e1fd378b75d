package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.AutoTriggerActionConfigRepositoryCustomImpl;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionConfigRepositoryCustomImplTest {

    @Mock
    private SqlQueryUtil sqlQueryUtil;

    @Mock
    KanbanSqlQueryUtil.SqlQueryModel queryModel;

    @InjectMocks
    private AutoTriggerActionConfigRepositoryCustomImpl repository;

    @BeforeEach
    void setUp() {
        when(sqlQueryUtil.queryModel()).thenReturn(queryModel);
    }

    @Test
    void findAllWithActiveTrueAndTriggerType_shouldReturnEmptyList_whenNoResults() {
        // Given
        AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.CONDITION;
        when(queryModel.queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class)))
                .thenReturn(new ArrayList<>());

        // When
        List<AutoTriggerActionConfigEntity> result = repository.findAllWithActiveTrueAndTriggerType(triggerType);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sqlQueryUtil).queryModel();
        verify(queryModel).queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class));
    }

    @Test
    void findAllWithActiveTrueAndTriggerType_shouldReturnMappedEntities_whenResultsExist() {
        // Given
        AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.TIME;
        
        AutoTriggerActionConfigResponse response1 = AutoTriggerActionConfigResponse.builder()
                .id("config1")
                .name("Test Config 1")
                .description("Test Description 1")
                .active(true)
                .ruleGroupColumn("{\"operator\":\"AND\",\"rules\":[]}")
                .timeSinceLastTrigger(3600L)
                .build();

        AutoTriggerActionConfigResponse response2 = AutoTriggerActionConfigResponse.builder()
                .id("config2")
                .name("Test Config 2")
                .description("Test Description 2")
                .active(true)
                .ruleGroupColumn("{\"operator\":\"OR\",\"rules\":[]}")
                .timeSinceLastTrigger(7200L)
                .build();

        List<AutoTriggerActionConfigResponse> responses = List.of(response1, response2);
        
        when(queryModel.queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class)))
                .thenReturn(responses);

        // When
        List<AutoTriggerActionConfigEntity> result = repository.findAllWithActiveTrueAndTriggerType(triggerType);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        AutoTriggerActionConfigEntity entity1 = result.get(0);
        assertEquals("config1", entity1.getId());
        assertEquals("Test Config 1", entity1.getName());
        assertEquals("Test Description 1", entity1.getDescription());
        assertTrue(entity1.getActive());
        assertEquals(3600L, entity1.getTimeSinceLastTrigger());

        AutoTriggerActionConfigEntity entity2 = result.get(1);
        assertEquals("config2", entity2.getId());
        assertEquals("Test Config 2", entity2.getName());
        assertEquals("Test Description 2", entity2.getDescription());
        assertTrue(entity2.getActive());
        assertEquals(7200L, entity2.getTimeSinceLastTrigger());

        verify(sqlQueryUtil).queryModel();
        verify(queryModel).queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class));
    }

    @Test
    void findAllWithActiveTrueAndTriggerType_shouldHandleNullRuleGroup_whenRuleGroupColumnIsNull() {
        // Given
        AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.CONDITION;
        
        AutoTriggerActionConfigResponse response = AutoTriggerActionConfigResponse.builder()
                .id("config1")
                .name("Test Config")
                .description("Test Description")
                .active(true)
                .ruleGroupColumn(null)
                .timeSinceLastTrigger(3600L)
                .build();

        List<AutoTriggerActionConfigResponse> responses = List.of(response);
        
        when(queryModel.queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class)))
                .thenReturn(responses);

        // When
        List<AutoTriggerActionConfigEntity> result = repository.findAllWithActiveTrueAndTriggerType(triggerType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        AutoTriggerActionConfigEntity entity = result.get(0);
        assertEquals("config1", entity.getId());
        assertEquals("Test Config", entity.getName());
        assertNull(entity.getRuleGroup());
    }

    @Test
    void findAllWithActiveTrueAndTriggerType_shouldHandleInvalidRuleGroup_whenRuleGroupColumnIsInvalid() {
        // Given
        AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.TIME;
        
        AutoTriggerActionConfigResponse response = AutoTriggerActionConfigResponse.builder()
                .id("config1")
                .name("Test Config")
                .description("Test Description")
                .active(true)
                .ruleGroupColumn("invalid json")
                .timeSinceLastTrigger(3600L)
                .build();

        List<AutoTriggerActionConfigResponse> responses = List.of(response);
        
        when(queryModel.queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class)))
                .thenReturn(responses);

        // When
        List<AutoTriggerActionConfigEntity> result = repository.findAllWithActiveTrueAndTriggerType(triggerType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        AutoTriggerActionConfigEntity entity = result.get(0);
        assertEquals("config1", entity.getId());
        assertEquals("Test Config", entity.getName());
        // RuleGroupConverter should handle invalid JSON gracefully and return null
        assertNull(entity.getRuleGroup());
    }

    @Test
    void findAllWithActiveTrueAndTriggerType_shouldIncludeTimeConditionInQuery() {
        // Given
        AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.TIME;
        when(queryModel.queryForList(anyString(), any(Map.class), eq(AutoTriggerActionConfigResponse.class)))
                .thenReturn(new ArrayList<>());

        // When
        repository.findAllWithActiveTrueAndTriggerType(triggerType);

        // Then
        verify(queryModel).queryForList(
                argThat(query -> query.contains("(LAST_RUN + NUMTODSINTERVAL(TIME_SINCE_LAST_TRIGGER, 'SECOND')) < SYSTIMESTAMP")
                        && query.contains("OR LAST_RUN IS NULL")),
                any(Map.class),
                eq(AutoTriggerActionConfigResponse.class)
        );
    }
}
