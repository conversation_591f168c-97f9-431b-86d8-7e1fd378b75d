package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;

/**
 * Repository table AlertPriorityConfig.
 */
public interface AlertGroupConfigRepositoryCustom {

  /**
   * find all group config match conditions.
   *
   * @param serviceId     serviceId
   * @param applicationId applicationId
   * @param deleted       deleted status
   * @param active        active status
   * @return list of AlertPriorityConfigEntity
   */
  List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(String serviceId,
                                                                                     String applicationId,
                                                                                     Boolean deleted, Boolean active);

  /**
   * find all group config by deleted and search.
   *
   * @param deleted deleted status
   * @param search  search
   * @return list of AlertPriorityConfigEntity
   */
  List<AlertGroupConfigEntity> findAllByDeletedAndSearch(Boolean deleted, String search);
}
