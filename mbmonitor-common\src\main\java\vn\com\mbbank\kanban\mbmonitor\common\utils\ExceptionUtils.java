package vn.com.mbbank.kanban.mbmonitor.common.utils;

import com.jayway.jsonpath.JsonPath;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.HttpClientErrorException;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 24/7/2025
 */
@Slf4j
public class ExceptionUtils {

  /**
   * Parse a remote REST API error JSON and return a BusinessRuntimeException
   * with extracted errorCode, errorDescription, status.
   *
   * @param ex catch HttpClientErrorException
   */
  public static void fromHttpClientError(HttpClientErrorException ex) throws BusinessException {
    String responseBody = ex.getResponseBodyAsString();

    String code;
    try {
      code = JsonPath.read(responseBody, "$.errorCode").toString();
    } catch (Exception e) {
      log.error("Failed to parse errorCode from body: {}", responseBody, e);
      throw new BusinessException(ErrorCode.EXECUTION_RUN_FAILED);
    }

    ErrorCode matchedError = Arrays.stream(ErrorCode.values())
            .filter(e -> e.getCode().equals(code))
            .findFirst()
            .orElse(ErrorCode.EXECUTION_RUN_FAILED);
    throw new BusinessException(matchedError);
  }
}
