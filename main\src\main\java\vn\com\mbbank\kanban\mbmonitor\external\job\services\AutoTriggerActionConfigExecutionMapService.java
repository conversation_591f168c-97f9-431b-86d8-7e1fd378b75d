package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;

/**
 * interface logic AutoTriggerActionConfigExecutionMapService.
 */
public interface AutoTriggerActionConfigExecutionMapService
        extends BaseService<AutoTriggerActionConfigExecutionMapEntity, String> {

  /**
   * find all AutoTriggerActionConfigExecutionMapEntity.
   *
   * @param autoTriggerActionConfigId AutoTriggerActionConfigId
   * @return list of AutoTriggerActionConfigExecutionMapEntity
   */
  List<AutoTriggerActionConfigExecutionMapEntity> findAllByAutoTriggerActionConfigId(String autoTriggerActionConfigId);

}
