package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyCollection;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.EmailConfigRepository;

@ExtendWith(MockitoExtension.class)
public class EmailConfigServiceImplTest {

  @Mock
  private EmailConfigRepository emailConfigRepository;

  @InjectMocks
  private EmailConfigServiceImpl emailConfigService;

  private static final EmailProtocolTypeEnum PROTOCOL_TYPE = EmailProtocolTypeEnum.SMTP;
  private static final EmailConfigEntity EMAIL_CONFIG_ENTITY = new EmailConfigEntity();
  private static final List<EmailConfigEntity> EMAIL_CONFIG_LIST = Arrays.asList(EMAIL_CONFIG_ENTITY);

  @BeforeEach
  public void setUp() {
    // Setup mock data if needed
    EMAIL_CONFIG_ENTITY.setId(1L);  // Example setup, add more fields as necessary
    EMAIL_CONFIG_ENTITY.setProtocolType(PROTOCOL_TYPE);
  }

  @Test
  public void getRepository_test() {
    // Verifying the repository method returns the correct repository
    JpaCommonRepository<EmailConfigEntity, Long> repository = emailConfigService.getRepository();
    assertNotNull(repository, "Repository should not be null");
  }

  @Test
  public void findAllByProtocolTypeIn_success() {
    // Mocking the behavior of the repository
    when(emailConfigRepository.findAllByProtocolTypeInAndIntervalTimeIsNotNull(anyCollection())).thenReturn(
        EMAIL_CONFIG_LIST);

    // Call the method
    Collection<EmailProtocolTypeEnum> protocolTypes = Arrays.asList(PROTOCOL_TYPE);
    List<EmailConfigEntity> result = emailConfigService.findAllByProtocolTypeIn(protocolTypes);

    // Verify the result
    assertNotNull(result, "The result should not be null");
    assertEquals(1, result.size(), "The result list should contain one item");
    assertEquals(PROTOCOL_TYPE, result.get(0).getProtocolType(), "The protocol type should match");

    // Verify the repository method was called
    verify(emailConfigRepository, times(1)).findAllByProtocolTypeInAndIntervalTimeIsNotNull(protocolTypes);
  }

  @Test
  public void findAllByProtocolTypeIn_emptyResult() {
    // Mocking the behavior of the repository to return an empty list
    when(emailConfigRepository.findAllByProtocolTypeInAndIntervalTimeIsNotNull(anyCollection())).thenReturn(
        Arrays.asList());

    // Call the method
    Collection<EmailProtocolTypeEnum> protocolTypes = Arrays.asList(PROTOCOL_TYPE);
    List<EmailConfigEntity> result = emailConfigService.findAllByProtocolTypeIn(protocolTypes);

    // Verify the result
    assertNotNull(result, "The result should not be null");
    assertTrue(result.isEmpty(), "The result list should be empty");

    // Verify the repository method was called
    verify(emailConfigRepository, times(1)).findAllByProtocolTypeInAndIntervalTimeIsNotNull(protocolTypes);
  }

  @Test
  public void findAllByProtocolTypeIn_multipleProtocols() {
    // Mocking the behavior of the repository to return multiple configurations
    EmailConfigEntity emailConfig2 = new EmailConfigEntity();
    emailConfig2.setId(2L);
    emailConfig2.setProtocolType(EmailProtocolTypeEnum.IMAP);
    List<EmailConfigEntity> emailConfigList = Arrays.asList(EMAIL_CONFIG_ENTITY, emailConfig2);
    when(emailConfigRepository.findAllByProtocolTypeInAndIntervalTimeIsNotNull(anyCollection())).thenReturn(
        emailConfigList);

    // Call the method with a collection of multiple protocol types
    Collection<EmailProtocolTypeEnum> protocolTypes = Arrays.asList(PROTOCOL_TYPE, EmailProtocolTypeEnum.IMAP);
    List<EmailConfigEntity> result = emailConfigService.findAllByProtocolTypeIn(protocolTypes);

    // Verify the result
    assertNotNull(result, "The result should not be null");
    assertEquals(2, result.size(), "The result list should contain two items");
    assertTrue(result.stream().anyMatch(e -> e.getProtocolType() == PROTOCOL_TYPE),
        "List should contain SMTP protocol");
    assertTrue(result.stream().anyMatch(e -> e.getProtocolType() == EmailProtocolTypeEnum.IMAP),
        "List should contain POP3 protocol");

    // Verify the repository method was called
    verify(emailConfigRepository, times(1)).findAllByProtocolTypeInAndIntervalTimeIsNotNull(protocolTypes);
  }
}
