package vn.com.mbbank.kanban.mbmonitor.external.job.configs.kafka;

import com.alibaba.fastjson2.TypeReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.internals.KafkaCompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.configs.configurations.KafkaConfig;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;

/**
 * kafka Consumer config.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 * @updated_date 6/15/2025 (updated with improvement suggestions)
 */
@Component
public class KafkaConsumer {
  private static final Logger logger = LoggerFactory.getLogger(KafkaConsumer.class);

  // Registry of consumer services mapped by message type
  private final Map<KafkaTypeEnum, CommonBaseConsumerService> serviceConsumers = new HashMap<>();

  // Spring-managed task executor for asynchronous processing
  @Autowired
  @Qualifier(BeanNameConstants.COMMON_TASK_EXECUTOR)
  private TaskExecutor commonTaskExecutor;

  // Retry template for handling failed message processing
  private RetryTemplate retryTemplate;

  // Kafka configuration properties
  private final KafkaConfig kafkaConfig;

  @Value("${kanban.kafka.consumer.execute-logic-timeout-seconds:600}")
  Long kafkaConsumerExecuteLogicTimeoutSeconds;
  /**
   * Constructs the KafkaConsumer with registered service consumers.
   *
   * @param serviceConsumers List of consumer services to handle different message types
   * @param kafkaConfig      kafkaConfig
   */

  @Autowired
  public KafkaConsumer(List<CommonBaseConsumerService> serviceConsumers, KafkaConfig kafkaConfig) {
    this.kafkaConfig = kafkaConfig;
    // Initialize retry mechanism based on configuration
    this.retryTemplate = createRetryTemplate();

    // Register all provided consumer services
    if (!KanbanCommonUtil.isEmpty(serviceConsumers)) {
      for (var consumer : serviceConsumers) {
        this.serviceConsumers.put(consumer.getKafkaType(), consumer);
        logger.info("Registered consumer for message type: {}", consumer.getKafkaType().name());
      }
    }
  }


  /**
   * Primary message consumer for the configured jobs topic.
   *
   * @param record The Kafka consumer record containing the message
   * @param ack    The acknowledgment interface to commit offsets
   */
  @KafkaListener(topics = "${kanban.kafka.topic.jobs}",
      concurrency = "${kanban.kafka.consumer.concurrency}")
  public void consumer(ConsumerRecord<String, String> record, Acknowledgment ack) throws Exception {
    consumerAsync(record, false);
    ack.acknowledge();
  }

  /**
   * Message consumer with dynamically generated group ID.
   *
   * @param record The Kafka consumer record containing the message
   * @param ack    The acknowledgment interface to commit offsets
   */
  @KafkaListener(
      topics = "#{kafkaProperties.topicJob}",
      groupId = "#{T(vn.com.mbbank.kanban.mbmonitor.external.job.configs.kafka.KafkaConsumer).generateGroupId()}"
  )
  public void consumerWithMultipleGroup(ConsumerRecord<String, String> record, Acknowledgment ack)
      throws Exception {
    consumerAsync(record, true);
    ack.acknowledge();
  }

  /**
   * Creates and configures a RetryTemplate based on application properties.
   *
   * @return Configured RetryTemplate instance
   */
  private RetryTemplate createRetryTemplate() {
    // Configure backoff policy (delay between retries)
    FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
    backOffPolicy.setBackOffPeriod(
        ObjectUtils.firstNonNull(kafkaConfig.getConsumer().getRetryBackoff(), 5000L));

    // Configure retry policy (number of attempts)
    SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
    retryPolicy.setMaxAttempts(
        Math.toIntExact(ObjectUtils.firstNonNull(kafkaConfig.getConsumer().getRetry(), 3L)));

    // Assemble the retry template
    RetryTemplate template = new RetryTemplate();
    template.setBackOffPolicy(backOffPolicy);
    template.setRetryPolicy(retryPolicy);
    return template;
  }


  /**
   * Generates a random consumer group ID.
   *
   * @return A unique consumer group ID string
   */
  public static String generateGroupId() {
    return "group-" + UUID.randomUUID();
  }

  /**
   * Core message processing logic executed for each consumed record.
   *
   * @param record          The Kafka record to process
   * @param isMultipleGroup Flag indicating if multiple consumer groups are used
   */
  private void consumerLogic(ConsumerRecord<String, String> record, boolean isMultipleGroup) {
    try {
      // Parse the JSON message payload
      var data = KanbanMapperUtils.jsonToObject(record.value(),
          new TypeReference<BaseKafkaModel<Object>>() {
          });

      // Validate message structure
      if (KanbanCommonUtil.isEmpty(data) || data.getType() == null) {
        logger.warn("Empty message or missing type field");
        return;
      }

      // Get the appropriate consumer service for this message type
      KafkaTypeEnum type = data.getType();
      CommonBaseConsumerService serviceConsumer = serviceConsumers.get(type);

      if (serviceConsumer == null) {
        logger.warn("No handler found for message type: {}", type);
        return;
      }

      logger.info("Processing message type {} with data: {}", type, record.value());

      // Verify group compatibility
      if (!Objects.equals(isMultipleGroup, serviceConsumer.isKafkaMultipleGroup())) {
        logger.debug("Skipping message as group type mismatch");
        return;
      }

      // Process with retry if configured
      if (serviceConsumer.isKafkaRetry()) {
        processWithRetry(serviceConsumer, data);
      } else {
        serviceConsumer.kafkaExecute(data);
      }
    } catch (Exception ex) {
      logger.error("Error processing Kafka message. Topic: {}, Partition: {}, Offset: {}",
          record.topic(), record.partition(), record.offset(), ex);
    }
  }

  /**
   * Processes a message with retry capability.
   *
   * @param service The consumer service to handle the message
   * @param data    The parsed message data
   * @throws Exception If all retry attempts fail
   */
  private void processWithRetry(CommonBaseConsumerService service,
                                BaseKafkaModel<Object> data) throws Exception {
    retryTemplate.execute(ctx -> {
      try {
        service.kafkaExecute(data);
        return null;
      } catch (Exception e) {
        logger.error("Attempt {} failed. Error: {}. Retrying in {} ms",
            ctx.getRetryCount() + 1,
            e.getMessage(), kafkaConfig.getConsumer().getRetryBackoff(), e);
        throw e;
      }
    });
  }

  /**
   * Asynchronously processes a Kafka record.
   *
   * @param record          The Kafka record to process
   * @param isMultipleGroup Flag for multi-group processing
   */
  private void consumerAsync(ConsumerRecord<String, String> record, boolean isMultipleGroup) {
    KafkaCompletableFuture.runAsync(() -> {
      consumerLogic(record, isMultipleGroup);
    }, commonTaskExecutor).orTimeout(kafkaConsumerExecuteLogicTimeoutSeconds,
        TimeUnit.SECONDS)
        .exceptionally(ex -> {
          if (ex instanceof TimeoutException) {
            logger.error(
                "Message processing TIMEOUT after {} seconds. Data {} Topic: {}, Partition: {}, Offset: {}",
                kafkaConsumerExecuteLogicTimeoutSeconds,
                KanbanMapperUtils.objectToObject(record, String.class),
                record.topic(),
                record.partition(),
                record.offset());
          } else {
            logger.error("Unexpected error during message processing", ex);
          }
          return null;
        });
  }
}