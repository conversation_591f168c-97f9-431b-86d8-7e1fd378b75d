package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core;

import jakarta.annotation.PostConstruct;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.RedisLockService;

/**
 * JobConfig for scheduler config.
 */
public abstract class JobConfig implements Job {
  private static final Logger logger = LoggerFactory.getLogger(JobConfig.class);
  @Autowired
  protected RedisLockService redisLockService;
  @Autowired
  private Scheduler scheduler;
  @Autowired
  private JobHistoryService jobHistoryService;

  @Value("${monitor.redis.lock.default.timeout:10000}")
  private Long lockTimeoutMs;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) {
    //save log db
    JobHistoryEntity jobHistoryEntity = new JobHistoryEntity();
    jobHistoryEntity.setStartDate(new Date());
    var jobDetail = jobExecutionContext.getJobDetail();
    String keyLock =
        String.format("%s_%s", jobDetail.getKey().getGroup(), jobDetail.getKey().getName());
    if (redisLockService.tryLock(keyLock, getLockTimeoutMs())) {
      try {
        this.executeJob(jobExecutionContext);
      } catch (BusinessException
               |
               SchedulerException
               |
               RuntimeException e) {
        jobHistoryEntity.setEndDate(new Date());
        jobHistoryEntity.setJobId(NumberUtils.toLong(jobDetail.getKey().getName(), 0L));
        jobHistoryEntity.setJobType(getGroupName());
        jobHistoryEntity.setDescription(
            ExceptionUtils.getMessage(e)
                +
                ExceptionUtils.getRootCauseMessage(e)
                +
                ExceptionUtils.getStackTrace(e));
        jobHistoryService.save(jobHistoryEntity);
        logger.error("Job excute error", e);
      } finally {
        redisLockService.unlock(keyLock);
      }
    }
  }

  /**
   * function job.
   *
   * @param context JobExecutionContext.
   */
  public abstract void executeJob(JobExecutionContext context)
      throws BusinessException, SchedulerException, BusinessRuntimeException;

  /**
   * Schedules a job with a fixed interval if it does not already exist.
   *
   * @param key          name of the job.
   * @param intervalTime interval in milliseconds.
   */
  public void scheduleJobIfNotExists(String key, Long intervalTime) throws SchedulerException {
    Trigger trigger = buildTrigger(key, intervalTime);
    scheduleJobIfAbsent(key, trigger);
  }

  /**
   * Schedules a job with a cron expression if it does not already exist.
   *
   * @param key      name of the job.
   * @param cronTime cron expression string.
   */
  public void scheduleJobIfNotExists(String key, String cronTime) throws SchedulerException {
    Trigger trigger = buildTrigger(key, cronTime);
    scheduleJobIfAbsent(key, trigger);
  }

  /**
   * Schedules a job run onetime in feature.
   *
   * @param key           name of the job.
   * @param triggeredDate triggeredDate.
   */
  public void scheduleJobIfNotExists(String key, Date triggeredDate) throws SchedulerException {
    Trigger trigger = buildTrigger(key, triggeredDate);
    scheduleJobIfAbsent(key, trigger);
  }

  /**
   * Internal shared logic to schedule the job if it does not exist.
   *
   * @param key     name of the job.
   * @param trigger the trigger for the job.
   */
  private void scheduleJobIfAbsent(String key, Trigger trigger) throws SchedulerException {
    JobKey jobKey = JobKey.jobKey(key, getGroupName());

    if (scheduler.checkExists(jobKey)) {
      logger.info("Job already exists with job name: " + key + ", job group: " + getGroupName());
      return;
    }

    logger.info("Creating a new job with job name: " + key + ", job group: " + getGroupName());

    JobDetail jobDetail = JobBuilder.newJob(this.getClass())
        .withIdentity(jobKey)
        .build();

    scheduler.scheduleJob(jobDetail, trigger);
    logger.info("Job scheduled successfully with job name: " + key + ", job group: " + getGroupName());
  }


  /**
   * Builds a trigger based on a fixed interval.
   *
   * @param key          name of the job.
   * @param intervalTime interval in milliseconds.
   * @return Trigger object.
   */
  public Trigger buildTrigger(String key, Long intervalTime) {
    return TriggerBuilder.newTrigger()
        .withIdentity(key + "Trigger", getGroupName())
        .startAt(getDefaultStartTime())
        .withSchedule(SimpleScheduleBuilder.simpleSchedule()
            .withIntervalInMilliseconds(intervalTime)
            .repeatForever())
        .build();
  }

  /**
   * Builds a trigger based on a cron expression.
   *
   * @param key           name of the job.
   * @param triggeredDate triggeredDate.
   * @return Trigger object.
   */
  public Trigger buildTrigger(String key, Date triggeredDate) {
    return TriggerBuilder.newTrigger()
        .withIdentity(key + "Trigger", getGroupName())
        .startAt(triggeredDate)
        .withSchedule(SimpleScheduleBuilder.simpleSchedule()
            .withRepeatCount(0))
        .build();
  }

  /**
   * Builds a trigger based call one time in feature.
   *
   * @param key      name of the job.
   * @param cronTime cron expression string.
   * @return Trigger object.
   */
  public Trigger buildTrigger(String key, String cronTime) {
    return TriggerBuilder.newTrigger()
        .withIdentity(key + "Trigger", getGroupName())
        .withSchedule(CronScheduleBuilder.cronSchedule(cronTime))
        .build();
  }


  /**
   * Returns the default start time used in all triggers.
   *
   * @return Date representing the default start time.
   */
  private Date getDefaultStartTime() {
    Calendar startAt = Calendar.getInstance();
    startAt.set(Calendar.YEAR, 2024);
    startAt.set(Calendar.MONTH, Calendar.NOVEMBER);
    startAt.set(Calendar.DAY_OF_MONTH, 20);
    startAt.set(Calendar.HOUR_OF_DAY, 0);
    startAt.set(Calendar.MINUTE, 0);
    startAt.set(Calendar.SECOND, 0);
    startAt.set(Calendar.MILLISECOND, 0);
    return startAt.getTime();
  }


  /**
   * function reschedule job.
   *
   * @param key          name of job.
   * @param intervalTime time interval.
   */
  public void rescheduleJob(String key, Long intervalTime)
      throws SchedulerException {
    removeJob(key);
    scheduleJobIfNotExists(key, intervalTime);
  }

  /**
   * function reschedule job.
   *
   * @param key      name of job.
   * @param cronTime cron time.
   */
  public void rescheduleJob(String key, String cronTime)
      throws SchedulerException {
    removeJob(key);
    scheduleJobIfNotExists(key, cronTime);
  }

  /**
   * function reschedule job.
   *
   * @param key           name of job.
   * @param triggeredDate triggeredDate.
   */
  public void rescheduleJob(String key, Date triggeredDate)
      throws SchedulerException {
    removeJob(key);
    scheduleJobIfNotExists(key, triggeredDate);
  }

  /**
   * function remove job.
   *
   * @param key name of job.
   */
  public void removeJob(String key) throws SchedulerException {
    JobKey jobKey = new JobKey(key, getGroupName());
    scheduler.deleteJob(jobKey);
    logger.info("Job was removed successfully job name: " + key + ", job group: " + getGroupName());
  }

  /**
   * Get lock redis timeout.
   *
   * @return config timeout
   */
  public Long getLockTimeoutMs() {
    return lockTimeoutMs;
  }

  /**
   * Init job when start app.
   *
   * @throws SchedulerException ex
   */
  @PostConstruct
  public void initScheduler() throws SchedulerException {
    if (scheduler.getJobGroupNames().contains(getGroupName())) {
      return;
    }
    Map<String, Long> intervalJobs = getMappingJobNameAndIntervalTime();
    Map<String, String> cronJobs = getMappingJobNameAndCronTime();
    Map<String, Date> triggeredDateJobs = getMappingJobNameAndTriggeredDate();
    if (CollectionUtils.isEmpty(intervalJobs) && CollectionUtils.isEmpty(cronJobs)) {
      return;
    }
    for (var entry : intervalJobs.entrySet()) {
      scheduleJobIfNotExists(entry.getKey(), entry.getValue());
    }
    for (var entry : cronJobs.entrySet()) {
      scheduleJobIfNotExists(entry.getKey(), entry.getValue());
    }
    for (var entry : triggeredDateJobs.entrySet()) {
      scheduleJobIfNotExists(entry.getKey(), entry.getValue());
    }
    logger.info("Scheduler initialized successfully.");
  }


  /**
   * List config with key = keyJob and value = interval time.
   *
   * @return Map
   */
  public abstract Map<String, Long> getMappingJobNameAndIntervalTime();

  /**
   * List config with key = keyJob and value = interval time.
   *
   * @return Map
   */
  public Map<String, String> getMappingJobNameAndCronTime() {
    return Map.of();
  }

  /**
   * List config with key = keyJob and value = triggeredDate.
   *
   * @return Map
   */
  public Map<String, Date> getMappingJobNameAndTriggeredDate() {
    return Map.of();
  }

  /**
   * Config group name of job.
   *
   * @return group name
   */
  public abstract String getGroupName();

  /**
   * get redis lock key.
   *
   * @param jobKey jobKey
   * @return lock redis key
   */
  private String getKeyLockRedis(String jobKey) {
    return String.format("%s_%s", getGroupName(), jobKey);
  }

  /**
   * changeConfigJob.
   *
   * @param isUpdate  When true, updates existing config; when false, removes config job by collectId
   * @param isActive  config isActive/inactive
   * @param collectId collectId
   * @param interval  interval
   * @throws SchedulerException ex
   */
  protected void changeConfigJob(boolean isUpdate, boolean isActive, String collectId, Long interval)
      throws SchedulerException {
    if (isUpdate) {
      if (!isActive) {
        removeJob(collectId);
      } else {
        rescheduleJob(collectId, interval);
      }
    }
    if (!isUpdate) {
      removeJob(collectId);
    }
  }

  /**
   * changeConfigJob.
   *
   * @param type      type of kafka job
   * @param collectId collectId
   * @param cronTime  cron time
   * @throws SchedulerException ex
   */
  protected void changeConfigJob(KafkaJobTypeEnum type, String collectId, String cronTime) throws SchedulerException {
    if (KafkaJobTypeEnum.NEW_OR_UPDATE.equals(type)) {
      rescheduleJob(collectId, cronTime);
    } else if (KafkaJobTypeEnum.DELETE.equals(type)) {
      removeJob(collectId);
    }
  }

  /**
   * changeConfigJob.
   *
   * @param type          type of kafka job
   * @param collectId     collectId
   * @param triggeredDate triggeredDate
   * @throws SchedulerException ex
   */
  protected void changeConfigJob(KafkaJobTypeEnum type, String collectId, Date triggeredDate)
      throws SchedulerException {
    if (KafkaJobTypeEnum.NEW_OR_UPDATE.equals(type)) {
      rescheduleJob(collectId, triggeredDate);
    } else if (KafkaJobTypeEnum.DELETE.equals(type)) {
      removeJob(collectId);
    }
  }
}
