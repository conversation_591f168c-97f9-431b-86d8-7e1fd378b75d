package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;

/**
 * interface logic Execution.
 */
public interface ExecutionService extends BaseService<ExecutionEntity, String> {

  /**
   * find executions by ids.
   *
   * @param ids to find ExecutionEntity
   * @return List ExecutionEntity
   */

  List<ExecutionEntity> findAllByIdIn(List<String> ids);

  /**
   * execute request and return response.
   *
   * @param execution Execution
   * @param configName name of config
   */
  void process(ExecutionEntity execution, String configName) throws BusinessException;
}