package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;

/**
 * Repository interface for managing modify alert configuration dependency data in the system.
 */
@Repository
public interface AutoTriggerActionConfigDependencyRepository
    extends JpaCommonRepository<AutoTriggerActionConfigDependencyEntity, String> {

  /**
   * find all AutoTriggerActionConfigDependencyEntity.
   *
   * @param configIds list id of config
   * @return list of AutoTriggerActionConfigDependencyEntity
   */
  List<AutoTriggerActionConfigDependencyEntity> findAllByAutoTriggerActionConfigIdIn(List<String> configIds);
}
