package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FileStorageRepository;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FileStorageServiceImplTest {

  @TempDir
  Path tempDir;
  @Mock
  private FileStorageRepository fileStorageRepository;
  @Mock
  private S3FileService s3FileService;
  @InjectMocks
  private FileStorageServiceImpl fileStorageService;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @TestForUser
  void registerFile_success() throws Exception {
    Path tempFile = Files.createFile(tempDir.resolve("test.txt"));

    when(fileStorageRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    when(s3FileService.headObject(any())).thenReturn(true);
    FileStorageEntity result = fileStorageService.registerFile(
        tempFile.toString(), "JobA", "123");

    assertEquals(tempFile.toString(), result.getPath());
    assertEquals("JobA", result.getDependencyName());
    assertEquals("123", result.getDependencyId());
  }

  @TestForUser
  void registerFile_error_whenFileNotExist() {
    String path = tempDir.resolve("not_found.txt").toString();

    assertThrows(BusinessException.class, () ->
        fileStorageService.registerFile(path, "JobA", "123")
    );
  }

  @TestForUser
  void findByIdIn_success() {
    List<FileStorageEntity> mockList = List.of(new FileStorageEntity());
    when(fileStorageRepository.findByIdIn(List.of(1L, 2L))).thenReturn(mockList);

    List<FileStorageEntity> result = fileStorageService.findByIdIn(List.of(1L, 2L));
    assertEquals(1, result.size());
  }

  @TestForUser
  void findByIdNotInAndDependencyName_success() {
    List<FileStorageEntity> mockList = List.of(new FileStorageEntity());
    when(fileStorageRepository.findAllByIdNotInAndDependencyName(List.of(1L), "JobA"))
        .thenReturn(mockList);

    List<FileStorageEntity> result = fileStorageService.findByIdNotInAndDependencyName(List.of(1L), "JobA");
    assertEquals(1, result.size());
  }

  @Test
  void deleteExpiredFile_success() {
    String folder = "test-folder";
    String fileToDelete = "test-folder/file.txt";
    String expiredFileKey = "test-folder/expired.txt";

    when(s3FileService.headObject(fileToDelete)).thenReturn(true);
    S3Object expiredObject = S3Object.builder()
        .key(expiredFileKey)
        .lastModified(Instant.now().minus(9, ChronoUnit.DAYS))
        .build();

    ListObjectsV2Response response = ListObjectsV2Response.builder()
        .contents(List.of(expiredObject))
        .isTruncated(false)
        .build();
    when(s3FileService.getBucketName()).thenReturn("bucket");
    S3Client mockClient = mock(S3Client.class);
    when(s3FileService.getS3Client()).thenReturn(mockClient);
    when(mockClient.listObjectsV2(any(ListObjectsV2Request.class))).thenReturn(response);

    fileStorageService.deleteExpiredFile(folder, List.of(fileToDelete));

    verify(s3FileService).deleteObjects(any());
    verify(s3FileService).deleteObjects(any());
  }

  @TestForUser
  void deleteExpiredFile_ignoreNotFound() {
    String path = tempDir.resolve("missing.txt").toString();

    assertDoesNotThrow(() ->
        fileStorageService.deleteExpiredFile(tempDir.toString(), List.of(path))
    );
  }

  @TestForUser
  void deleteExpiredFile_keepRecentFile() throws IOException {
    Path recentFile = Files.createFile(tempDir.resolve("recent.txt"));
    recentFile.toFile().setLastModified(System.currentTimeMillis());

    fileStorageService.deleteExpiredFile(tempDir.toString(), List.of());

    assertTrue(Files.exists(recentFile));
  }

  @TestForDev
  void deleteExpiredFile_error_IOExceptionWhendeleteByListPath() {
    String filePathStr = "dummy.txt";
    Path filePath = Paths.get(filePathStr).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.exists(filePath)).thenReturn(true);
      mockedFiles.when(() -> Files.delete(filePath))
          .thenThrow(new IOException("Simulated delete error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile("dummyFolder", List.of(filePathStr))
      );
    }
  }

  @Test
  void deleteExpiredFile_error_IOExceptionWhendeleteByListPath2() {
    String filePathStr = tempDir.resolve("dummy.txt").toString();
    Path filePath = Paths.get(filePathStr).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.exists(filePath)).thenReturn(true);
      mockedFiles.when(() -> Files.delete(filePath))
          .thenThrow(new IOException("Simulated delete error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile("dummyFolder", List.of(filePathStr))
      );
    }
  }

  @Test
  void deleteExpiredFile_error_IOExceptionWhenWalkingDirectory() {
    String folder = tempDir.resolve("dummyFolder").toString();
    Path baseDir = Paths.get(folder).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.walk(baseDir))
          .thenThrow(new IOException("Simulated walk error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile(folder, List.of())
      );
    }
  }

  @TestForDev
  void getRepository_success() {
    assertEquals(fileStorageRepository, fileStorageService.getRepository());
  }
}