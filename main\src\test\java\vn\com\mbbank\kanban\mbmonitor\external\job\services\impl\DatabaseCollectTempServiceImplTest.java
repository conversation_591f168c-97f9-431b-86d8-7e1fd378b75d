package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

import jakarta.persistence.EntityManager;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseCollectTempRepository;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/25/2024
 */
class DatabaseCollectTempServiceImplTest {
  @Mock
  DatabaseCollectTempRepository databaseCollectTempRepository;
  @Mock
  EntityManager entityManager;
  @InjectMocks
  DatabaseCollectTempServiceImpl databaseCollectTempServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getRepository() {
    JpaCommonRepository<DatabaseCollectTempEntity, Long> result =
        databaseCollectTempServiceImpl.getRepository();
    Assertions.assertEquals(databaseCollectTempRepository, result);
  }

  @Test
  void findAllByDatabaseCollectId_success() {
    when(databaseCollectTempRepository.findAllByDatabaseCollectIdOrderByAlertCollectDateDesc(
        anyLong())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));

    List<DatabaseCollectTempEntity> result =
        databaseCollectTempServiceImpl.findAllByDatabaseCollectId(Long.valueOf(1));
    Assertions.assertEquals(1, result.size());
  }

  @Test
  void deleteAllByDatabaseCollectId() {
    when(databaseCollectTempRepository.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));

    Integer result = databaseCollectTempServiceImpl.deleteAllByDatabaseCollectId(Long.valueOf(1));
    Assertions.assertEquals(Integer.valueOf(0), result);
  }


}
