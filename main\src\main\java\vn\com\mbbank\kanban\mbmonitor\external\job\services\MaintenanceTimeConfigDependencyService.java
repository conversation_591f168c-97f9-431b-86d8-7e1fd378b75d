package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;

/**
 * interface logic MaintenanceTimeConfigDependencyService.
 */
public interface MaintenanceTimeConfigDependencyService
    extends BaseService<MaintenanceTimeConfigDependencyEntity, Long> {

  /**
   * find all MaintenanceTimeConfigDependencyEntity.
   *
   * @param maintenanceTimeConfigId MaintenanceTimeConfigId
   * @return list of MaintenanceTimeConfigDependencyEntity
   */
  List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigId(Long maintenanceTimeConfigId);


  /**
   * find all MaintenanceTimeConfigDependencyEntity by id in.
   *
   * @param maintenanceTimeConfigIds list of MaintenanceTimeConfigId
   * @return list of MaintenanceTimeConfigDependencyEntity
   */
  List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigIdIn(List<Long> maintenanceTimeConfigIds);
}
