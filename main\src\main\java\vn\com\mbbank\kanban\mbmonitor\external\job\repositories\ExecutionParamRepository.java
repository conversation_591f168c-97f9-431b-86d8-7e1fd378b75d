package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;

/**
 * Repository table Execution Param.
 */
@Repository
public interface ExecutionParamRepository
    extends JpaCommonRepository<ExecutionParamEntity, String> {

  /**
   * find all by executionId.
   *
   * @param executionId execution id
   * @return number deleted record
   */
  List<ExecutionParamEntity> findAllByExecutionId(String executionId);
}
