package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigDependencyRepository;

@ExtendWith(MockitoExtension.class)
class ModifyAlertConfigDependencyServiceImplTest {

  @Mock
  private ModifyAlertConfigDependencyRepository modifyAlertConfigDependencyRepository;

  @InjectMocks
  private ModifyAlertConfigDependencyServiceImpl modifyAlertConfigDependencyServiceImpl;

  private ModifyAlertConfigDependencyEntity dependencyEntity1;
  private ModifyAlertConfigDependencyEntity dependencyEntity2;

  @BeforeEach
  void setUp() {
    dependencyEntity1 = new ModifyAlertConfigDependencyEntity();
    dependencyEntity1.setId(1L);
    dependencyEntity1.setModifyAlertConfigId(100L);

    dependencyEntity2 = new ModifyAlertConfigDependencyEntity();
    dependencyEntity2.setId(2L);
    dependencyEntity2.setModifyAlertConfigId(100L);
  }

  @Test
  void findAllByModifyAlertConfigId_returnsExpectedList() {
    Long configId = 100L;
    List<ModifyAlertConfigDependencyEntity> expectedList = Arrays.asList(dependencyEntity1, dependencyEntity2);

    when(modifyAlertConfigDependencyRepository.findAllByModifyAlertConfigId(configId))
        .thenReturn(expectedList);

    List<ModifyAlertConfigDependencyEntity> result =
        modifyAlertConfigDependencyServiceImpl.findAllByModifyAlertConfigId(configId);

    assertEquals(expectedList, result);
    verify(modifyAlertConfigDependencyRepository).findAllByModifyAlertConfigId(configId);
  }
  @Test
  void getRepository_success(){
    assertEquals(modifyAlertConfigDependencyRepository,modifyAlertConfigDependencyServiceImpl.getRepository());
  }
  @Test
  void findAllByModifyAlertConfigIdIn_success(){
    when(modifyAlertConfigDependencyRepository.findAllByModifyAlertConfigIdIn(anyList())).thenReturn(List.of(new ModifyAlertConfigDependencyEntity()));
    assertNotNull(modifyAlertConfigDependencyServiceImpl.findAllByModifyAlertConfigIdIn(List.of(1L)));
  }
}
