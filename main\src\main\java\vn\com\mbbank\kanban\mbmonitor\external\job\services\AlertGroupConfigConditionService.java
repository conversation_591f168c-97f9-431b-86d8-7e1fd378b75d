package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;

/**
 * interface logic AlertGroupConfigConditionService.
 */
public interface AlertGroupConfigConditionService extends BaseService<AlertGroupConfigConditionEntity, Long> {

  /**
   * find all AlertGroupConfigConditionEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return list of AlertGroupConfigConditionEntity
   */
  List<AlertGroupConfigConditionEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

  /**
   * delete all by alertGroupConfigId.
   *
   * @param alertGroupConfigId alertGroupConfigId
   */
  void deleteAllByAlertGroupConfigId(Long alertGroupConfigId);
}
