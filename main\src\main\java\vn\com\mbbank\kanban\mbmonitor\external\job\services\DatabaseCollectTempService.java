package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@Service
public interface DatabaseCollectTempService extends BaseService<DatabaseCollectTempEntity, Long> {
  /**
   * find all by database collect id.
   *
   * @param databaseCollectId databaseCollectId
   * @return list DatabaseCollectTempEntity
   */
  List<DatabaseCollectTempEntity> findAllByDatabaseCollectId(Long databaseCollectId);

  /**
   * Delele by DatabaseCollectId.
   *
   * @param databaseCollectId id config
   * @return Total row
   */
  @Transactional
  Integer deleteAllByDatabaseCollectId(Long databaseCollectId);

}
