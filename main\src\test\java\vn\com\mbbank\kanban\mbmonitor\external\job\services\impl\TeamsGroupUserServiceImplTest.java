package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupUserEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsGroupUserRepository;

@ExtendWith(MockitoExtension.class)
/**
 * Generated by K-tool
 * Created date: 2025-05-14
 */
public class TeamsGroupUserServiceImplTest {

  @Mock
  private TeamsGroupUserRepository teamsGroupUserRepository;

  private MockedStatic<KanbanApplicationConfigUtils> mockedConfig;

  @InjectMocks
  @Spy
  private TeamsGroupUserServiceImpl teamsGroupUserServiceImpl;

  @Mock
  Environment environment;


  @BeforeEach
  public void setUp() {
    // Create the static mock
    mockedConfig = Mockito.mockStatic(KanbanApplicationConfigUtils.class);

    // Set up common mocks
    mockedConfig.when(() -> KanbanApplicationConfigUtils.getProperty("spring.jpa.properties.hibernate.jdbc.batch_size"))
        .thenReturn("700");

    mockedConfig.when(() -> KanbanApplicationConfigUtils.getProperty("spring.jpa.properties.hibernate.jdbc.batch_size", "500"))
        .thenReturn("700");

    mockedConfig.when(() -> KanbanApplicationConfigUtils.getProperty("spring.jpa.properties.hibernate.jdbc.batch_size", Integer.class))
        .thenReturn(700);
  }

  @AfterEach
  public void tearDown() {
    // Close the mock to avoid memory leaks
    if (mockedConfig != null) {
      mockedConfig.close();
    }
  }


  @Test
  void deleteAllByTeamsGroupChatIdIn_positiveTest() {
    // [EN] Input: teamsGroupChatIds = ["chatId1", "chatId2"], teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn returns 2
    // [VI] Input: teamsGroupChatIds = ["chatId1", "chatId2"], teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn trả về 2
    // [EN] Expected: Return 2, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn is called with the provided list.
    // [VI] Expected: Trả về 2, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn được gọi với danh sách được cung cấp.
    List<String> teamsGroupChatIds = Arrays.asList("chatId1", "chatId2");
    when(teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds)).thenReturn(2);

    int result = teamsGroupUserServiceImpl.deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);

    assertEquals(2, result);
    verify(teamsGroupUserRepository).deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);
  }

  @Test
  void deleteAllByTeamsGroupChatIdIn_emptyListTest() {
    // [EN] Input: teamsGroupChatIds is empty.
    // [VI] Input: teamsGroupChatIds rỗng.
    // [EN] Expected: Return 0, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn is not called.
    // [VI] Expected: Trả về 0, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn không được gọi.
    List<String> teamsGroupChatIds = new ArrayList<>();

    int result = teamsGroupUserServiceImpl.deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);

    assertEquals(0, result);
  }

  @Test
  void deleteAllByTeamsGroupChatIdIn_nullListTest() {
    // [EN] Input: teamsGroupChatIds is null.
    // [VI] Input: teamsGroupChatIds là null.
    // [EN] Expected: Return 0, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn is not called.
    // [VI] Expected: Trả về 0, teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn không được gọi.
    List<String> teamsGroupChatIds = null;
    // Mock BatchUtils to return 0 directly to avoid NullPointerException inside BatchUtils.
    doReturn(0).when(teamsGroupUserServiceImpl).deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);

    int result = teamsGroupUserServiceImpl.deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);

    assertEquals(0, result);
  }

  @Test
  void getRepository_test() {
    // [EN] Input: No input.
    // [VI] Input: Không có đầu vào.
    // [EN] Expected: Return teamsGroupUserRepository.
    // [VI] Expected: Trả về teamsGroupUserRepository.

    JpaCommonRepository<TeamsGroupUserEntity, String> repository = teamsGroupUserServiceImpl.getRepository();

    assertEquals(teamsGroupUserRepository, repository);
  }

  @Test
  void deleteAllByTeamsGroupChatIdIn_batchProcessingTest() {
    // [EN] Input: A large list of teamsGroupChatIds (e.g., 1000), and the batch size is configured to 500.
    // [VI] Input: Một danh sách lớn các teamsGroupChatIds (ví dụ: 1000), và kích thước batch được cấu hình là 500.
    // [EN] Expected: The list is processed in batches, and deleteAllByTeamsGroupChatIdIn is called multiple times with different batches. The total count is returned.
    // [VI] Expected: Danh sách được xử lý theo batch, và deleteAllByTeamsGroupChatIdIn được gọi nhiều lần với các batch khác nhau. Tổng số lượng được trả về.

    List<String> teamsGroupChatIds = new ArrayList<>();
    for (int i = 0; i < 1000; i++) {
      teamsGroupChatIds.add("chatId" + i);
    }

    when(teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn(any())).thenReturn(10); // Mock to return 10 for each batch

    int result = teamsGroupUserServiceImpl.deleteAllByTeamsGroupChatIdIn(teamsGroupChatIds);

    assertEquals(20, result); // Expect 2 batches * 10 = 20
  }


}