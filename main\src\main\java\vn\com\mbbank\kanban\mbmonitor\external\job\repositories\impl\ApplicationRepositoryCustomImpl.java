package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ApplicationWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.repositories.ApplicationRepositoryQuery;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ApplicationRepositoryCustom;


/**
 * Implement ApplicationRepositoryCustom table APPLICATION.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ApplicationRepositoryCustomImpl implements ApplicationRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;
  ApplicationRepositoryQuery  applicationRepositoryQuery;

  @Override
  public List<ApplicationResponse> findAllByIdIn(List<String> ids) {
    var query = applicationRepositoryQuery.findAllByIdIn(ids);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(),
            ApplicationResponse.class);
  }

  @Override
  public List<ApplicationWithPriorityModel> findApplicationWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    PrepareQuery query = applicationRepositoryQuery.findApplicationWithPriorityByAlertGroupStatus(
        alertGroupStatus);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), ApplicationWithPriorityModel.class);
  }

  @Override
  public ApplicationResponse findApplicationById(String id) {
    var query = applicationRepositoryQuery.findApplicationById(id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), ApplicationResponse.class);
  }

  @Override
  public Page<ApplicationResponse> findAll(ApplicationPaginationRequest applicationPaginationRequest) {
    var query = applicationRepositoryQuery.findAll(applicationPaginationRequest);
    Pageable pageable = PageRequest.of(applicationPaginationRequest.getPage(),
        applicationPaginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQuery(), query.getParams(), ApplicationResponse.class, pageable);
  }

}
