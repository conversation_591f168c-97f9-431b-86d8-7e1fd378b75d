package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.BaseCollectAlertService;


/**
 * Monitor web service.
 *
 * <AUTHOR>
 * @created_date 03/06/2025
 */
public interface MonitorWebService extends BaseService<MonitorWebConfigEntity, String>, BaseCollectAlertService {
  /**
   * Collect database.
   *
   * @param id id collect config
   * @throws BusinessException ex
   */
  @Transactional(rollbackFor = {Exception.class})
  void collect(String id) throws BusinessException;
  
  /**
   * find all monitor web configs are active.
   *
   * @return List MonitorWebConfigEntity is active
   */
  List<MonitorWebConfigEntity> findAllByIsActiveTrue();
}