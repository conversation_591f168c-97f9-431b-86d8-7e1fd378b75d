package vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports;

import java.util.List;

/**
 * A functional interface for fetching batches of data with pagination support.
 *
 * <p>This interface is designed to support batched export operations,
 * allowing the caller to retrieve data incrementally by specifying the
 * starting offset and the maximum number of items to fetch per call.</p>
 *
 * @param <T> the type of data to fetch
 */
@FunctionalInterface
public interface BatchFetcher<T> {

  /**
   * Fetches a batch of data starting from the given offset and limited by the specified size.
   *
   * @param offset the starting index of records to fetch (zero-based)
   * @param limit the maximum number of records to fetch in this batch
   * @return a list of records fetched; an empty list indicates that no more data is available
   */
  List<T> fetch(int offset, int limit);
}
