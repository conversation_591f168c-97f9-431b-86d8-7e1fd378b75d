package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseCollectTempRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectTempService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@AllArgsConstructor
@Service
public class DatabaseCollectTempServiceImpl extends BaseServiceImpl<DatabaseCollectTempEntity, Long>
    implements DatabaseCollectTempService {
  private final DatabaseCollectTempRepository databaseCollectTempRepository;

  @Override
  protected JpaCommonRepository<DatabaseCollectTempEntity, Long> getRepository() {
    return databaseCollectTempRepository;
  }

  @Override
  public List<DatabaseCollectTempEntity> findAllByDatabaseCollectId(Long databaseCollectId) {
    return databaseCollectTempRepository.findAllByDatabaseCollectIdOrderByAlertCollectDateDesc(databaseCollectId);
  }

  @Override
  @Transactional
  public Integer deleteAllByDatabaseCollectId(Long databaseCollectId) {
    return databaseCollectTempRepository.deleteAllByDatabaseCollectId(databaseCollectId);
  }
}
