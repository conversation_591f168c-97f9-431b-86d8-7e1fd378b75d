package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import java.util.stream.Collectors;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.RpaConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.RpaConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MonitorWebService;

/**
 * Monitor web job.
 *
 * <AUTHOR>
 * @created_date 05/05/2025
 */
@Component(JobNameConstants.MONITOR_WEB_CONFIG)
public class MonitorWebJob extends JobConfig implements CommonBaseConsumerService {
  @Autowired
  private MonitorWebService monitorWebService;
  
  @Autowired
  private RpaConfigRepository rpaConfigRepository;
  
  @Value("${monitor.redis.lock.rpa.timeout:10000}")
  private Long lockTimeoutMs;
  
  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException {
    String key = context.getJobDetail().getKey().getName();
    monitorWebService.collect(key);
  }

  @Override
  public Map<String, String> getMappingJobNameAndCronTime() {
    RpaConfigEntity rpaConfig = rpaConfigRepository.findFirstByOrderByCreatedDateDesc().orElse(null);
    if (rpaConfig == null || !rpaConfig.getActive()) {
      return Map.of();
    }
    var lstConfig = monitorWebService.findAllByIsActiveTrue();
    return lstConfig.stream().collect(Collectors.toMap(MonitorWebConfigEntity::getId,
      CronUtils::makeCronTimeByMonitorWebConfigEntity));
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.MONITOR_WEB_CONFIG;
  }

  @Override
  public Long getLockTimeoutMs() {
    return this.lockTimeoutMs;
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    return Map.of();
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    var kafkaJobModel = KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var configId = String.valueOf(kafkaJobModel.configId());
    var cronTime = kafkaJobModel.cronTime();
    changeConfigJob(kafkaJobModel.type(), configId, cronTime);
  }
  
  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.MONITOR_WEB_CONFIG;
  }
  
}