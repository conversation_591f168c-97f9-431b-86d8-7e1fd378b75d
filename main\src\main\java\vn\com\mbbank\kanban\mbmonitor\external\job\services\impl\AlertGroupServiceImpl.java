package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupService;

/**
 * Service Logic AlertGroupServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class AlertGroupServiceImpl extends BaseServiceImpl<AlertGroupEntity, Long>
    implements AlertGroupService {

  private final AlertGroupRepository alertGroupRepository;


  @Override
  protected JpaCommonRepository<AlertGroupEntity, Long> getRepository() {
    return alertGroupRepository;
  }

  @Override
  public Optional<AlertGroupEntity> findByAlertGroupConfigIdAndStatus(Long alertGroupConfigId,
                                                                      AlertGroupStatusEnum status) {
    return alertGroupRepository.findByAlertGroupConfigIdAndStatus(alertGroupConfigId, status);
  }

  @Override
  public List<AlertGroupEntity> findByAlertGroupConfigIdAndMatchValueInAndStatus(Long alertGroupConfigId,
                                                                                 List<String> matchValue,
                                                                                 AlertGroupStatusEnum status) {
    return alertGroupRepository.findByAlertGroupConfigIdAndMatchValueInAndStatus(alertGroupConfigId, matchValue,
        status);
  }

  @Override
  public List<String> findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum alertGroupStatus) {
    return alertGroupRepository.findAlertRecipientByAlertGroupStatus(alertGroupStatus);
  }
}
