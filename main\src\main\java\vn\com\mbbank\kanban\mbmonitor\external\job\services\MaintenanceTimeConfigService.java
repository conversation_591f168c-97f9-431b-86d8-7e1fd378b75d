package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;


/**
 * interface logic AlertGroupConfigService.
 */
public interface MaintenanceTimeConfigService
    extends BaseService<MaintenanceTimeConfigEntity, Long> {

  /**
   * Updates the statuses of alerts that match active maintenance time configurations.
   *
   * @param alerts the list of {@link AlertEntity} objects to evaluate
   * @return the list of {@link AlertBaseModel} objects with updated statuses if maintenance applies
   */
  List<AlertBaseModel> updateAlertsForMaintenance(List<AlertBaseModel> alerts);
}
