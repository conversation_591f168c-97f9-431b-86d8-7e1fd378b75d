package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ServiceWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;

/**
 * Custom Repo table Service.
 */
public interface ServiceRepositoryCustom {
  /**
   * Find service by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list service
   */
  List<ServiceWithPriorityModel> findServiceWithPriorityByAlertGroupStatus(AlertGroupStatusEnum alertGroupStatus);

}
