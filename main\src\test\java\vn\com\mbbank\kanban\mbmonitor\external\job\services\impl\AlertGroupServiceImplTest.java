package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupRepository;

@ExtendWith({MockitoExtension.class})
class AlertGroupServiceImplTest {
  @Mock
  AlertGroupRepository alertGroupRepository;

  @InjectMocks
  AlertGroupServiceImpl alertGroupServiceImpl;

  @Test
  void getRepository_success() {
    JpaCommonRepository<AlertGroupEntity, Long> result =
        alertGroupServiceImpl.getRepository();
    assertEquals(alertGroupRepository, result);
  }

  @Test
  void findByAlertGroupConfigIdAndStatus_success() {
    when(alertGroupRepository.findByAlertGroupConfigIdAndStatus(anyLong(), any(AlertGroupStatusEnum.class))).thenReturn(
        Optional.empty());
    var res = alertGroupServiceImpl.findByAlertGroupConfigIdAndStatus(1L, AlertGroupStatusEnum.NEW);
    assertEquals(false, res.isPresent());
  }

  @Test
  void findByAlertGroupConfigIdAndMatchValueInAndStatus_success() {
    when(alertGroupRepository.findByAlertGroupConfigIdAndMatchValueInAndStatus(anyLong(), anyList(),
        any(AlertGroupStatusEnum.class))).thenReturn(List.of());
    var res = alertGroupServiceImpl.findByAlertGroupConfigIdAndMatchValueInAndStatus(1L, List.of("a"),
        AlertGroupStatusEnum.NEW);
    assertEquals(0, res.size());
  }

  @Test
  void findAlertRecipientByAlertGroupStatus_success() {
    List<String> recipients = Collections.emptyList();
    when(alertGroupRepository.findAlertRecipientByAlertGroupStatus(any(AlertGroupStatusEnum.class))).thenReturn(
        recipients);

    var result = alertGroupServiceImpl.findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum.CLOSE);

    assertEquals(0, result.size());
    verify(alertGroupRepository).findAlertRecipientByAlertGroupStatus(any(AlertGroupStatusEnum.class));
  }

}