package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.mockito.ArgumentMatchers.anyLong;

import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MaintenanceTimeConfigDependencyRepository;

@ExtendWith({MockitoExtension.class})
class MaintenanceTimeConfigDependencyServiceImplTest {

  @Mock
  MaintenanceTimeConfigDependencyRepository maintenanceTimeConfigDependencyRepository;


  @InjectMocks
  MaintenanceTimeConfigDependencyServiceImpl maintenanceTimeConfigDependencyServiceImpl;

  @Test
  void getRepository_success() {
    JpaCommonRepository<MaintenanceTimeConfigDependencyEntity, Long> result =
        maintenanceTimeConfigDependencyServiceImpl.getRepository();
    Assertions.assertEquals(maintenanceTimeConfigDependencyRepository, result);
  }

  @Test
  void findAllByMaintenanceTimeConfigId_success() {
    Mockito.when(maintenanceTimeConfigDependencyRepository.findAllByMaintenanceTimeConfigId(anyLong()))
        .thenReturn(List.of());
    var res = maintenanceTimeConfigDependencyServiceImpl.findAllByMaintenanceTimeConfigId(1L);
    Assertions.assertEquals(0, res.size());
  }

}
