package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.BatchUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsGroupUserRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupUserService;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:19:36
 */
@Service
@RequiredArgsConstructor
public class TeamsGroupUserServiceImpl extends BaseServiceImpl<TeamsGroupUserEntity, String>
    implements TeamsGroupUserService {

  private final TeamsGroupUserRepository teamsGroupUserRepository;

  @Override
  protected JpaCommonRepository<TeamsGroupUserEntity, String> getRepository() {
    return teamsGroupUserRepository;
  }

  @Override
  public int deleteAllByTeamsGroupChatIdIn(List<String> teamsGroupChatIds) {
    return BatchUtils.processBatchesWithNumberResult(
        teamsGroupChatIds,
        batch -> teamsGroupUserRepository.deleteAllByTeamsGroupChatIdIn(batch));
  }
}
