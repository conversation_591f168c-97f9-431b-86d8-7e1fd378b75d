package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Dialog;
import com.microsoft.playwright.Frame;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.RpaConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.FindElementByEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.PlaywrightManager;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MonitorActionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MonitorWebConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.RpaConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.VariableService;

@ExtendWith(MockitoExtension.class)
public class MonitorWebServiceImplTest {

  @Mock
  private MonitorWebConfigRepository monitorWebConfigRepository;
  @Mock
  private MonitorActionRepository monitorActionRepository;
  @Mock
  private RpaConfigRepository rpaConfigRepository;
  @Mock
  private AlertService alertService;
  @Mock
  private CommonRawAlertService commonRawAlertService;
  @Mock
  private MaintenanceTimeConfigService maintenanceTimeConfigService;
  @Mock
  private FilterAlertConfigService filterAlertConfigService;
  @Mock
  private ModifyAlertConfigService modifyAlertConfigService;
  @Mock
  private AlertPriorityConfigService alertPriorityConfigService;
  @Mock
  private PlaywrightManager playwrightManager;
  @Mock
  private BrowserContext browserContext;
  @Mock
  private Page page;
  @Mock
  private Frame frame;
  @InjectMocks
  private MonitorWebServiceImpl monitorWebService;
  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private VariableService variableService;
  
  @TestForDev
  void getRepository_success() {
    JpaCommonRepository<MonitorWebConfigEntity, String> repository = monitorWebService.getRepository();
    assertEquals(monitorWebConfigRepository, repository);
  }
  
  @Test
  void collect_RpaConfigNotFoundOrInactive_DoesNothing() throws BusinessException {
    // Arrange
    when(rpaConfigRepository.findFirstByOrderByCreatedDateDesc()).thenReturn(Optional.empty());
    
    // Act
    monitorWebService.collect("id1");
    
    // Assert
    verify(monitorWebConfigRepository, never()).findById(any());
  }
  
  @Test
  void collect_MonitorWebConfigNotFoundOrInactive_DoesNothing() throws BusinessException {
    // Arrange
    RpaConfigEntity rpaConfig = new RpaConfigEntity();
    rpaConfig.setActive(true);
    when(rpaConfigRepository.findFirstByOrderByCreatedDateDesc()).thenReturn(Optional.of(rpaConfig));
    when(monitorWebConfigRepository.findById("id1")).thenReturn(Optional.empty());
    
    // Act
    monitorWebService.collect("id1");
    
    // Assert
    verify(commonRawAlertService, never()).createRawData(any(), any());
  }
  
  @Test
  void collect_SuccessfulExecution_NoAlertCollected() throws Exception {
    // Arrange
    RpaConfigEntity rpaConfig = new RpaConfigEntity();
    rpaConfig.setActive(true);
    rpaConfig.setInterval(1);
    rpaConfig.setNumberOfRetry(0);
    
    MonitorWebConfigEntity webConfig = new MonitorWebConfigEntity();
    webConfig.setId("id1");
    webConfig.setActive(true);
    webConfig.setMonitorType(MonitorTypeEnum.LOGIN);
    webConfig.setWebUrl("https://test.com");
    webConfig.setTimeout(10);
    
    when(rpaConfigRepository.findFirstByOrderByCreatedDateDesc()).thenReturn(Optional.of(rpaConfig));
    when(monitorWebConfigRepository.findById("id1")).thenReturn(Optional.of(webConfig));
    when(playwrightManager.createPageWithCustomBrowser(any())).thenReturn(browserContext);
    when(browserContext.newPage()).thenReturn(page);
    
    // Act
    monitorWebService.collect("id1");
    
    // Assert
    verify(commonRawAlertService, never()).createRawData(any(), any());
  }
  
  @Test
  void collect_FailedExecution_AlertCollected() throws Exception {
    // Arrange
    RpaConfigEntity rpaConfig = createRpaConfigEntity();
    MonitorWebConfigEntity webConfig = createMonitorWebConfigEntity();
    List<MonitorActionEntity> authActions = List.of(
      createMonitorActionEntity("482L", "G66", ActionTypeEnum.SEND_KEY, "{{username}}", 1),
      createMonitorActionEntity("483L", "G66", ActionTypeEnum.SEND_KEY, "test", 2),
      createMonitorActionEntity("484L", "G66", ActionTypeEnum.CLICK, null, 3));
    List<MonitorActionEntity> actions = createSampleActions();
    VariableEntity variableEntity = new VariableEntity();
    variableEntity.setId("id");
    variableEntity.setName("username");
    variableEntity.setHidden(false);
    variableEntity.setValue("value");
    List<VariableEntity> variableEntities = List.of(variableEntity);
    
    AlertPriorityConfigEntity priority = new AlertPriorityConfigEntity();
    priority.setName("High");
    
    when(rpaConfigRepository.findFirstByOrderByCreatedDateDesc()).thenReturn(Optional.of(rpaConfig));
    when(monitorWebConfigRepository.findById("id1")).thenReturn(Optional.of(webConfig));
    when(monitorActionRepository.findByActionIdOrderByOrdersAsc(webConfig.getAuthActionId())).thenReturn(authActions);
    when(monitorActionRepository.findByActionIdOrderByOrdersAsc(webConfig.getActionId())).thenReturn(actions);
    when(variableService.findAllByNameIn(any())).thenReturn(variableEntities);
    
    when(playwrightManager.createPageWithCustomBrowser(any())).thenReturn(browserContext);
    when(browserContext.newPage()).thenReturn(page);
    when(alertPriorityConfigService.findById(any())).thenReturn(priority);
    
    Locator locator = mock(Locator.class);
    when(page.mainFrame()).thenReturn(frame);
    when(frame.locator(any())).thenReturn(locator);
    
    AlertBaseModel alertBase = new AlertBaseModel();
    alertBase.setContentRaw("Alert: Test error");
    alertBase.setRecipientRaw("<EMAIL>");
    alertBase.setServiceNameRaw("service1");
    alertBase.setApplicationNameRaw("app1");
    alertBase.setStatus(AlertStatusEnum.NEW);
    alertBase.setSource(AlertSourceTypeEnum.MONITOR_WEB);
    
    when(commonRawAlertService.createRawData(any(), any()))
      .thenReturn(List.of(alertBase));
    
    // Act
    monitorWebService.collect("id1");
    
    // Assert
    ArgumentCaptor<List<AlertBaseModel>> alertCaptor = ArgumentCaptor.forClass(List.class);
    verify(commonRawAlertService).createRawData(alertCaptor.capture(), eq(AlertSourceTypeEnum.MONITOR_WEB));
    
    AlertBaseModel alert = alertCaptor.getValue().get(0);
    assertEquals("testContent", alert.getContent());
    assertEquals("<EMAIL>", alert.getRecipient());
    assertEquals("service1", alert.getServiceId());
    assertEquals("app1", alert.getApplicationId());
    assertEquals(AlertStatusEnum.NEW, alert.getStatus());
    assertEquals(AlertSourceTypeEnum.MONITOR_WEB, alert.getSource());
  }
  
  
  @Test
  void findAllByIsActiveTrue_Success() {
    // Arrange
    MonitorWebConfigEntity config = new MonitorWebConfigEntity();
    when(monitorWebConfigRepository.findAllByActiveTrue()).thenReturn(List.of(config));
    
    // Act
    List<MonitorWebConfigEntity> result = monitorWebService.findAllByIsActiveTrue();
    
    // Assert
    assertEquals(1, result.size());
    assertEquals(config, result.get(0));
  }
  
  @Test
  void filterAlerts_Success() {
    // Arrange
    AlertBaseModel alert = new AlertBaseModel();
    when(filterAlertConfigService.updateAlertsForFilter(any())).thenReturn(List.of(alert));
    
    // Act
    List<AlertBaseModel> result = monitorWebService.filterAlerts(List.of(alert));
    
    // Assert
    assertEquals(1, result.size());
    assertEquals(alert, result.get(0));
  }
  
  @Test
  void modifyAlerts_Success() {
    // Arrange
    AlertBaseModel alert = new AlertBaseModel();
    when(modifyAlertConfigService.updateAlertsForModify(any())).thenReturn(List.of(alert));
    
    // Act
    List<AlertBaseModel> result = monitorWebService.modifyAlerts(List.of(alert));
    
    // Assert
    assertEquals(1, result.size());
    assertEquals(alert, result.get(0));
  }
  
  @Test
  void maintenanceAlerts_Success() {
    // Arrange
    AlertBaseModel alert = new AlertBaseModel();
    when(maintenanceTimeConfigService.updateAlertsForMaintenance(any())).thenReturn(List.of(alert));
    
    // Act
    List<AlertBaseModel> result = monitorWebService.maintenanceAlerts(List.of(alert));
    
    // Assert
    assertEquals(1, result.size());
    assertEquals(alert, result.get(0));
  }
  
  @Test
  void saveAlerts_Success() {
    // Arrange
    AlertBaseModel alert = new AlertBaseModel();
    when(alertService.saveAll(any())).thenReturn(
      List.of(new vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity()));
    
    // Act
    List<AlertBaseModel> result = monitorWebService.saveAlerts(List.of(alert));
    
    // Assert
    assertNotNull(result);
    verify(alertService).saveAll(any());
  }
  
  @Test
  void collectFilters_FiltersInvalidAlerts() {
    // Arrange
    AlertBaseModel validAlert = new AlertBaseModel();
    validAlert.setIsValid(true);
    AlertBaseModel invalidAlert = new AlertBaseModel();
    invalidAlert.setIsValid(false);
    
    // Act
    List<AlertBaseModel> result = monitorWebService.collectFilters(Arrays.asList(validAlert, invalidAlert));
    
    // Assert
    assertEquals(1, result.size());
    assertEquals(validAlert, result.get(0));
  }
  
  @Test
  void performAction_Click_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.CLICK);
    action.setFindElementBy(FindElementByEnum.XPATH);
    action.setIdentifier("//button");
    
    Locator locator = mock(Locator.class);
    when(frame.locator("xpath=//button")).thenReturn(locator);
    when(page.mainFrame()).thenReturn(frame);
    
    // Use reflection to test private method
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(frame.locator("xpath=//button")).click(any());
  }

  @Test
  void performAction_DoubleClick_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.DOUBLE_CLICK);
    action.setFindElementBy(FindElementByEnum.CSS_SELECTOR);
    action.setIdentifier(".btn-submit");
    
    Locator locator = mock(Locator.class);
    when(frame.locator(".btn-submit")).thenReturn(locator);
    when(page.mainFrame()).thenReturn(frame);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(locator).dblclick(any());
  }
  
  @Test
  void performAction_SendKey_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.SEND_KEY);
    action.setFindElementBy(FindElementByEnum.ID);
    action.setIdentifier("username");
    action.setValue("tester");
    
    Locator locator = mock(Locator.class);
    when(frame.locator("#username")).thenReturn(locator);
    when(page.mainFrame()).thenReturn(frame);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(locator).fill(eq("tester"), any());
  }
  
  
  @Test
  void performAction_ClearInput_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.CLEAR_INPUT);
    action.setFindElementBy(FindElementByEnum.NAME);
    action.setIdentifier("email");
    
    Locator locator = mock(Locator.class);
    when(frame.locator("[name=\"email\"]")).thenReturn(locator);
    when(page.mainFrame()).thenReturn(frame);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(locator).fill(eq(""), any());
  }
  
  
  @Test
  void performAction_Hover_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.HOVER);
    action.setFindElementBy(FindElementByEnum.CSS_SELECTOR);
    action.setIdentifier(".menu-item");
    
    Locator locator = mock(Locator.class);
    when(page.mainFrame()).thenReturn(frame);
    when(frame.locator(".menu-item")).thenReturn(locator);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(locator).hover(any());
  }
  
  @Test
  void performAction_Wait_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.WAIT);
    action.setValue("2"); // 2 seconds
    
    // Act
    long startTime = System.currentTimeMillis();
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    long duration = System.currentTimeMillis() - startTime;
    
    // Assert
    verify(page).waitForTimeout(eq(2000.0));
  }
  
  @Test
  void performAction_FindElement_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.FIND_ELEMENT);
    action.setFindElementBy(FindElementByEnum.XPATH);
    action.setIdentifier("//div[@class='content']");
    
    Locator locator = mock(Locator.class);
    when(page.mainFrame()).thenReturn(frame);
    when(frame.locator("xpath=//div[@class='content']")).thenReturn(locator);
    when(locator.isVisible()).thenReturn(true);
    
    // Act
    assertDoesNotThrow(() ->
      ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000)
    );
    
    // Assert
    verify(frame).locator("xpath=//div[@class='content']");
    verify(locator).isVisible();
  }
  
  @Test
  void performAction_FindElement_ElementNotVisible_ThrowsException() throws BusinessException {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.FIND_ELEMENT);
    action.setFindElementBy(FindElementByEnum.XPATH);
    action.setIdentifier("//div[@class='not-found']");
    
    // Arrange
    RpaConfigEntity rpaConfig = createRpaConfigEntity();
    MonitorWebConfigEntity webConfig = createMonitorWebConfigEntity();
    webConfig.setMonitorType(MonitorTypeEnum.DISCOVER);
    List<MonitorActionEntity> actions = new ArrayList<>();
    actions.add(action);
    AlertPriorityConfigEntity priority = new AlertPriorityConfigEntity();
    priority.setName("High");
    
    Locator locator = mock(Locator.class);
    when(page.mainFrame()).thenReturn(frame);
    when(frame.locator("xpath=//div[@class='not-found']")).thenReturn(locator);
    when(locator.isVisible()).thenReturn(false);
    
    when(rpaConfigRepository.findFirstByOrderByCreatedDateDesc()).thenReturn(Optional.of(rpaConfig));
    when(monitorWebConfigRepository.findById("id1")).thenReturn(Optional.of(webConfig));
    when(monitorActionRepository.findByActionIdOrderByOrdersAsc(webConfig.getActionId())).thenReturn(actions);
    
    when(playwrightManager.createPageWithCustomBrowser(any())).thenReturn(browserContext);
    when(browserContext.newPage()).thenReturn(page);
    when(alertPriorityConfigService.findById(any())).thenReturn(priority);
    
    
    AlertBaseModel alertBase = new AlertBaseModel();
    alertBase.setContentRaw("Alert: Test error");
    alertBase.setRecipientRaw("<EMAIL>");
    alertBase.setServiceNameRaw("service1");
    alertBase.setApplicationNameRaw("app1");
    alertBase.setStatus(AlertStatusEnum.NEW);
    alertBase.setSource(AlertSourceTypeEnum.MONITOR_WEB);
    
    when(commonRawAlertService.createRawData(any(), any()))
      .thenReturn(List.of(alertBase));
    
    // Act
    monitorWebService.collect("id1");
    
    // Assert
    ArgumentCaptor<List<AlertBaseModel>> alertCaptor = ArgumentCaptor.forClass(List.class);
    verify(commonRawAlertService).createRawData(alertCaptor.capture(), eq(AlertSourceTypeEnum.MONITOR_WEB));
    
    AlertBaseModel alert = alertCaptor.getValue().get(0);
    assertEquals("testContent", alert.getContent());
    assertEquals("<EMAIL>", alert.getRecipient());
    assertEquals("service1", alert.getServiceId());
    assertEquals("app1", alert.getApplicationId());
    assertEquals(AlertStatusEnum.NEW, alert.getStatus());
    assertEquals(AlertSourceTypeEnum.MONITOR_WEB, alert.getSource());
  }
  
  @Test
  void performAction_SelectFromDropdown_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.SELECT_FROM_DROPDOWN);
    action.setFindElementBy(FindElementByEnum.CSS_SELECTOR);
    action.setIdentifier("select#country");
    action.setValue("Vietnam");
    
    Locator locator = mock(Locator.class);
    when(page.mainFrame()).thenReturn(frame);
    when(frame.locator("select#country")).thenReturn(locator);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(locator).selectOption(eq(new String[] {"Vietnam"}), any());
  }
  
  
  @Test
  void performAction_WaitingForElement_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.WAITING_FOR_ELEMENT);
    action.setFindElementBy(FindElementByEnum.ID);
    action.setIdentifier("loading-spinner");
    action.setValue("2");
    
    when(page.mainFrame()).thenReturn(frame);
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    // Assert
    verify(page).waitForSelector(eq("#loading-spinner"), any());
  }
  
  
  @Test
  void performAction_AlertAcceptPrompt_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.ALERT_ACCEPT);
    action.setValue("2");
    
    Dialog mockDialog = mock(Dialog.class);
    when(mockDialog.type()).thenReturn("prompt");
    
    ArgumentCaptor<Consumer<Dialog>> dialogHandlerCaptor = ArgumentCaptor.forClass(Consumer.class);
    
    // Mock page.onceDialog
    doNothing().when(page).onceDialog(dialogHandlerCaptor.capture());
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    dialogHandlerCaptor.getValue().accept(mockDialog);
    
    // Assert
    verify(mockDialog).accept("2");
  }
  
  @Test
  void performAction_AlertAccept_Success() {
    // Arrange
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.ALERT_ACCEPT);
    action.setValue("2");
    
    Dialog mockDialog = mock(Dialog.class);
    when(mockDialog.type()).thenReturn("accept");
    
    ArgumentCaptor<Consumer<Dialog>> dialogHandlerCaptor = ArgumentCaptor.forClass(Consumer.class);
    
    // Mock page.onceDialog
    doNothing().when(page).onceDialog(dialogHandlerCaptor.capture());
    
    // Act
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    dialogHandlerCaptor.getValue().accept(mockDialog);
    
    // Assert
    verify(mockDialog).accept();
  }
  
  @Test
  void performAction_AlertDismiss_Success() {
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.ALERT_DISMISS);
    
    Dialog mockDialog = mock(Dialog.class);
    ArgumentCaptor<Consumer<Dialog>> dialogHandlerCaptor = ArgumentCaptor.forClass(Consumer.class);
    doNothing().when(page).onceDialog(dialogHandlerCaptor.capture());
    
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 1000);
    
    dialogHandlerCaptor.getValue().accept(mockDialog);
    
    verify(mockDialog).dismiss();
  }
  
  
  @Test
  void performAction_GoToUrl_Success() {
    MonitorActionEntity action = new MonitorActionEntity();
    action.setActionType(ActionTypeEnum.GO_TO_URL);
    action.setValue("https://example.com");
    
    ReflectionTestUtils.invokeMethod(monitorWebService, "performAction", action, page, 3000);
    
    ArgumentCaptor<Page.NavigateOptions> optionsCaptor = ArgumentCaptor.forClass(Page.NavigateOptions.class);
    verify(page).navigate(eq("https://example.com"), optionsCaptor.capture());
  }
  
  
  @Test
  void resolveSelector_Success() {
    // Test all selector resolution cases
    
    // XPATH
    MonitorActionEntity actionXpath = new MonitorActionEntity();
    actionXpath.setFindElementBy(FindElementByEnum.XPATH);
    actionXpath.setIdentifier("//div");
    String selectorXpath = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveSelector", actionXpath);
    assertEquals("xpath=//div", selectorXpath);
    
    // CSS_SELECTOR
    MonitorActionEntity actionCss = new MonitorActionEntity();
    actionCss.setFindElementBy(FindElementByEnum.CSS_SELECTOR);
    actionCss.setIdentifier(".container");
    String selectorCss = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveSelector", actionCss);
    assertEquals(".container", selectorCss);
    
    // ID
    MonitorActionEntity actionId = new MonitorActionEntity();
    actionId.setFindElementBy(FindElementByEnum.ID);
    actionId.setIdentifier("main-content");
    String selectorId = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveSelector", actionId);
    assertEquals("#main-content", selectorId);
    
    // NAME
    MonitorActionEntity actionName = new MonitorActionEntity();
    actionName.setFindElementBy(FindElementByEnum.NAME);
    actionName.setIdentifier("username");
    String selectorName = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveSelector", actionName);
    assertEquals("[name=\"username\"]", selectorName);
  }
  
  @Test
  void resolveCurrentPageByActionType_SwitchToPopup_Success() {
    // Arrange
    AtomicReference<Page> mainPageRef = new AtomicReference<>(page);
    when(browserContext.waitForPage(any(), any())).thenReturn(page);
    
    // Act
    Page result = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveCurrentPageByActionType",
      ActionTypeEnum.SWITCH_TO_POPUP, page, browserContext, mainPageRef, 1000);
    
    // Assert
    assertEquals(page, result);
    assertEquals(page, mainPageRef.get());
  }
  
  @Test
  void resolveCurrentPageByActionType_ClosePopup_Success() {
    // Arrange
    Page mainPage = mock(Page.class);
    Page popupPage = mock(Page.class);
    AtomicReference<Page> mainPageRef = new AtomicReference<>(mainPage);
    
    // Act
    Page result = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveCurrentPageByActionType",
      ActionTypeEnum.CLOSE_POPUP, popupPage, browserContext, mainPageRef, 1000);
    
    // Assert
    verify(popupPage).close();
    assertEquals(mainPage, result);
  }
  
  @Test
  void resolveCurrentPageByActionType_BackToMain_Success() {
    // Arrange
    Page mainPage = mock(Page.class);
    Page popupPage = mock(Page.class);
    AtomicReference<Page> mainPageRef = new AtomicReference<>(mainPage);
    
    // Act
    Page result = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveCurrentPageByActionType",
      ActionTypeEnum.BACK_TO_MAIN, popupPage, browserContext, mainPageRef, 1000);
    
    // Assert
    verify(mainPage).bringToFront();
    assertEquals(mainPage, result);
  }
  
  @Test
  void resolveCurrentPageByActionType_DefaultCase() {
    // Arrange
    Page currentPage = mock(Page.class);
    AtomicReference<Page> mainPageRef = new AtomicReference<>(null);
    
    // Act
    Page result = ReflectionTestUtils.invokeMethod(monitorWebService, "resolveCurrentPageByActionType",
      ActionTypeEnum.CLICK, currentPage, browserContext, mainPageRef, 1000);
    
    // Assert
    assertEquals(currentPage, result);
  }
  
  private RpaConfigEntity createRpaConfigEntity() {
    RpaConfigEntity rpaConfig = new RpaConfigEntity();
    rpaConfig.setActive(true);
    rpaConfig.setInterval(1);
    rpaConfig.setNumberOfRetry(3);
    
    return rpaConfig;
  }
  
  private MonitorWebConfigEntity createMonitorWebConfigEntity() {
    MonitorWebConfigEntity webConfig = new MonitorWebConfigEntity();
    webConfig.setId("id1");
    webConfig.setActive(true);
    webConfig.setMonitorType(MonitorTypeEnum.LOGIN);
    webConfig.setWebUrl("https://test.com");
    webConfig.setContent("testContent");
    webConfig.setContact("<EMAIL>");
    webConfig.setServiceId("service1");
    webConfig.setApplicationId("app1");
    webConfig.setAuthActionId("authActionId");
    webConfig.setActionId("actionId");
    webConfig.setTimeout(10);
    
    return webConfig;
  }
  
  private MonitorActionEntity createMonitorActionEntity(String id, String actionId, ActionTypeEnum type, String value,
                                                        int orders) {
    MonitorActionEntity entity = new MonitorActionEntity();
    entity.setId(id);
    entity.setActionId(actionId);
    entity.setActionType(type);
    entity.setFindElementBy(FindElementByEnum.XPATH);
    entity.setValue(value);
    entity.setOrders(orders);
    return entity;
  }
  
  private List<MonitorActionEntity> createSampleActions() {
    List<MonitorActionEntity> actions = new ArrayList<>();
    
    actions.add(createMonitorActionEntity("64L", "test", ActionTypeEnum.CLICK, "1", 1));
    actions.add(createMonitorActionEntity("65L", "test", ActionTypeEnum.DOUBLE_CLICK, "2", 2));
    actions.add(createMonitorActionEntity("66L", "test", ActionTypeEnum.SEND_KEY, "3", 3));
    actions.add(createMonitorActionEntity("67L", "test", ActionTypeEnum.CLEAR_INPUT, "4", 4));
    actions.add(createMonitorActionEntity("68L", "test", ActionTypeEnum.SWITCH_FRAME, "5", 5));
    actions.add(createMonitorActionEntity("69L", "test", ActionTypeEnum.FIND_ELEMENT, "10 move to 6", 6));
    actions.add(createMonitorActionEntity("70L", "test", ActionTypeEnum.HOVER, "6", 7));
    actions.add(createMonitorActionEntity("71L", "test", ActionTypeEnum.SELECT_FROM_DROPDOWN, "7", 8));
    actions.add(createMonitorActionEntity("72L", "test", ActionTypeEnum.WAIT, null, 9));
    actions.add(createMonitorActionEntity("73L", "test", ActionTypeEnum.WAITING_FOR_ELEMENT, "9", 10));
    actions.add(createMonitorActionEntity("74L", "test", ActionTypeEnum.CLICK, "test", 11));
    actions.add(createMonitorActionEntity("75L", "test", ActionTypeEnum.BACK_TO_MAIN, null, 12));
    actions.add(createMonitorActionEntity("76L", "test", ActionTypeEnum.CLOSE_POPUP, null, 13));
    actions.add(createMonitorActionEntity("77L", "test", ActionTypeEnum.SWITCH_TO_POPUP, null, 14));
    actions.add(createMonitorActionEntity("78L", "test", ActionTypeEnum.ALERT_ACCEPT, "TEST", 15));
    actions.add(createMonitorActionEntity("79L", "test", ActionTypeEnum.ALERT_DISMISS, null, 16));
    actions.add(createMonitorActionEntity("80L", "test", ActionTypeEnum.GO_TO_URL, "TEST", 17));
    actions.add(createMonitorActionEntity("75L", "test", ActionTypeEnum.SWITCH_TO_DEFAULT_FRAME, null, 18));
    
    return actions;
  }
  
  public List<MonitorActionEntity> createSampleAuthActions() {
    return List.of(
      createMonitorActionEntity("482L", "G66", ActionTypeEnum.SEND_KEY, "quandary2.os", 1),
      createMonitorActionEntity("483L", "G66", ActionTypeEnum.SEND_KEY, "test", 2),
      createMonitorActionEntity("484L", "G66", ActionTypeEnum.CLICK, null, 3));
  }
}
