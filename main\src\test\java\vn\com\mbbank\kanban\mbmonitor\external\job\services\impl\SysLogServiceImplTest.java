package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysLogEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.ApplicationTest;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.SysLogRepository;

@ExtendWith(MockitoExtension.class)
class SysLogServiceImplTest extends ApplicationTest {

  @Mock
  SysLogRepository sysLogRepository;

  @InjectMocks
  SysLogServiceImpl sysLogService;


  @Test
  void getRepository_success() {
    JpaCommonRepository<SysLogEntity, String> result = sysLogService.getRepository();
    assertEquals(sysLogRepository, result);
  }

  @Test
  void saveLog_failed_caseExisted() {
    var request = new SysLogModel();
    request.setId("123");
    when(sysLogRepository.findById(anyString())).thenReturn(Optional.of(new SysLogEntity()));
    assertThrows(BusinessException.class, () -> sysLogService.saveLog(request));
  }

  @Test
  void saveLog_success() throws BusinessException {
    var request = new SysLogModel();
    request.setId("123");
    when(sysLogRepository.findById(anyString())).thenReturn(Optional.empty());
    when(sysLogRepository.save(any())).thenReturn(new SysLogEntity());
    var res = sysLogService.saveLog(request);
    assertNotNull(res);
  }
}

