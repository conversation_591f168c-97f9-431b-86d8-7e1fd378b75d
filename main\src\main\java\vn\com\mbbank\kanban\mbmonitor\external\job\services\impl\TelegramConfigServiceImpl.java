package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TelegramConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TelegramConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Service
@RequiredArgsConstructor
public class TelegramConfigServiceImpl extends BaseServiceImpl<TelegramConfigEntity, String>
    implements TelegramConfigService {

  private final TelegramConfigRepository telegramConfigRepository;

  @Override
  protected JpaCommonRepository<TelegramConfigEntity, String> getRepository() {
    return telegramConfigRepository;
  }

  @Override
  public TelegramConfigEntity findFirstByType(TelegramConfigTypeEnum type) {
    return telegramConfigRepository.findFirstByType(type).orElse(null);
  }
}
