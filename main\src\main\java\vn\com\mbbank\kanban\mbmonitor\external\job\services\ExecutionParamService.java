package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;

/**
 * interface logic Execution Param.
 */
public interface ExecutionParamService extends BaseService<ExecutionParamEntity, String> {

  /**
   * find all by executionId.
   *
   * @param executionId execution id
   * @return number deleted record
   */
  List<ExecutionParamEntity> findAllByExecutionId(String executionId);
}