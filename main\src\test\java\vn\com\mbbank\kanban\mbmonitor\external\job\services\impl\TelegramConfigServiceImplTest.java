package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import jakarta.persistence.EntityManager;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TelegramConfigRepository;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/25/2025
 */
class TelegramConfigServiceImplTest {
  @Mock
  TelegramConfigRepository telegramConfigRepository;
  @Mock
  EntityManager entityManager;
  @InjectMocks
  TelegramConfigServiceImpl telegramConfigServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<TelegramConfigEntity, String> result =
        telegramConfigServiceImpl.getRepository();
    Assertions.assertEquals(telegramConfigRepository, result);
  }

  @Test
  void findFirstByType_success() {
    when(telegramConfigRepository.findFirstByType(any())).thenReturn(Optional.empty());

    TelegramConfigEntity result = telegramConfigServiceImpl.findFirstByType(
        TelegramConfigTypeEnum.ALERT);
    Assertions.assertEquals(null, result);
  }
}
