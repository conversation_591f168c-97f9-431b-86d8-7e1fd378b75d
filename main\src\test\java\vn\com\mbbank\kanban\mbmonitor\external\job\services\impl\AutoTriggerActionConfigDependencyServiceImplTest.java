package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.List;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigDependencyRepository;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionConfigDependencyServiceImplTest {

    @Mock
    private AutoTriggerActionConfigDependencyRepository autoTriggerActionConfigDependencyRepository;

    @InjectMocks
    private AutoTriggerActionConfigDependencyServiceImpl service;

    private AutoTriggerActionConfigDependencyEntity createTestDependency(String id, String configId, String dependencyId, DependencyTypeEnum type) {
        AutoTriggerActionConfigDependencyEntity dependency = new AutoTriggerActionConfigDependencyEntity();
        dependency.setId(id);
        dependency.setAutoTriggerActionConfigId(configId);
        dependency.setDependencyId(dependencyId);
        dependency.setType(type);
        return dependency;
    }

    @BeforeEach
    void setUp() {
        // Setup any common test data if needed
    }

    @Test
    void testGetRepository_shouldReturnCorrectRepository() {
        // When & Then
        assertEquals(autoTriggerActionConfigDependencyRepository, service.getRepository());
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldReturnDependencies_whenConfigIdsExist() {
        // Given
        List<String> configIds = List.of("config1", "config2");
        
        AutoTriggerActionConfigDependencyEntity dependency1 = createTestDependency(
                "dep1", "config1", "service1", DependencyTypeEnum.SERVICE);
        AutoTriggerActionConfigDependencyEntity dependency2 = createTestDependency(
                "dep2", "config1", "app1", DependencyTypeEnum.APPLICATION);
        AutoTriggerActionConfigDependencyEntity dependency3 = createTestDependency(
                "dep3", "config2", "service2", DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);

        List<AutoTriggerActionConfigDependencyEntity> expectedDependencies = 
                List.of(dependency1, dependency2, dependency3);

        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(expectedDependencies);

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(expectedDependencies, result);
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldReturnEmptyList_whenNoConfigIdsMatch() {
        // Given
        List<String> configIds = List.of("nonexistent1", "nonexistent2");
        
        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldReturnEmptyList_whenConfigIdsListIsEmpty() {
        // Given
        List<String> configIds = Collections.emptyList();
        
        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldHandleNullConfigIds() {
        // Given
        List<String> configIds = null;
        
        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(Collections.emptyList());

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldReturnCorrectDependencyTypes() {
        // Given
        List<String> configIds = List.of("config1");
        
        AutoTriggerActionConfigDependencyEntity serviceDep = createTestDependency(
                "dep1", "config1", "service1", DependencyTypeEnum.SERVICE);
        AutoTriggerActionConfigDependencyEntity appDep = createTestDependency(
                "dep2", "config1", "app1", DependencyTypeEnum.APPLICATION);
        AutoTriggerActionConfigDependencyEntity serviceAllAppDep = createTestDependency(
                "dep3", "config1", "service2", DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);

        List<AutoTriggerActionConfigDependencyEntity> expectedDependencies = 
                List.of(serviceDep, appDep, serviceAllAppDep);

        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(expectedDependencies);

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify each dependency type is present
        assertTrue(result.stream().anyMatch(dep -> dep.getType() == DependencyTypeEnum.SERVICE));
        assertTrue(result.stream().anyMatch(dep -> dep.getType() == DependencyTypeEnum.APPLICATION));
        assertTrue(result.stream().anyMatch(dep -> dep.getType() == DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION));
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldReturnDependenciesForMultipleConfigs() {
        // Given
        List<String> configIds = List.of("config1", "config2", "config3");
        
        AutoTriggerActionConfigDependencyEntity dep1 = createTestDependency(
                "dep1", "config1", "service1", DependencyTypeEnum.SERVICE);
        AutoTriggerActionConfigDependencyEntity dep2 = createTestDependency(
                "dep2", "config2", "app1", DependencyTypeEnum.APPLICATION);
        AutoTriggerActionConfigDependencyEntity dep3 = createTestDependency(
                "dep3", "config3", "service2", DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);

        List<AutoTriggerActionConfigDependencyEntity> expectedDependencies = List.of(dep1, dep2, dep3);

        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds))
                .thenReturn(expectedDependencies);

        // When
        List<AutoTriggerActionConfigDependencyEntity> result = service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify dependencies for each config
        assertTrue(result.stream().anyMatch(dep -> "config1".equals(dep.getAutoTriggerActionConfigId())));
        assertTrue(result.stream().anyMatch(dep -> "config2".equals(dep.getAutoTriggerActionConfigId())));
        assertTrue(result.stream().anyMatch(dep -> "config3".equals(dep.getAutoTriggerActionConfigId())));
        
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(configIds);
    }

    @Test
    void testFindAllByAutoTriggerActionIdIn_shouldPassCorrectParametersToRepository() {
        // Given
        List<String> configIds = List.of("config1", "config2");
        
        when(autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(any()))
                .thenReturn(Collections.emptyList());

        // When
        service.findAllByAutoTriggerActionIdIn(configIds);

        // Then
        verify(autoTriggerActionConfigDependencyRepository, times(1))
                .findAllByAutoTriggerActionConfigIdIn(eq(configIds));
    }
}
