package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ApiInfoRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.AuthenticationApiRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.BodyApiRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;

/**
 * ExecutionResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExecutionScriptRequestMapper extends KanbanBaseMapper<ExecutionScriptRequest, ExecutionEntity> {
  ExecutionScriptRequestMapper INSTANCE = Mappers.getMapper(ExecutionScriptRequestMapper.class);

  /**
   * Maps the provided ExecutionEntity to an ExecutionScriptRequest, sets the executionBy field,
   * and updates the params field if a non-empty list of parameters is provided.
   *
   * @param executionEntity    the ExecutionEntity to be mapped.
   * @param params             the list of ExecuteScriptParamModel parameters to be set in the request.
   * @param executionBy        the identifier of who is executing the request.
   * @param executionApiEntity the ExecutionApiEntity to be mapped.
   * @return the mapped ExecutionScriptRequest populated with provided data.
   */
  default ExecutionScriptRequest map(ExecutionEntity executionEntity, List<ExecuteScriptParamModel> params,
                                     String executionBy, ExecutionApiEntity executionApiEntity) {
    var res = map(executionEntity);
    if (Objects.nonNull(res)) {
      res.setExecutionBy(executionBy);
      if (CollectionUtils.isNotEmpty(params)) {
        res.setParams(params);
      }
    }
    if (Objects.nonNull(executionApiEntity)) {
      ApiInfoRequest apiInfoRequest = new ApiInfoRequest();
      apiInfoRequest.setUrl(executionApiEntity.getUrl());
      apiInfoRequest.setMethod(executionApiEntity.getMethod());
      apiInfoRequest.setHttpVersion(executionApiEntity.getHttpVersion());
      apiInfoRequest.setEnableSsl(Boolean.TRUE.equals(executionApiEntity.getEnableSsl()));

      apiInfoRequest.setHeaders(executionApiEntity.getHeaders());
      apiInfoRequest.setParams(executionApiEntity.getParams());

      BodyApiRequest body = new BodyApiRequest();
      body.setBodyType(executionApiEntity.getBodyType());
      body.setBodyRaw(executionApiEntity.getBodyRaw());
      body.setFormUrlEncoded(executionApiEntity.getFormUrlEncoded());
      body.setContentType(executionApiEntity.getContentType());
      apiInfoRequest.setBody(body);

      AuthenticationApiRequest authentication = new AuthenticationApiRequest();
      authentication.setAuthType(executionApiEntity.getAuthType());
      authentication.setToken(executionApiEntity.getAuthToken());
      authentication.setUsername(executionApiEntity.getUsername());
      authentication.setPassword(executionApiEntity.getPassword());
      apiInfoRequest.setAuthentication(authentication);
      res.setApiInfo(apiInfoRequest);
    }
    return res;
  }
}
