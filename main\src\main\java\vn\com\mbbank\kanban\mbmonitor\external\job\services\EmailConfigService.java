package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.Collection;
import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

/**
 * interface logic email config account.
 */
public interface EmailConfigService extends BaseService<EmailConfigEntity, Long> {


  /**
   * Returns the count of EmailConfigEntity entries that match the provided username
   * and protocol types, excluding the specified ID.
   *
   * @param protocolType the list of protocol types to filter by
   * @return the list of matching EmailConfigEntity entries
   */
  List<EmailConfigEntity> findAllByProtocolTypeIn(Collection<EmailProtocolTypeEnum> protocolType);
}
