package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationEventResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;

/**
 * Service for notification events.
 */
public interface NotificationEventService extends BaseService<NotificationEventEntity, String> {

  /**
   * find all notification event by active true.
   *
   * @return list of notification event.
   */
  List<NotificationEventResponse> findAllByActiveTrue();

  /**
   * push notification.
   *
   * @param notificationId notificationId
   */
  void pushNotification(String notificationId) throws BusinessException;

  /**
   * find all by active true and schedule type.
   *
   * @param scheduleType scheduleType
   * @return list of NotificationEventEntity
   */
  List<NotificationEventEntity> findAllByActiveTrueAndScheduleType(NotificationEventScheduleTypeEnum scheduleType);
}