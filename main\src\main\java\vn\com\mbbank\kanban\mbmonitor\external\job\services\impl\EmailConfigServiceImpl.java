package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.Collection;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.EmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailConfigService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailConfigServiceImpl extends BaseServiceImpl<EmailConfigEntity, Long>
    implements EmailConfigService {
  EmailConfigRepository emailConfigRepository;

  @Override
  protected JpaCommonRepository<EmailConfigEntity, Long> getRepository() {
    return emailConfigRepository;
  }


  @Override
  public List<EmailConfigEntity> findAllByProtocolTypeIn(Collection<EmailProtocolTypeEnum> protocolType) {
    return emailConfigRepository.findAllByProtocolTypeInAndIntervalTimeIsNotNull(protocolType);
  }

}
