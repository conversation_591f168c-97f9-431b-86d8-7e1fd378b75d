package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.model.S3Object;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FileStorageRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;

@Service
@RequiredArgsConstructor
public class FileStorageServiceImpl extends BaseSoftServiceImpl<FileStorageEntity, Long>
    implements FileStorageService {

  private static final Long EXPORT_DATA_FILE_TIMEOUT_MS =  8 * 24 * 60 * 60 * 1000L; //millisecond
  private final FileStorageRepository fileStorageRepository;
  private final S3FileService s3FileService;
  private final Logger logger = LoggerFactory.getLogger(FileStorageServiceImpl.class);

  @Override
  public FileStorageEntity registerFile(String filePath, String dependencyName, String dependencyId)
      throws BusinessException {
    try {
      if (!s3FileService.headObject(filePath)) {
        logger.error("S3 file not found: {}", filePath);
        throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
      }

      FileStorageEntity entity = new FileStorageEntity();
      entity.setPath(filePath);
      entity.setDependencyName(dependencyName);
      entity.setDependencyId(dependencyId);
      return save(entity);
    } catch (Exception e) {
      logger.error("Error registering S3 file: {}", filePath, e);
      throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
    }
  }

  @Override
  public List<FileStorageEntity> findByIdIn(List<Long> ids) {
    return fileStorageRepository.findByIdIn(ids);
  }

  @Override
  public List<FileStorageEntity> findByIdNotInAndDependencyName(List<Long> ids, String dependencyName) {
    return fileStorageRepository.findAllByIdNotInAndDependencyName(ids, dependencyName);
  }

  @Override
  public void deleteExpiredFile(String folder, List<String> filePathsToDelete) {
    // 1. Delete by explicit file paths
    try {
      s3FileService.deleteObjects(filePathsToDelete);
    } catch (Exception e) {
      logger.warn("Failed to delete files from S3: {}", filePathsToDelete, e);
    }

    // 2. Delete expired files in S3 (older than threshold)
    long now = System.currentTimeMillis();
    try {
      var bucket = s3FileService.getBucketName();
      var client = s3FileService.getS3Client();
      var expiredKeys = client.listObjectsV2Paginator(b -> b.bucket(bucket).prefix(folder))
          .stream()
          .flatMap(r -> r.contents().stream())
          .filter(obj -> now - obj.lastModified().toEpochMilli() > EXPORT_DATA_FILE_TIMEOUT_MS)
          .map(S3Object::key)
          .toList();
      if (!expiredKeys.isEmpty()) {
        s3FileService.deleteObjects(expiredKeys);
        logger.info("Deleted {} expired files under folder '{}'", expiredKeys.size(), folder);
      }
    } catch (Exception e) {
      logger.error("Failed to delete expired files under '{}': {}", folder, e.getMessage());
    }
  }


  @Override
  protected BaseSoftRepository<FileStorageEntity, Long> getRepository() {
    return fileStorageRepository;
  }
}
