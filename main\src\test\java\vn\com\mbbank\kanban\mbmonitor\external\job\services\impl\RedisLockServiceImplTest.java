package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;

class RedisLockServiceImplTest {

  @Mock
  private RedisAdapter redisAdapter;

  @Mock
  private RedisTemplate<String, Object> redisTemplate;

  @Mock
  private ValueOperations<String, Object> valueOperations;

  @InjectMocks
  private RedisLockServiceImpl redisLockService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    when(redisAdapter.getRedisTemplate()).thenReturn((RedisTemplate) redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
  }

  @Test
  void tryLockWithDefaultTimeoutLockAcquired() {
    String key = "myLock";
    Long timeout = 10L;
    when(valueOperations.setIfAbsent(eq(key), eq("LOCKED"), eq(timeout), eq(TimeUnit.MILLISECONDS)))
        .thenReturn(Boolean.TRUE);
    boolean result = redisLockService.tryLock(key);
    assertTrue(result);
    verify(valueOperations).setIfAbsent(eq(key), eq("LOCKED"), eq(timeout), eq(TimeUnit.MILLISECONDS));
  }

  @Test
  void tryLockWithDefaultTimeoutLockNotAcquired() {
    String key = "myLock";
    Long timeout = 10L;
    when(valueOperations.setIfAbsent(eq(key), eq("LOCKED"), eq(timeout), eq(TimeUnit.MILLISECONDS)))
        .thenReturn(Boolean.FALSE);
    boolean result = redisLockService.tryLock(key);
    assertFalse(result);
    verify(valueOperations).setIfAbsent(eq(key), eq("LOCKED"), eq(timeout), eq(TimeUnit.MILLISECONDS));
  }

  @Test
  void tryLockWithCustomTimeoutLockAcquired() {
    String key = "myLock";
    Long customTimeout = 5000L;
    when(valueOperations.setIfAbsent(eq(key), eq("LOCKED"), eq(customTimeout), eq(TimeUnit.MILLISECONDS)))
        .thenReturn(Boolean.TRUE);
    boolean result = redisLockService.tryLock(key, customTimeout);
    assertTrue(result);
    verify(valueOperations).setIfAbsent(eq(key), eq("LOCKED"), eq(customTimeout), eq(TimeUnit.MILLISECONDS));
  }

  @Test
  void tryLockWithCustomTimeoutLockNotAcquired() {
    String key = "myLock";
    Long customTimeout = 5000L;
    when(valueOperations.setIfAbsent(eq(key), eq("LOCKED"), eq(customTimeout), eq(TimeUnit.MILLISECONDS)))
        .thenReturn(Boolean.FALSE);
    boolean result = redisLockService.tryLock(key, customTimeout);
    assertFalse(result);
    verify(valueOperations).setIfAbsent(eq(key), eq("LOCKED"), eq(customTimeout), eq(TimeUnit.MILLISECONDS));
  }

  @Test
  void unlockDeletionSuccessful() {
    String key = "myLock";
    when(redisTemplate.delete(eq(key))).thenReturn(Boolean.TRUE);
    redisLockService.unlock(key);
    verify(redisTemplate).delete(eq(key));
  }

  @Test
  void unlockDeletionFails() {
    String key = "myLock";
    when(redisTemplate.delete(eq(key))).thenReturn(Boolean.FALSE);
    redisLockService.unlock(key);
    verify(redisTemplate).delete(eq(key));
  }
}
