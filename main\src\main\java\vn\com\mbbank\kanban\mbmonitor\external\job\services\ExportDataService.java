package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;

/**
 * Service interface for exporting data files.
 */
public interface ExportDataService
    extends BaseService<ExportDataEntity, String>, CommonBaseConsumerService {

  /**
   * Exports alert-related data to a file.
   *
   * @param id      the ID of the export data.
   * @param request the request containing export configuration.
   */
  void exportFile(String id, ExportFileAlertRequest request);

  /**
   * Exports service-related data to a file.
   *
   * @param id      the ID of the export data.
   * @param request the request containing export configuration.
   */
  void exportFile(String id, ExportFileServiceRequest request);

  /**
   * Exports application-related data to a file.
   *
   * @param id      the ID of the export data.
   * @param request the request containing export configuration.
   */
  void exportFile(String id, ExportFileApplicationRequest request);

  /**
   * Deletes expired files.
   */
  void deleteExpiredFile();
}
