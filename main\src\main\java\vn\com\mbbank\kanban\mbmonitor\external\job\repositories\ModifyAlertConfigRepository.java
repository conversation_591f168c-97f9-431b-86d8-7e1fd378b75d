package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;

/**
 * Repository table ModifyAlertConfigEntity.
 */
@Repository
public interface ModifyAlertConfigRepository
    extends JpaCommonRepository<ModifyAlertConfigEntity, Long> {
  /**
   * find all config by  active.
   *
   * @param active active status
   * @return next position.
   */
  List<ModifyAlertConfigEntity> findAllByActive(Boolean active);

  /**
   * find all config by  active.
   *
   * @param active active status
   * @return next position.
   */
  List<ModifyAlertConfigEntity> findAllByActiveOrderByPosition(Boolean active);

}
