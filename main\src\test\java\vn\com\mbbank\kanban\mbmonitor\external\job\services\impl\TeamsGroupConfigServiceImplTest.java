package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsGroupConfigRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
/**
 * Generate by K-tool
 * Create date: 2025-05-09
 */
class TeamsGroupConfigServiceImplTest {

    @Mock
    private TeamsGroupConfigRepository teamsGroupConfigRepository;

    @InjectMocks
    private TeamsGroupConfigServiceImpl teamsGroupConfigServiceImpl;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getRepository_PositiveTest() {
        // Input: No input.
        // Expected: Returns the injected teamsGroupConfigRepository.
        JpaCommonRepository<TeamsGroupConfigEntity, String> repository = teamsGroupConfigServiceImpl.getRepository();
        assertEquals(teamsGroupConfigRepository, repository);
    }

    @Test
    void deleteAllByTeamsConfigId_PositiveTest() {
        // Input: Valid teamsConfigId.
        // Expected: Returns the number of deleted records.
        String teamsConfigId = "test-config-id";
        when(teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId)).thenReturn(5);

        int deletedCount = teamsGroupConfigServiceImpl.deleteAllByTeamsConfigId(teamsConfigId);

        assertEquals(5, deletedCount);
        verify(teamsGroupConfigRepository, times(1)).deleteAllByTeamsConfigId(teamsConfigId);
    }

    @Test
    void deleteAllByTeamsConfigId_ZeroDeletedTest() {
        // Input: Valid teamsConfigId, but no records are deleted.
        // Expected: Returns 0.
        String teamsConfigId = "test-config-id";
        when(teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId)).thenReturn(0);

        int deletedCount = teamsGroupConfigServiceImpl.deleteAllByTeamsConfigId(teamsConfigId);

        assertEquals(0, deletedCount);
        verify(teamsGroupConfigRepository, times(1)).deleteAllByTeamsConfigId(teamsConfigId);
    }

    @Test
    void deleteAllByTeamsConfigId_NegativeTest() {
        // Input: teamsConfigId with no matching records.
        // Expected: Returns 0 as no records were deleted.
        String teamsConfigId = "non-existent-id";
        when(teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId)).thenReturn(0);

        int deletedCount = teamsGroupConfigServiceImpl.deleteAllByTeamsConfigId(teamsConfigId);

        assertEquals(0, deletedCount);
        verify(teamsGroupConfigRepository, times(1)).deleteAllByTeamsConfigId(teamsConfigId);
    }

    @Test
    void deleteAllByTeamsConfigId_EmptyStringTest() {
        // Input: Empty string for teamsConfigId.
        // Expected: Returns the number of deleted records (might be 0 if no records match).
        String teamsConfigId = "";
        when(teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId)).thenReturn(0);

        int deletedCount = teamsGroupConfigServiceImpl.deleteAllByTeamsConfigId(teamsConfigId);

        assertEquals(0, deletedCount);
        verify(teamsGroupConfigRepository, times(1)).deleteAllByTeamsConfigId(teamsConfigId);
    }

    @Test
    void deleteAllByTeamsConfigId_NullInputTest() {
        // Input: Null value for teamsConfigId.
        // Expected: Should handle null input gracefully and return 0 (or throw exception depending on implementation).
        String teamsConfigId = null;
        when(teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId)).thenReturn(0);

        int deletedCount = teamsGroupConfigServiceImpl.deleteAllByTeamsConfigId(teamsConfigId);

        assertEquals(0, deletedCount);
        verify(teamsGroupConfigRepository, times(1)).deleteAllByTeamsConfigId(teamsConfigId);
    }
}