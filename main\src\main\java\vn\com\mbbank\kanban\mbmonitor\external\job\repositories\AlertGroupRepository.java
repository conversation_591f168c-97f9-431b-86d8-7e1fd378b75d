package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;

/**
 * Repository table AlertGroupRepository.
 */
@Repository
public interface AlertGroupRepository extends JpaCommonRepository<AlertGroupEntity, Long>, AlertGroupRepositoryCustom {

  /**
   * find all alert group.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @param status             status
   * @return list of recipient
   */
  Optional<AlertGroupEntity> findByAlertGroupConfigIdAndStatus(Long alertGroupConfigId, AlertGroupStatusEnum status);

  /**
   * find all alert group.
   *
   * @param alertGroupConfigId alertGroupStatus
   * @param matchValue         matchValue
   * @param status             status
   * @return list of alert group
   */
  List<AlertGroupEntity> findByAlertGroupConfigIdAndMatchValueInAndStatus(Long alertGroupConfigId,
                                                                          List<String> matchValue,
                                                                          AlertGroupStatusEnum status);
}
