package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigConditionRepository;

@ExtendWith({MockitoExtension.class})
class AlertGroupConfigConditionServiceImplTest {
  @Mock
  AlertGroupConfigConditionRepository alertGroupConfigConditionRepository;

  @InjectMocks
  AlertGroupConfigConditionServiceImpl alertGroupConfigConditionServiceImpl;

  @Test
  void getRepository_success() {
    JpaCommonRepository<AlertGroupConfigConditionEntity, Long> result =
        alertGroupConfigConditionServiceImpl.getRepository();
    Assertions.assertEquals(alertGroupConfigConditionRepository, result);
  }

  @Test
  void findAllByAlertGroupConfigId_success() {
    Mockito.when(alertGroupConfigConditionRepository.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    var res = alertGroupConfigConditionServiceImpl.findAllByAlertGroupConfigId(1L);
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void deleteAllByAlertGroupConfigId_success() {
    alertGroupConfigConditionServiceImpl.deleteAllByAlertGroupConfigId(1L);
    Mockito.verify(alertGroupConfigConditionRepository).deleteAllByAlertGroupConfigId(Mockito.anyLong());
  }
}
