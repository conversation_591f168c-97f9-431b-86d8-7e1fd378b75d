package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 11:37:47
 */
@Repository
public interface TeamsConfigRepository extends JpaCommonRepository<TeamsConfigEntity, String> {
  /**
   * find first by type.
   *
   * @param type type
   * @return TeamsConfigEntity
   */
  Optional<TeamsConfigEntity> findFirstByType(TeamsConfigTypeEnum type);
}
