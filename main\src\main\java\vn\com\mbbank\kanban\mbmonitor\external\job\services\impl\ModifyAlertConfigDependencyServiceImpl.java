package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigDependencyService;

@Service
@RequiredArgsConstructor
public class ModifyAlertConfigDependencyServiceImpl extends BaseServiceImpl<ModifyAlertConfigDependencyEntity, Long>
    implements ModifyAlertConfigDependencyService {

  private final ModifyAlertConfigDependencyRepository modifyAlertConfigDependencyRepository;

  @Override
  protected JpaCommonRepository<ModifyAlertConfigDependencyEntity, Long> getRepository() {
    return modifyAlertConfigDependencyRepository;
  }


  @Override
  public List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigId(Long alertGroupConfigId) {
    return modifyAlertConfigDependencyRepository.findAllByModifyAlertConfigId(alertGroupConfigId);
  }

  @Override
  public List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigIdIn(List<Long> alertGroupConfigIds) {
    return modifyAlertConfigDependencyRepository.findAllByModifyAlertConfigIdIn(alertGroupConfigIds);
  }
}
