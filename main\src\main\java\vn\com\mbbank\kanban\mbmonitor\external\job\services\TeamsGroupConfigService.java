package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:17:12
 */
public interface TeamsGroupConfigService extends BaseService<TeamsGroupConfigEntity, String> {

  /**
   * delete old group chat.
   *
   * @param teamsConfigId teamsConfigId
   * @return total record
   */
  @Transactional
  int deleteAllByTeamsConfigId(String teamsConfigId);
}
