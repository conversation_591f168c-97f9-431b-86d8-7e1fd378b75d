package vn.com.mbbank.kanban.mbmonitor.external.job.services;


import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ModifyAlertModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;


/**
 * interface logic ModifyAlertConfigService.
 */
public interface ModifyAlertConfigService extends BaseService<ModifyAlertConfigEntity, Long> {

  /**
   * Updates the fields of alerts that match active configurations.
   *
   * @param alerts the list of {@link ModifyAlertModel} objects to evaluate
   * @return the list of {@link AlertEntity} objects if filter applies
   */
  List<AlertBaseModel> updateAlertsForModify(List<AlertBaseModel> alerts);
}
