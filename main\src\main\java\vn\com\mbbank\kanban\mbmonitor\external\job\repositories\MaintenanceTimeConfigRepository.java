package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;

/**
 * Repository table MaintenanceTimeConfigEntity.
 */
@Repository
public interface MaintenanceTimeConfigRepository
    extends JpaCommonRepository<MaintenanceTimeConfigEntity, Long> {
  /**
   * find all alert group config by deleted and active.
   *
   * @param active active status
   * @return next position.
   */
  List<MaintenanceTimeConfigEntity> findAllByActive(Boolean active);

}
