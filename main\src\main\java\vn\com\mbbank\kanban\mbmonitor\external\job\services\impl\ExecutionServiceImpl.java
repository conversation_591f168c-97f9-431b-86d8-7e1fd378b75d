package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalExecutionUrl;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.execution.ExecutionScriptRequestMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.AutoTriggerConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExecutionParamService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.VariableService;

/**
 * Implementation of the ExecutionService providing functionality to manage and execute operations
 * with execution entities, handling execution groups, parameters, variables, and associated configurations.
 * This service is a core component for managing execution workflows and scripts in the system.
 * <p>
 * This class extends BaseServiceImpl and implements the ExecutionService interface,
 * inheriting common service methods and defining custom execution-specific business logic.
 * <p>
 * Key Responsibilities:
 * - CRUD operations for execution entities.
 * - Management of execution groups, parameters, and variables.
 * - Execution of scripts and SQL commands with validation.
 * - Interaction with database connections and configurations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExecutionServiceImpl extends BaseServiceImpl<ExecutionEntity, String>
        implements ExecutionService {
  private final ExecutionRepository executionRepository;
  private final ExecutionParamService executionParamService;
  private final VariableService variableService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final ExecutionScriptRequestMapper executionScriptRequestMapper = ExecutionScriptRequestMapper.INSTANCE;
  private final Logger logger = LoggerFactory.getLogger(this.getClass());
  @Autowired
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_KEYCLOAK_CENTRALIZED)
  RestTemplate restTemplateKeycloakCentralized;

  @Override
  protected JpaCommonRepository<ExecutionEntity, String> getRepository() {
    return executionRepository;
  }

  @Override
  public List<ExecutionEntity> findAllByIdIn(List<String> ids) {
    return executionRepository.findAllByIdIn(ids);
  }

  @Override
  public void process(ExecutionEntity execution, String configName) {
    String executionBy = AutoTriggerConstants.TRIGGER_ACTION_NAME + configName;
    if (!ExecutionTypeEnum.SQL.equals(execution.getType())) {
      try {
        restTemplateKeycloakCentralized.exchange(
                ExternalExecutionUrl.getUrl(ExternalExecutionUrl.EXECUTE_SCRIPT),
                HttpMethod.POST,
                new HttpEntity<>(executionScriptRequestMapper.map(execution, null,
                        executionBy, new ExecuteScriptRequest())),
                new ParameterizedTypeReference<ResponseData<ExecutionScriptResponse>>() {});
      } catch (HttpClientErrorException ex) {
        logger.error("Error when execute", ex);
        if (ex.getMessage().contains(ErrorCode.EXECUTION_PARAM_IS_INVALID.getMessage())) {
          sysLogKafkaProducerService.send(LogActionEnum.DENY_TRIGGER, configName, execution.getName());
        } else {
          sysLogKafkaProducerService.send(LogActionEnum.ERROR_TRIGGER, configName, execution.getName(),
                  ex.getMessage());
        }
      } catch (Exception e) {
        logger.error("Error when do call server execution url{}",
                ExternalExecutionUrl.getUrl(ExternalExecutionUrl.EXECUTE_SCRIPT), e);
        sysLogKafkaProducerService.send(LogActionEnum.ERROR_TRIGGER, configName, execution.getName(),
                e.getMessage());
      }
    } else {
      sysLogKafkaProducerService.send(LogActionEnum.INVALID_TYPE_TRIGGER, configName, execution.getName());
    }
  }

  protected List<ExecuteScriptParamModel> buildParams(ExecutionEntity execution) {
    List<String> names = executionParamService
            .findAllByExecutionId(execution.getId())
            .stream()
            .map(ExecutionParamEntity::getName)
            .toList();

    if (names.isEmpty()) {
      return List.of();
    }
    Map<String, VariableEntity> defaultMap = variableService
            .findAllByNameIn(names)
            .stream()
            .collect(Collectors.toMap(VariableEntity::getName, Function.identity()));
    return names.stream()
            .map(name -> {
              ExecuteScriptParamModel m = new ExecuteScriptParamModel();
              m.setName(name);

              VariableEntity v = defaultMap.get(name);
              if (v != null) {
                m.setHidden(v.isHidden());
                String val = v.getValue();
                m.setValue(v.isHidden() ? KanbanEncryptorUtils.decrypt(val) : val);
              }
              return m;
            })
            .toList();
  }
}
