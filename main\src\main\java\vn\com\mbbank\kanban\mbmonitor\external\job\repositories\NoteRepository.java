package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;

/**
 * Repository table  note.
 */
@Repository
public interface NoteRepository extends JpaCommonRepository<NoteEntity, Long> {

  /**
   * Find comment by alertId.
   *
   * @param alertGroupId alert group id.
   * @return list CommentEntity
   */
  List<NoteEntity> findAllByAlertGroupIdOrderByCreatedDateDesc(Long alertGroupId);

  /**
   * Find comment by alertId.
   *
   * @param alertGroupIds list alert group id.
   * @return list CommentEntity
   */
  List<NoteEntity> findAllByAlertGroupIdInOrderByCreatedDateDesc(List<Long> alertGroupIds);
}
