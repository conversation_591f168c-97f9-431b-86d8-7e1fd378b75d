package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertPriorityConfigRepository;
import java.util.ArrayList;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AlertPriorityConfigServiceImplTest {

  @Mock
  private AlertPriorityConfigRepository alertPriorityConfigRepository;

  @InjectMocks
  @Spy
  private AlertPriorityConfigServiceImpl alertPriorityConfigService;

  private AlertPriorityConfigEntity priorityConfig1;
  private AlertPriorityConfigEntity priorityConfig2;

  @BeforeEach
  public void setUp() {
    priorityConfig1 = new AlertPriorityConfigEntity();
    priorityConfig1.setId(1L);
    priorityConfig1.setPosition(1);

    priorityConfig2 = new AlertPriorityConfigEntity();
    priorityConfig2.setId(2L);
    priorityConfig2.setPosition(2);
  }

  @Test
  public void findAllMatchPriorityConfig_success() {
    List<AlertPriorityConfigEntity> configs = List.of(priorityConfig1, priorityConfig2);
    when(alertPriorityConfigRepository.findAllMatchPriorityConfig(anyString(),
        anyString())).thenReturn(configs);

    List<AlertPriorityConfigEntity> result =
        alertPriorityConfigService.findAllMatchPriorityConfig("HIGH", "Alert Content");

    assertEquals(2, result.size());
    assertEquals(priorityConfig1, result.get(0));
    assertEquals(priorityConfig2, result.get(1));
  }

  @TestForDev
  void findAllNameByIdIn_success_idsEmpty() {
    assertEquals(0, alertPriorityConfigService.findAllNameByIdIn(List.of()).size());
  }

  @TestForUser
  void findAllNameByIdIn_success() {
    var alertPriority = new AlertPriorityConfigEntity();
    alertPriority.setName("1223");
    alertPriority.setId(1L);
    when(alertPriorityConfigRepository.findAllByIdIn(anyList())).thenReturn(List.of(alertPriority));
    var res = alertPriorityConfigService.findAllNameByIdIn(List.of(1L));
    assertEquals(1, res.size());
  }

  @Test
  public void getPriorityConfig_withCustomPriorityConfig_success() {
    List<AlertPriorityConfigEntity> matchedConfigs = List.of(priorityConfig1);
    lenient().when(alertPriorityConfigRepository.findAllMatchPriorityConfig(anyString(),
        anyString())).thenReturn(matchedConfigs);
    lenient().when(alertPriorityConfigService.findById(any()))
        .thenReturn(priorityConfig2);

    Long result = alertPriorityConfigService.getPriorityConfig(2L, "Alert Content");

    assertEquals(1L, result);
  }

  @Test
  public void getPriorityConfig_withoutCustomPriorityConfig_success() {
    List<AlertPriorityConfigEntity> matchedConfigs = List.of(priorityConfig1);
    lenient().when(alertPriorityConfigRepository.findAllMatchPriorityConfig(anyString(),
        anyString())).thenReturn(matchedConfigs);
    lenient().when(alertPriorityConfigService.findById(any())).thenReturn(null);

    Long result = alertPriorityConfigService.getPriorityConfig(3L, "Alert Content");

    assertEquals(1L,
        result); // Assuming priorityConfig1 is the highest priority if custom config not found
  }

  @Test
  public void getPriorityConfig_noMatchingConfigs_returnsDefault() {
    lenient().when(alertPriorityConfigRepository.findAllMatchPriorityConfig(anyString(),
        anyString())).thenReturn(
        new ArrayList<>());
    lenient().when(alertPriorityConfigService.findById(any())).thenReturn(null);

    Long result = alertPriorityConfigService.getPriorityConfig(4L, "Non-matching content");

    assertEquals(-1L, result);
  }
}
