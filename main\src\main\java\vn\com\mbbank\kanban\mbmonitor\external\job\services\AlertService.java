package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.io.IOException;
import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;

/**
 * interface logic alerts.
 */
public interface AlertService extends BaseService<AlertEntity, Long> {
  /**
   * find alerts without group id and status.
   *
   * @param alertGroupId   alertGroupId
   * @param status         status of alert
   * @param numberOfResult numberOfResult
   * @return list of alert entity
   */
  List<AlertEntity> findTopAlertsByAlertGroupIdAndStatus(Long alertGroupId, AlertStatusEnum status,
                                                         int numberOfResult);

  /**
   * find all alert do not in a group by serviceId, applicationId, in and createTime range.
   *
   * @param serviceIds                      serviceIds
   * @param applicationIds                  applicationIds
   * @param alertGroupHandleTriggerInterval createTime range
   * @param status                          status
   * @return StreamingResponseBody A stream of the exported file that can be sent directly to the client.
   */
  List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status);

  /**
   * Executes the logic to export data as a file and returns it ,
   * allowing the file to be streamed directly to the client without loading the entire file into memory.
   *
   * @param request  The request object containing pagination and filter details used to retrieve the data
   *                 that needs to be exported.
   * @param userName user name.
   * @param filePath path of file in NFS.
   * @return file strorage entity.
   */
  FileStorageEntity exportFile(ExportFileAlertRequest request, String userName, String filePath)
      throws IOException, BusinessException;

  /**
   * find primary alert by group ids.
   *
   * @param groupIds for find
   * @return list of alert
   */
  List<AlertEntity> findAlertKeyByGroupIdIn(List<Long> groupIds);
}