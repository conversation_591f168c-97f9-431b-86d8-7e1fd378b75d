//package vn.com.mbbank.kanban.mbmonitor.external.job.services;
//
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.anyLong;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.doThrow;
//import static org.mockito.Mockito.never;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.quartz.SchedulerException;
//import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailIntervalTimeEnum;
//import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.EmailJob;
//import vn.com.mbbank.kanban.mbmonitor.external.job.configs.kafka.KafkaProperties;
//
//class KafkaEmailConsumerServiceTest {
//
//  @Mock
//  private KafkaProperties kafkaProperties;
//  @Mock
//  private EmailJob emailJob;
//  @Mock
//  private EmailConfigService emailConfigService;
//  @InjectMocks
//  private KafkaEmailConsumerService kafkaEmailConsumerService;
//
//  @BeforeEach
//  void setUp() {
//    MockitoAnnotations.openMocks(this);
//  }
//
//  @Test
//  void handleRecord_shouldRescheduleJob_whenKeyMatchesAndValueIsValid() throws Exception {
//    ConsumerRecord<String, String> record =
//        new ConsumerRecord<>("topic", 0, 0L, "expectedKey", "123");
//    when(kafkaProperties.getKey()).thenReturn("expectedKey");
//    when(emailConfigService.findById(123L)).thenReturn(new EmailConfigEntity());
//
//    kafkaEmailConsumerService.handleRecord(record);
//  }
//
//  @Test
//  void handleRecord_shouldNotRescheduleJob_whenKeyDoesNotMatch() throws Exception {
//    ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 0, 0L, "wrongKey", "123");
//    when(kafkaProperties.getKey()).thenReturn("expectedKey");
//
//    kafkaEmailConsumerService.handleRecord(record);
//
//    verify(emailJob, never()).rescheduleJob(anyString(), anyString(), anyLong());
//  }
//
//  @Test
//  void handleRecord_shouldNotRescheduleJob_whenValueIsInvalid() throws Exception {
//    ConsumerRecord<String, String> record =
//        new ConsumerRecord<>("topic", 0, 0L, "expectedKey", "invalidValue");
//    when(kafkaProperties.getKey()).thenReturn("expectedKey");
//
//    kafkaEmailConsumerService.handleRecord(record);
//
//    verify(emailJob, never()).rescheduleJob(anyString(), anyString(), anyLong());
//  }
//
//  @Test
//  void rescheduleJob_shouldHandleSchedulerException() throws SchedulerException {
//    Long configId = 123L;
//    String jobName = "testJob";
//    EmailConfigEntity emailConfig = new EmailConfigEntity();
//    emailConfig.setIntervalTime(CollectEmailIntervalTimeEnum.FIFTEEN_SECONDS);
//    when(emailConfigService.findById(configId)).thenReturn(emailConfig);
//    doThrow(new SchedulerException("SchedulerException")).when(emailJob)
//        .rescheduleJob(anyString(), anyLong());
//    kafkaEmailConsumerService.rescheduleJob(configId, jobName);
//  }
//
//  @Test
//  void rescheduleJob_success() throws SchedulerException {
//    Long configId = 123L;
//    String jobName = "testJob";
//    EmailConfigEntity emailConfig = new EmailConfigEntity();
//    emailConfig.setIntervalTime(CollectEmailIntervalTimeEnum.FIFTEEN_SECONDS);
//    when(emailConfigService.findById(configId)).thenReturn(emailConfig);
//    doNothing().when(emailJob)
//        .rescheduleJob(anyString(), anyString(), anyLong());
//    kafkaEmailConsumerService.rescheduleJob(configId, jobName);
//  }
//
//  @Test
//  void listen_shouldProcessRecord() {
//    ConsumerRecord<String, String> record =
//        new ConsumerRecord<>("topic", 0, 0L, "expectedKey", "123");
//    kafkaEmailConsumerService.listen(record);
//  }
//
//  @Test
//  void generateGroupId_success() {
//    var res = KafkaEmailConsumerService.generateGroupId();
//    assertNotNull(res);
//  }
//}
