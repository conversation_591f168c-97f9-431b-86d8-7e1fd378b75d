package vn.com.mbbank.kanban.mbmonitor.external.job.controllers;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.annotations.SkipAuthentication;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/19/2024
 */
@RestController
@RequestMapping("database-collects")
@AllArgsConstructor
public class DatabaseCollectController {
  /**
   * Trigger collect.
   *
   * @return OK
   */
  @SkipAuthentication
  @GetMapping("trigger")
  public ResponseData<String> collectTrigger() {
    return ResponseUtils.success("OK");
  }

}
