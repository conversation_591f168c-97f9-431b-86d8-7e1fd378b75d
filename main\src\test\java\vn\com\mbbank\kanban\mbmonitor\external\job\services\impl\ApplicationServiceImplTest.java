package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ApplicationWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.ApplicationTest;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ApplicationRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ServiceRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.BatchFetcher;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.ExportFileProcessor;

@ExtendWith({MockitoExtension.class})
class ApplicationServiceImplTest extends ApplicationTest {

  @Mock
  ApplicationRepository applicationRepository;
  @Mock
  ServiceRepository serviceRepository;
  @InjectMocks
  ApplicationServiceImpl applicationServiceImpl;
  @Mock
  ExportFileProcessor fileProcessor;
  @TempDir
  Path tempDir;
  @BeforeEach
  void setUp(){
  }
  @TestForUser
  void exportFile_success() throws IOException, BusinessException {
    ExportFileApplicationRequest request = new ExportFileApplicationRequest();
    request.setNumberOfResults(10);
    ApplicationPaginationRequest paginationRequest = new ApplicationPaginationRequest();
    request.setPaginationRequest(paginationRequest);
    ExportDataModel exportDataModel = new ExportDataModel();
    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
    request.setExportDataModel(exportDataModel);
    String filePathStr = tempDir.resolve("export_success.csv").toString();

    ApplicationResponse resp = new ApplicationResponse();
    resp.setId("1");
    resp.setName("App 1");
    resp.setCreatedDate("2021-01-01T00:00:00");

    List<ApplicationResponse> responses = Collections.singletonList(resp);
    Page<ApplicationResponse> page = new PageImpl<>(responses, PageRequest.of(0, 10), 1);

    when(applicationRepository.findAll(any(ApplicationPaginationRequest.class))).thenReturn(page);

    FileStorageEntity storageEntity = new FileStorageEntity();
    storageEntity.setPath(filePathStr);

    when(fileProcessor.exportFileCommon(any(), any(), any(), any(), any())).thenAnswer(invocation -> {
      BatchFetcher<ApplicationResponse> batchFetcher = invocation.getArgument(4);
      List<ApplicationResponse> fetched = batchFetcher.fetch(0, 10);
      assertEquals(1, fetched.size());
      assertEquals("App 1", fetched.get(0).getName());
      return storageEntity;
    });

    // Act
    FileStorageEntity result = applicationServiceImpl.exportFile(request, "userName", filePathStr);

    // Assert
    assertNotNull(result);
    assertEquals(filePathStr, result.getPath());
  }

  @TestForUser
  void exportFile_success_emptyPage() throws IOException, BusinessException {
    ExportFileApplicationRequest request = new ExportFileApplicationRequest();
    request.setNumberOfResults(10);
    ApplicationPaginationRequest paginationRequest = new ApplicationPaginationRequest();
    request.setPaginationRequest(paginationRequest);
    ExportDataModel exportDataModel = new ExportDataModel();
    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
    request.setExportDataModel(exportDataModel);
    String userName = "testUser";
    String filePathStr = tempDir.resolve("export_empty.csv").toString();
      FileStorageEntity storageEntity = new FileStorageEntity();
      storageEntity.setPath(filePathStr);
      when(fileProcessor.exportFileCommon(any(),any(),any(),any(),any())).thenReturn(storageEntity);
      FileStorageEntity result = applicationServiceImpl.exportFile(request, userName, filePathStr);
      assertNotNull(result);
      assertEquals(filePathStr, result.getPath());
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<ApplicationEntity, String> result = applicationServiceImpl.getRepository();
    assertEquals(result, applicationRepository);
  }

  @Test
  void findFirstByNameIgnoreCaseAndServiceId_success() {
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("1L");
    when(applicationRepository.findFirstByNameIgnoreCaseAndServiceIdAndDeletedFalse(any(),
        any())).thenReturn(
        Optional.of(applicationEntity));
    var result = applicationServiceImpl.findFirstByNameIgnoreCaseAndServiceId(any(), any());
    assertEquals(result.isPresent(), true);

  }

  @Test
  void findApplicationWithPriorityByAlertStatus_success() {
    List<ApplicationWithPriorityModel> applications =
        List.of(new ApplicationWithPriorityModel("1L", "test", "1L",
                AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID, 1),
            new ApplicationWithPriorityModel("1L", "test", "1L",
                AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID, 1));
    when(applicationRepository.findApplicationWithPriorityByAlertGroupStatus(
        any(AlertGroupStatusEnum.class))).thenReturn(
        applications);

    var result = applicationServiceImpl.findApplicationWithPriorityByAlertGroupStatus(
        AlertGroupStatusEnum.CLOSE);

    assertEquals(1, result.size());
    verify(applicationRepository).findApplicationWithPriorityByAlertGroupStatus(
        any(AlertGroupStatusEnum.class));
  }

  @Test
  void findAll_success() {
    when(applicationRepository.findAll(any(ApplicationPaginationRequest.class))).thenReturn(
        Page.empty());
    var res = applicationServiceImpl.findAll(new ApplicationPaginationRequest());
    verify(applicationRepository, times(1)).findAll(any(ApplicationPaginationRequest.class));
    assertEquals(res.getContent().size(), 0);
    assertEquals(res.getTotalElements(), 0);
  }

  @Test
  void findAllByServiceId_success() {
    when(applicationRepository.findAllByServiceIdAndDeleted(any(), anyBoolean())).thenReturn(null);
    var res = applicationServiceImpl.findAllByServiceId("()");
    verify(applicationRepository, times(1)).findAllByServiceIdAndDeleted(any(), anyBoolean());
    assertNull(res);
  }

  @Test
  void findApplicationById_success() {
    when(applicationRepository.findApplicationById(any())).thenReturn(null);
    var res = applicationServiceImpl.findApplicationById("1");
    verify(applicationRepository, times(1)).findApplicationById(any());
    assertNull(res);
  }


  @Test
  void existByNameAndServiceId_success_greater() {
    when(applicationRepository.countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(any(),
        any())).thenReturn(1L);

    var res = applicationServiceImpl.existByNameAndServiceId("", "");
    verify(applicationRepository, times(1)).countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(
        any(), any());
    assertEquals(true, res);
  }


  @Test
  void existByName_success() {
    when(applicationRepository.countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(any(),
        any())).thenReturn(1L);
    var res = applicationServiceImpl.existByNameAndServiceId("1L", "");
    verify(applicationRepository, times(1)).countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(
        any(), any());
    assertEquals(true, res);
  }

  @Test
  void existByNameAndIdAndServiceId_success() {
    when(applicationRepository.countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(any(),
        any(),
        any())).thenReturn(1L);
    var res = applicationServiceImpl.existByIdNotAndNameAndServiceId("1L", "1", "1");
    verify(applicationRepository,
        times(1)).countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(any(), any(),
        any());
    assertEquals(true, res);
  }


  @Test
  void generateId_success() {
    when(applicationRepository.getNextSequenceValue()).thenReturn(1L);
    var res = applicationServiceImpl.generateId();
    assertEquals("A00001", res);
  }


  @Test
  public void genTitleForFileExport_WithSearchKeyword_success() {
    ApplicationPaginationRequest request = new ApplicationPaginationRequest();
    request.setSearch("testKeyword");
    String result = applicationServiceImpl.genTitleForFileExport(request);
    assertEquals(
        "Application - Filter by condition: ApplicationName/Description contain keyword testKeyword.",
        result);
  }
  @Test
  public void genTitleForFileExport_WithNotSearchKeyword_success() {
    ApplicationPaginationRequest request = new ApplicationPaginationRequest();
    String result = applicationServiceImpl.genTitleForFileExport(request);
    assertEquals(
        "Application - Filter by condition: ",
        result);
  }

  @Test
  void findAllNameByIdIn_WithEmptyIds() {
    List<String> actualNames = applicationServiceImpl.findAllNameByIdIn(List.of());
    assertNotNull(actualNames);
    assertTrue(actualNames.isEmpty());
  }


  @Test
  public void findAllNameByIdIn_WithEmptyIds_success() {
    List<String> ids = Collections.emptyList();
    List<String> result = applicationServiceImpl.findAllNameByIdIn(ids);
    assertTrue(result.isEmpty());
    verify(applicationRepository, never()).findAllByIdIn(anyList());
  }

  @Test
  public void findAllNameByIdIn_success() {
    List<String> ids = List.of("1");
     when(applicationRepository.findApplicationNameByIdIn(anyList())).thenReturn(Set.of());
    List<String> result = applicationServiceImpl.findAllNameByIdIn(ids);
    assertTrue(result.isEmpty());
  }

  @Test
  public void findAllByIdIn_WithNonExistentIds_success() {
    List<String> ids = Arrays.asList("4", "5");
    when(applicationRepository.findAllByIdIn(ids)).thenReturn(Collections.emptyList());
    List<ApplicationResponse> result = applicationServiceImpl.findAllByIdIn(ids);
    assertTrue(result.isEmpty());
    verify(applicationRepository, times(1)).findAllByIdIn(ids);
  }

  @Test
  public void findAllByIdIn_WithNonExistentIds_success_caseIdEmpty() {
    List<String> ids = List.of();
    List<ApplicationResponse> result = applicationServiceImpl.findAllByIdIn(ids);
    assertTrue(result.isEmpty());
  }

  @Test
  public void findAllByServiceIdInAndDeleted_success() {
    Mockito.when(applicationRepository.findAllByServiceIdInAndDeleted(Mockito.anyList(),
        Mockito.anyBoolean())).thenReturn(List.of());
    var res = applicationServiceImpl.findAllByServiceIdInAndDeleted(List.of(), false);
    assertNotNull(res);
  }

  @Test
  void findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse_success() {
    when(applicationRepository.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
        any(),
        any(), any())).thenReturn(new ArrayList<>());

    List<ApplicationEntity> result =
        applicationServiceImpl.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            List.of("applicationNameSource"),
            List.of(""), List.of("12"));
    Assertions.assertEquals(0, result.size());
  }
}