package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.MaintenanceTimeConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MaintenanceTimeConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;


/**
 * Service Logic MaintenanceTimeConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class MaintenanceTimeConfigServiceImpl
    extends BaseServiceImpl<MaintenanceTimeConfigEntity, Long>
    implements MaintenanceTimeConfigService {
  private final MaintenanceTimeConfigRepository maintenanceTimeConfigRepository;
  private final MaintenanceTimeConfigDependencyService maintenanceTimeConfigDependencyService;
  private final CustomObjectService customObjectService;
  private final ApplicationService applicationService;

  private final Logger logger = LoggerFactory.getLogger(MaintenanceTimeConfigService.class);
  private final MaintenanceTimeConfigResponseMapper maintenanceTimeConfigModelMapper =
      MaintenanceTimeConfigResponseMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<MaintenanceTimeConfigEntity, Long> getRepository() {
    return maintenanceTimeConfigRepository;
  }

  @Override
  public List<AlertBaseModel> updateAlertsForMaintenance(List<AlertBaseModel> alerts) {
    if (CollectionUtils.isEmpty(alerts)) {
      return new ArrayList<>();
    }
    Date now = new Date();

    List<MaintenanceTimeConfigEntity> configs =
        maintenanceTimeConfigRepository.findAllByActive(true)
            .stream()
            .filter(config -> Objects.nonNull(config.getRuleGroup()) && isEffectiveNow(config, now))
            .toList();

    Map<Long, List<MaintenanceTimeConfigDependencyEntity>> dependenciesMap =
        maintenanceTimeConfigDependencyService
            .findAllByMaintenanceTimeConfigIdIn(
                configs.stream().map(MaintenanceTimeConfigEntity::getId).toList())
            .stream()
            .collect(Collectors.groupingBy(
                MaintenanceTimeConfigDependencyEntity::getMaintenanceTimeConfigId));

    List<MaintenanceTimeConfigResponse> effectiveConfigs = configs.stream()
        .map(config -> this.mapDependenciesConfig(config,
            dependenciesMap.getOrDefault(config.getId(), List.of())))
        .toList();

    if (effectiveConfigs.isEmpty()) {
      return alerts;
    }
    var customObjects = customObjectService.findAllByDeletedIsFalse();
    alerts.parallelStream().forEach(alert -> {
      if (effectiveConfigs.stream().anyMatch(config -> this.isConfigRelatedToAlert(config, alert)
          && config.getRuleGroup()
          .check(CustomObjectUtils.getAlertConditionValueMap(alert, customObjects)))) {
        alert.setStatus(AlertStatusEnum.MAINTENANCE);
      }
    });
    return alerts;
  }

  boolean isEffectiveNow(MaintenanceTimeConfigEntity config, Date now) {
    if (Objects.isNull(config.getType())) {
      return false;
    }
    return switch (config.getType()) {
      case NEXT_TIME, FROM_TIME_TO_TIME -> this.isFromTimeToTimeEffective(config, now);
      case CRON_JOB -> this.isCronJobEffective(config, now);
    };
  }

  MaintenanceTimeConfigResponse mapDependenciesConfig(MaintenanceTimeConfigEntity config,
                                                      List<MaintenanceTimeConfigDependencyEntity> dependencies) {
    var serviceIds = new ArrayList<String>();
    var applicationIds = new ArrayList<String>();
    var serviceIdsWithAllApplication = new ArrayList<String>();
    for (MaintenanceTimeConfigDependencyEntity dependency : dependencies) {
      if (MaintenanceTimeConfigDependencyTypeEnum.APPLICATION.equals(dependency.getType())) {
        applicationIds.add(dependency.getDependencyId());
      } else if (MaintenanceTimeConfigDependencyTypeEnum.SERVICE.equals(dependency.getType())) {
        serviceIds.add(dependency.getDependencyId());
      } else {
        serviceIds.add(dependency.getDependencyId());
        serviceIdsWithAllApplication.add(dependency.getDependencyId());
      }
    }
    var additionalApplicationIds = applicationService
        .findAllByServiceIdInAndDeleted(serviceIdsWithAllApplication, false)
        .stream().map(ApplicationEntity::getId).toList();
    applicationIds.addAll(additionalApplicationIds);
    return maintenanceTimeConfigModelMapper.map(config, serviceIds, applicationIds);
  }

  boolean isConfigRelatedToAlert(MaintenanceTimeConfigResponse config, AlertEntity alert) {
    return config.getServiceIds().contains(alert.getServiceId())
        && config.getApplicationIds().contains(alert.getApplicationId());
  }


  boolean isCronJobEffective(MaintenanceTimeConfigEntity config, Date now) {
    if (KanbanStringUtils.isNullOrEmpty(config.getCronExpression())) {
      return false;
    }
    try {
      CronParser parser =
          new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.UNIX));
      Cron cron = parser.parse(config.getCronExpression());
      cron.validate();
      ExecutionTime executionTime = ExecutionTime.forCron(cron);
      return executionTime.isMatch(now.toInstant().atZone(ZoneId.systemDefault()));
    } catch (Exception e) {
      logger.warn(
          "Invalid cron expression: " + config.getCronExpression() + " Error: " + e.getMessage());
      return false;
    }
  }

  boolean isFromTimeToTimeEffective(MaintenanceTimeConfigEntity config, Date now) {
    if (Objects.isNull(config.getStartTime()) || Objects.isNull(config.getEndTime())) {
      return false;
    }
    return !now.before(config.getStartTime()) && !now.after(config.getEndTime());
  }

}
