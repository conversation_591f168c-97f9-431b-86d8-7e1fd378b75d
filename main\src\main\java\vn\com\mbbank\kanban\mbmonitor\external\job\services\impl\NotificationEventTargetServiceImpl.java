package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventTargetEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.NotificationEventTargetRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NotificationEventTargetService;

/**
 * Implementation of NotificationEventService.
 */
@Service
@RequiredArgsConstructor
public class NotificationEventTargetServiceImpl extends BaseServiceImpl<NotificationEventTargetEntity, String>
    implements NotificationEventTargetService {

  private final NotificationEventTargetRepository notificationEventTargetRepository;


  @Override
  protected JpaCommonRepository<NotificationEventTargetEntity, String> getRepository() {
    return notificationEventTargetRepository;
  }

  @Override
  public List<NotificationEventTargetEntity> findAllByNotificationEventId(String id) {
    return notificationEventTargetRepository.findAllByNotificationEventId(id);
  }

  @Override
  public List<NotificationEventTargetEntity> findAllByNotificationEventIdIn(List<String> ids) {
    return notificationEventTargetRepository.findAllByNotificationEventIdIn(ids);
  }
}