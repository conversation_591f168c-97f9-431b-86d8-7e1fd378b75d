package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.QuerySqlResponse;

/**
 * Custom Repo table Query SQL.
 */
public interface QuerySqlRepositoryCustom {

  /**
   * Find a QuerySqlEntity object with the specified id.
   *
   * @param id The id of group query sql.
   * @return The list QuerySqlEntity objects with the specified id of group query sql.
   */
  QuerySqlResponse findQuerySqlById(Long id);
}
