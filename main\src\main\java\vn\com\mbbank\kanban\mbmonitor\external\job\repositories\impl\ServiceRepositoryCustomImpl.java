package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ServiceWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ServiceRepositoryCustom;


/**
 * Implement ServiceRepositoryCustom table SERVICE.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ServiceRepositoryCustomImpl implements ServiceRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;

  @Override
  public List<ServiceWithPriorityModel> findServiceWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    PrepareQuery query = new PrepareQuery("""
        SELECT service.ID                       AS id,
               service.NAME                     AS name,
               alert.ALERT_PRIORITY_CONFIG_ID   AS alertPriorityConfigId,
               COUNT(alertGroup.PRIMARY_ALERT_ID) AS alertAmount
        FROM ALERT_GROUP alertGroup
        JOIN ALERT alert
            ON alertGroup.PRIMARY_ALERT_ID = alert.ID
        LEFT JOIN SERVICE service
            ON alert.SERVICE_ID = service.ID
        WHERE 1 = 1
         """)
        .append(buildQueryStatusEq(alertGroupStatus))
        .append("""
            GROUP BY service.ID, service.NAME, alert.ALERT_PRIORITY_CONFIG_ID
            ORDER BY service.NAME
            """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), ServiceWithPriorityModel.class);
  }

  private PrepareQuery buildQueryStatusEq(AlertGroupStatusEnum alertGroupStatus) {
    if (Objects.isNull(alertGroupStatus)) {
      return null;
    }
    return new PrepareQuery(" AND alertGroup.STATUS = :status ", "status", alertGroupStatus.name());
  }
}
