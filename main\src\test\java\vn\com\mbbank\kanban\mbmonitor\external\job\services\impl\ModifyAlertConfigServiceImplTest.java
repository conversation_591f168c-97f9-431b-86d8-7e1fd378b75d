package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertModifyFieldEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ModifyAlertConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigModifyService;


@ExtendWith(MockitoExtension.class)
public class ModifyAlertConfigServiceImplTest {

  // Giả lập các dependency
  @Mock
  private ModifyAlertConfigRepository modifyAlertConfigRepository;
  @Mock
  private CustomObjectService customObjectService;
  @Mock
  private ModifyAlertConfigModifyService modifyAlertConfigModifyService;
  @Mock
  private ModifyAlertConfigDependencyService modifyAlertConfigDependencyService;
  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private AlertPriorityConfigService alertPriorityConfigService;

  @InjectMocks
  private ModifyAlertConfigServiceImpl service;

  private static final long DEFAULT_PRIORITY_ID = -1L;

  @BeforeEach
  public void setUp() {
    service = new ModifyAlertConfigServiceImpl(
        modifyAlertConfigRepository,
        customObjectService,
        modifyAlertConfigModifyService,
        alertPriorityConfigService,
        modifyAlertConfigDependencyService,
        sysLogKafkaProducerService
    );
  }

  @Test
  public void updateAlertsForModify_emptyInput_returnsEmptyList() {
    List<AlertBaseModel> result = service.updateAlertsForModify(Collections.emptyList());
    assertTrue(result.isEmpty(), "Khi không có alert raw, kết quả trả về phải là list rỗng");
  }

  @Test
  public void updateAlertsForModify_noActiveConfigs_returnsConvertedAlerts() {
    // Giả lập: repository trả về danh sách config trống
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(Collections.emptyList());

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(),
        "Khi không có config, alert raw chỉ được chuyển sang entity mà không bị thay đổi");
    AlertEntity alertEntity = result.get(0);
    assertEquals("original content", alertEntity.getContent());
    assertEquals("original recipient", alertEntity.getRecipient());
    assertEquals(-1L, alertEntity.getAlertPriorityConfigId());
  }
  @Test
  public void updateAlertsForModify_withActiveConfig_appliesModifications() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    ModifyAlertConfigEntity config2 = new ModifyAlertConfigEntity();
    config2.setId(2L);
    ModifyAlertConfigEntity config3 = new ModifyAlertConfigEntity();
    config2.setActive(true);
    config3.setId(3L);
    config3.setActive(true);
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config, config2,config3));

    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    dependencyApp.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("10L");
    dependencyService.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");
    dependencyService2.setModifyAlertConfigId(1L);
    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(dependencyApp, dependencyService,dependencyService2));

    // Giả lập danh sách các modify field cho config
    ModifyAlertConfigModifyEntity modContent = new ModifyAlertConfigModifyEntity();
    modContent.setId(1L);
    modContent.setModifyAlertConfigId(1L);
    modContent.setFieldName("content");
    modContent.setFieldValue("modified content");

    ModifyAlertConfigModifyEntity modPriority = new ModifyAlertConfigModifyEntity();
    modPriority.setId(2L);
    modPriority.setModifyAlertConfigId(1L);
    modPriority.setFieldName("priority");
    modPriority.setFieldValue("200");

    ModifyAlertConfigModifyEntity modContact = new ModifyAlertConfigModifyEntity();
    modContact.setId(3L);
    modContact.setModifyAlertConfigId(1L);
    modContact.setFieldName("contact");
    modContact.setFieldValue("modified contact");
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    RuleCondition element =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    RuleCondition element1 =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS).value("2343321321")
            .build();
    RuleCondition element2 =
        RuleCondition.builder().field("123").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    when(customObjectService.calculatorCustomObjectValue(any(),any())).thenReturn("123");
    ruleGroupType.setRules(List.of(element,element2));
    config.setRuleGroup(ruleGroupType);
    RuleGroupType r2 = new RuleGroupType();
    r2.setRules(List.of(element1));
    r2.setCombinator(ConditionCombinatorEnum.AND);
    config3.setRuleGroup(r2);
    config2.setRuleGroup(r2);

    when(modifyAlertConfigModifyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(modContent, modPriority));

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
  }

  @Test
  public void updateAlertsForModify_withActiveConfig_appliesModifications2() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    ModifyAlertConfigEntity config2 = new ModifyAlertConfigEntity();
    config2.setId(2L);
    ModifyAlertConfigEntity config3 = new ModifyAlertConfigEntity();
    config2.setActive(true);
    config3.setId(3L);
    config3.setActive(true);
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config, config2,config3));

    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    dependencyApp.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("10L");
    dependencyService.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");
    dependencyService2.setModifyAlertConfigId(1L);
    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(dependencyApp, dependencyService,dependencyService2));

    // Giả lập danh sách các modify field cho config
    ModifyAlertConfigModifyEntity modContent = new ModifyAlertConfigModifyEntity();
    modContent.setId(1L);
    modContent.setModifyAlertConfigId(1L);
    modContent.setFieldName("content");
    modContent.setFieldValue("modified content");

    ModifyAlertConfigModifyEntity modPriority = new ModifyAlertConfigModifyEntity();
    modPriority.setId(2L);
    modPriority.setModifyAlertConfigId(1L);
    modPriority.setFieldName("priority");
    modPriority.setFieldValue("200");

    ModifyAlertConfigModifyEntity modContact = new ModifyAlertConfigModifyEntity();
    modContact.setId(3L);
    modContact.setModifyAlertConfigId(1L);
    modContact.setFieldName("contact");
    modContact.setFieldValue("modified contact");
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    RuleCondition element =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    RuleCondition element1 =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS).value("2343321321")
            .build();
    RuleCondition element2 =
        RuleCondition.builder().field("123").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    when(customObjectService.calculatorCustomObjectValue(any(),any())).thenReturn("123");
    ruleGroupType.setRules(List.of(element,element2));
    config.setRuleGroup(ruleGroupType);
    RuleGroupType r2 = new RuleGroupType();
    r2.setRules(List.of(element1));
    r2.setCombinator(ConditionCombinatorEnum.AND);
    config3.setRuleGroup(r2);
    config2.setRuleGroup(r2);
    when(modifyAlertConfigModifyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(modContent, modPriority, modContact, modContact));

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
    AlertEntity alertEntity = result.get(0);
  }

  @Test
  public void updateAlertsForModify_withConfig_dependencyNotMatch_noModificationsApplied() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);

    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config));

//    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigId(1L))
//        .thenReturn(Collections.emptyList());

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");
    RuleGroupType ruleGroupType = RuleGroupType.builder().combinator(ConditionCombinatorEnum.OR).rules(List.of()).build();
    config.setRuleGroup(ruleGroupType);
    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
    AlertEntity alertEntity = result.get(0);
    assertEquals("original content", alertEntity.getContent());
    assertEquals("original recipient", alertEntity.getRecipient());
    assertEquals(DEFAULT_PRIORITY_ID, alertEntity.getAlertPriorityConfigId(),
        "Priority phải được set về DEFAULT_PRIORITY_ID (-1)");
  }

  @Test
  public void updateAlertsForModify_withActiveConfig_appliesModifications_customObjectRule() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    ModifyAlertConfigEntity config2 = new ModifyAlertConfigEntity();
    config2.setId(2L);
    config2.setActive(true);
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config, config2));

    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("10L");
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");

//    when(customObjectService.calculatorCustomObjectValue(any(),any())).thenReturn("123");
//    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigId(1L))
//        .thenReturn(List.of(dependencyApp, dependencyService, dependencyService2));

    // Giả lập danh sách các modify field cho config
    ModifyAlertConfigModifyEntity modContent = new ModifyAlertConfigModifyEntity();
    modContent.setId(1L);
    modContent.setModifyAlertConfigId(1L);
    modContent.setFieldName("content");
    modContent.setFieldValue("modified content");


    ModifyAlertConfigModifyEntity modContact = new ModifyAlertConfigModifyEntity();
    modContact.setId(3L);
    modContact.setModifyAlertConfigId(1L);
    modContact.setFieldName("contact");
    modContact.setFieldValue("modified contact");
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    RuleCondition element =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    RuleCondition element2 =
        RuleCondition.builder().field("1L").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    ruleGroupType.setRules(List.of(element, element2));
    config.setRuleGroup(ruleGroupType);
    config2.setRuleGroup(ruleGroupType);
    when(modifyAlertConfigModifyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(modContent, modContact, modContent));

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
    AlertBaseModel alertEntity = result.get(0);
  }

  @Test
  public void updateAlertsForModify_withActiveConfig_appliesModifications_customObjectRule_modifyDouble() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    ModifyAlertConfigEntity config2 = new ModifyAlertConfigEntity();
    config2.setId(2L);
    config2.setActive(true);
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config, config2));

    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("10L");
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");

//    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigId(1L))
//        .thenReturn(List.of(dependencyApp, dependencyService,dependencyService2));

    // Giả lập danh sách các modify field cho config
    ModifyAlertConfigModifyEntity modContent = new ModifyAlertConfigModifyEntity();
    modContent.setId(1L);
    modContent.setModifyAlertConfigId(1L);
    modContent.setFieldName("content");
    modContent.setFieldValue("modified content");


    ModifyAlertConfigModifyEntity modContact = new ModifyAlertConfigModifyEntity();
    modContact.setId(3L);
    modContact.setModifyAlertConfigId(1L);
    modContact.setFieldName("contact");
    modContact.setFieldValue("modified contact");
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    RuleCondition element =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    RuleCondition element2 =
        RuleCondition.builder().field("1L").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    ruleGroupType.setRules(List.of(element, element2));
    config.setRuleGroup(ruleGroupType);
    config2.setRuleGroup(ruleGroupType);
    when(modifyAlertConfigModifyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(modContent, modContact));

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
    AlertEntity alertEntity = result.get(0);
    assertEquals("original content", alertEntity.getContent(), "Nội dung phải được thay đổi");
  }

  @Test
  public void updateAlertsForModify_withActiveConfig_appliesModifications_customObjectRule_ruleGroup_False() {
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    ModifyAlertConfigEntity config2 = new ModifyAlertConfigEntity();
    config2.setId(2L);
    config2.setActive(true);
    when(modifyAlertConfigRepository.findAllByActiveOrderByPosition(true))
        .thenReturn(List.of(config, config2));

    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("10L");
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");

//    when(modifyAlertConfigDependencyService.findAllByModifyAlertConfigId(1L))
//        .thenReturn(List.of(dependencyApp, dependencyService,dependencyService2));

    // Giả lập danh sách các modify field cho config
    ModifyAlertConfigModifyEntity modContent = new ModifyAlertConfigModifyEntity();
    modContent.setId(1L);
    modContent.setModifyAlertConfigId(1L);
    modContent.setFieldName("content");
    modContent.setFieldValue("modified content");

    ModifyAlertConfigModifyEntity modContentError = new ModifyAlertConfigModifyEntity();
    modContentError.setId(1L);
    modContentError.setModifyAlertConfigId(1L);
    modContentError.setFieldName("con1tent");
    modContentError.setFieldValue("modified content");


    ModifyAlertConfigModifyEntity modContact = new ModifyAlertConfigModifyEntity();
    modContact.setId(3L);
    modContact.setModifyAlertConfigId(1L);
    modContact.setFieldName("contact");
    modContact.setFieldValue("modified contact");
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    RuleCondition element =
        RuleCondition.builder().field("content").operator(OperatorEnum.IS).value("2343321321")
            .build();
    RuleCondition element2 =
        RuleCondition.builder().field("1L").operator(OperatorEnum.IS_NOT).value("2343321321")
            .build();
    ruleGroupType.setRules(List.of(element, element2));
    config.setRuleGroup(ruleGroupType);
    config2.setRuleGroup(ruleGroupType);
    when(modifyAlertConfigModifyService.findAllByModifyAlertConfigIdIn(anyList()))
        .thenReturn(List.of(modContent, modContact));

    AlertBaseModel alertRaw = new AlertBaseModel();
    alertRaw.setContent("original content");
    alertRaw.setRecipient("original recipient");
    alertRaw.setApplicationId("1L");
    alertRaw.setServiceId("10L");

    List<AlertBaseModel> result = service.updateAlertsForModify(List.of(alertRaw));
    assertEquals(1, result.size(), "Có 1 alert entity được tạo ra");
    AlertBaseModel alertEntity = result.get(0);
    assertEquals("original content", alertEntity.getContent(), "Nội dung phải được thay đổi");
  }

  @Test
  void applyModification_success(){
    Set<AlertModifyFieldEnum> modifyFieldEnumSet = new HashSet<>();
    AlertEntity alertEntity = new AlertEntity();
    alertEntity.setContent("123");
    ModifyAlertConfigModifyEntity m1 = new ModifyAlertConfigModifyEntity();
    m1.setFieldName("priority");
    m1.setFieldValue("11");
    ModifyAlertConfigModifyEntity m2 = new ModifyAlertConfigModifyEntity();
    m2.setFieldName("content");
    m2.setFieldValue("1L");
    ModifyAlertConfigModifyEntity m3 = new ModifyAlertConfigModifyEntity();
    m3.setFieldName("contact");
    m3.setFieldValue("1L");
    when(customObjectService.replaceCustomObjectIdsWithCustomObjectValues(any(),any())).thenReturn("123");
    List<ModifyAlertConfigModifyEntity> modifyEntities = List.of(m1,m2,m3,m1);
        ReflectionTestUtils.invokeMethod(
            service, "applyModifications", modifyFieldEnumSet,alertEntity, modifyEntities
        );

  }
  @Test
  void dependenciesMatch_success(){
    ModifyAlertConfigDependencyEntity dependencyApp = new ModifyAlertConfigDependencyEntity();
    dependencyApp.setType(ModifyAlertConfigDependencyTypeEnum.APPLICATION);
    dependencyApp.setDependencyId("1L");
    dependencyApp.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService = new ModifyAlertConfigDependencyEntity();
    dependencyService.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE);
    dependencyService.setDependencyId("120L");
    dependencyService.setModifyAlertConfigId(1L);
    ModifyAlertConfigDependencyEntity dependencyService2 = new ModifyAlertConfigDependencyEntity();
    dependencyService2.setType(ModifyAlertConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    dependencyService2.setDependencyId("10L");
    dependencyService2.setModifyAlertConfigId(1L);
    ModifyAlertConfigEntity config = new ModifyAlertConfigEntity();
    config.setId(1L);
    config.setActive(true);
    AlertEntity alertRaw = new AlertEntity();
    alertRaw.setServiceId("10L");
    alertRaw.setApplicationId("1L");
    Map<Long, List<ModifyAlertConfigDependencyEntity>> depenMaps = Map.of(1L,List.of(dependencyService,
        dependencyService2, dependencyApp));
   boolean res =  ReflectionTestUtils.invokeMethod(
       service, "dependenciesMatch", config,alertRaw,depenMaps
   );
   assertTrue(res);

  }
  @Test
  void getRepository_success() {
    assertEquals(modifyAlertConfigRepository, service.getRepository());
  }
}
