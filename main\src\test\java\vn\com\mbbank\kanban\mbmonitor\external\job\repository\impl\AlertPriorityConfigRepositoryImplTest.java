package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.ApplicationTest;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.AlertPriorityConfigRepositoryCustomImpl;

@ExtendWith(MockitoExtension.class)
class AlertPriorityConfigRepositoryImplTest extends ApplicationTest {
  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;

  @InjectMocks
  AlertPriorityConfigRepositoryCustomImpl alertPriorityConfigRepositoryCustom;

  @Test
  void findAllMatchPriorityConfig_success() {
    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), any())).thenReturn(Collections.emptyList());
    var res = alertPriorityConfigRepositoryCustom.findAllMatchPriorityConfig("", "");
    assertEquals(res.size(), 0);
  }


  @Test
  void buildRawPriorityAndRawAlertContentAndGroup_success_caseReturnNull() {
    String rawPriority = "";
    String rawAlertContent = "";

    // Act
    PrepareQuery result = (PrepareQuery) ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityAndRawAlertContentAndGroup", rawPriority, rawAlertContent
    );

    // Assert
    assertNull(result);
  }

  @Test
  void buildRawPriorityAndRawAlertContentAndGroup_success_caseRawPriorityEmpty() {
    String rawPriority = "";
    String rawAlertContent = "content";

    // Act
    PrepareQuery result = (PrepareQuery) ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityAndRawAlertContentAndGroup", rawPriority, rawAlertContent
    );

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildRawPriorityAndRawAlertContentAndGroup_success_caseRawAlertContentEmpty() {
    String rawPriority = "content";
    String rawAlertContent = "";

    // Act
    PrepareQuery result = (PrepareQuery) ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityAndRawAlertContentAndGroup", rawPriority, rawAlertContent
    );

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildRawPriorityAndRawAlertContentAndGroup_success() {
    String rawPriority = "123";
    String rawAlertContent = "";

    // Act
    PrepareQuery result = (PrepareQuery) ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityAndRawAlertContentAndGroup", rawPriority, rawAlertContent
    );

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildRawPriorityAndRawAlertContentAndGroup_success_caseNotEmpty() {
    String rawPriority = "123";
    String rawAlertContent = "123";

    // Act
    PrepareQuery result = (PrepareQuery) ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityAndRawAlertContentAndGroup", rawPriority, rawAlertContent
    );

    // Assert
    assertNotNull(result);
  }


  @Test
  void buildRawPriorityEq_success_caseReturnNull() {
    String rawPriority = "";

    // Act
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityEq", rawPriority
    );

    // Assert
    assertNull(result);
  }

  @Test
  void buildRawPriorityEq_success() {
    String rawPriority = "a";

    // Act
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawPriorityEq", rawPriority
    );

    // Assert
    assertNotNull(result);
  }

  @Test
  void buildRawAlertContentLike_success_caseReturnNull() {
    String rawAlertContent = "";

    // Act
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawAlertContentLike", rawAlertContent
    );

    // Assert
    assertNull(result);
  }

  @Test
  void buildRawAlertContentLike_success() {
    String rawAlertContent = "a";

    // Act
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertPriorityConfigRepositoryCustom, "buildRawAlertContentLike", rawAlertContent
    );

    // Assert
    assertNotNull(result);
  }

}
