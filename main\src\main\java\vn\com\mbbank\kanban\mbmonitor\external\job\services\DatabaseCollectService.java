package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.BaseCollectAlertService;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
public interface DatabaseCollectService extends BaseService<DatabaseCollectEntity, Long>,
    BaseCollectAlertService {
  /**
   * Collect database.
   *
   * @param id id collect config
   * @throws BusinessException ex
   */
  @Transactional(rollbackFor = {Exception.class})
  void collect(Long id) throws BusinessException;


  /**
   * find all config active.
   *
   * @return lst
   */
  List<DatabaseCollectEntity> findAllByIsActiveTrue();
}