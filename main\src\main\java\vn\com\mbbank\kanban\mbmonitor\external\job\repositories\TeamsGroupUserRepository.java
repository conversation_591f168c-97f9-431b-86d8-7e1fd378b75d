package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupUserEntity;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:19:36
 */
@Repository
public interface TeamsGroupUserRepository extends JpaCommonRepository<TeamsGroupUserEntity, String> {
  /**
   * Delete user old.
   *
   * @param teamsGroupIds teamsGroupIds
   * @return total record
   */
  @Transactional
  int deleteAllByTeamsGroupChatIdIn(List<String> teamsGroupIds);
}
