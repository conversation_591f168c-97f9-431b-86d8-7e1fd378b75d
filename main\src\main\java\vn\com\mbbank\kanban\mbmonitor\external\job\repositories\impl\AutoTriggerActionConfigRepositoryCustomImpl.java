package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.external.job.mapper.AutoTriggerActionConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigRepositoryCustom;

/**
 * AutoTriggerActionConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AutoTriggerActionConfigRepositoryCustomImpl implements AutoTriggerActionConfigRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AutoTriggerActionConfigEntity> findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum triggerTypeEnum) {
    var query = new PrepareQuery(
            """
                    SELECT autoTriggerActionConfig.ID AS id,
                    autoTriggerActionConfig.NAME AS name,
                    autoTriggerActionConfig.DESCRIPTION AS description,
                    autoTriggerActionConfig.ACTIVE AS active,
                    autoTriggerActionConfig.RULE_GROUP AS ruleGroupColumn,
                    autoTriggerActionConfig.CREATED_DATE AS createdDate,
                    autoTriggerActionConfig.TIME_SINCE_LAST_TRIGGER AS timeSinceLastTrigger
                    FROM AUTO_TRIGGER_ACTION_CONFIG autoTriggerActionConfig
                    WHERE ACTIVE = 1
                    AND (
                    (LAST_RUN + NUMTODSINTERVAL(TIME_SINCE_LAST_TRIGGER, 'SECOND')) < SYSTIMESTAMP
                    OR LAST_RUN IS NULL
                    )
                    AND autoTriggerActionConfig.TRIGGER_TYPE = :triggerType
                    ORDER BY CREATED_DATE DESC
                    """,
            Map.of("triggerType", triggerTypeEnum));
    var result = sqlQueryUtil.queryModel()
            .queryForList(query.getQuery(), query.getParams(), AutoTriggerActionConfigResponse.class);
    if (result.isEmpty()) {
      return new ArrayList<>();
    }
    result.forEach(config -> {
      config.setRuleGroup(RuleGroupConverter.convertToRuleGroup(config.getRuleGroupColumn()));
    });
    return AutoTriggerActionConfigResponseMapper.INSTANCE.mapTo(result);
  }
}
