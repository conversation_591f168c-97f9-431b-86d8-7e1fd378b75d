package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExportDataService;

/**
 * ExportDataJob for scheduler config.
 *
 * <AUTHOR>
 * @created_date 24/03/2025
 */
@Component(JobNameConstants.EXPORT_FILE)
@RequiredArgsConstructor
public class ExportDataJob extends JobConfig {
  private final ExportDataService exportDataService;

  private static final Long EXPORT_DATA_LOCK_TIME_OUT_MS = 15 * 60 * 1000L; //millisecond
  private static final Long EXPORT_DATA_FILE_INTERVAL_TIME_MS = 24 * 60 * 60 * 1000L; //millisecond

  @Override
  public void executeJob(JobExecutionContext context) {
    exportDataService.deleteExpiredFile();
  }


  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    return Map.of(JobNameConstants.EXPORT_FILE, EXPORT_DATA_FILE_INTERVAL_TIME_MS);
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.EXPORT_FILE;
  }


  @Override
  public Long getLockTimeoutMs() {
    return EXPORT_DATA_LOCK_TIME_OUT_MS;
  }
}
