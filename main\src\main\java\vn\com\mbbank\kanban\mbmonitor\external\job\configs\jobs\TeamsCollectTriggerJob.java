package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.RedisLockService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsCollectGroupService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 5/7/2025
 */
@Component(JobNameConstants.TEAMS_CONFIG_ALERT_TRIGGER)
@RequiredArgsConstructor
public class TeamsCollectTriggerJob implements CommonBaseConsumerService {
  private  final TeamsCollectGroupService teamsCollectGroupService;
  @Value("${monitor.redis.lock.teams.timeout:600000}")
  private Long lockTimeoutMs;

  @Autowired
  protected RedisLockService redisLockService;

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {

    if (redisLockService.tryLock(JobNameConstants.TEAMS_CONFIG_ALERT_TRIGGER, lockTimeoutMs)) {
      try {
        teamsCollectGroupService.collectGroup();
      } finally {
        redisLockService.unlock(JobNameConstants.TEAMS_CONFIG_ALERT_TRIGGER);
      }
    }
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.TEAMS_CONFIG_ALERT_TRIGGER;
  }
}
