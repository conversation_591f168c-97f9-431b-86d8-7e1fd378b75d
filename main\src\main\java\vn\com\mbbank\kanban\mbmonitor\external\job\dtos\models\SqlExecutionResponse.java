package vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Model use for SqlExecutionResponse.
 */
@Getter
@Setter
@Builder
public class SqlExecutionResponse {
  /**
   * Mapping sql column and value.
   */
  @Getter
  @Setter
  @Builder
  public static class SqlMappingColumnData {
    private String column;
    private String value;
  }

  /**
   * Mapping all Column and Value to a Row.
   */
  @Getter
  @Setter
  @Builder
  public static class SqlDataMapping {
    private List<SqlMappingColumnData> listSqlMappingColumnDatas;
  }

  private List<SqlDataMapping> listDataMappings;
  List<String> listColumns;

  @JsonProperty(value = "isNonQuery")
  private boolean isNonQuery;
}
