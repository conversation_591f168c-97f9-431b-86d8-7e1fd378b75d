package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileStatusEnum;

/**
 * Repository table  ExportDataEntity.
 */
@Repository
public interface ExportDataRepository extends JpaCommonRepository<ExportDataEntity, String> {

  /**
   * Updates the status and file storage ID for the export data entity with the given ID.
   *
   * @param id            the export data entity ID to update
   * @param status        the new status as a {@code String}
   * @param fileStorageId the new file storage ID to set
   */
  @Modifying
  @Transactional
  @Query(value = "UPDATE EXPORT_DATA exportData SET exportData.STATUS = :status,"
      + " exportData.FILE_STORAGE_ID = :fileStorageId WHERE exportData.ID = :id",
      nativeQuery = true)
  void updateStatusAndFileStorageId(@Param("id") String id,
                                    @Param("status") String status,
                                    @Param("fileStorageId") Long fileStorageId);

  /**
   * Updates the status for the export data entity with the given ID.
   *
   * @param id     the export data entity ID to update
   * @param status the new status as a {@code String}
   */
  @Modifying
  @Transactional
  @Query(value = "UPDATE EXPORT_DATA exportData SET exportData.STATUS = :status WHERE exportData.ID = :id",
      nativeQuery = true)
  void updateStatusById(@Param("id") String id,
                        @Param("status") String status);

  /**
   * Updates the status for the export data entity with the given ID.
   *
   * @param ids     the export data entity ID to update
   * @param status the new status as a {@code String}
   */
  @Modifying
  @Query(value = "UPDATE EXPORT_DATA exportData SET exportData.STATUS = :status WHERE exportData.ID IN :ids",
      nativeQuery = true)
  void updateStatusByIdIn(@Param("ids") List<String> ids, @Param("status") String status);

  /**
   * find all export data by created data before and status.
   *
   * @param date     datatime.
   * @param status the new status as a {@code String}
   * @return list export data entity.
   */
  List<ExportDataEntity> findAllByCreatedDateBeforeAndStatus(Date date, ExportFileStatusEnum status);

}
