package vn.com.mbbank.kanban.mbmonitor.external.job.test;

import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/13/2025
 */

@Transactional
@Rollback
public class ControllerTest {
  public void verifyPermissions(List<AclPermissionModel> expected, Method method) {
    var expectedCopy = KanbanCommonUtil.isEmpty(expected) ? new ArrayList<>() : expected;
    HasPermission hasPermission = method.getAnnotation(HasPermission.class);
    var actuals = KanbanCommonUtil.isEmpty(hasPermission) ? new ArrayList<AclPermission>() :
        new ArrayList<AclPermission>(
            Arrays.asList(hasPermission.value()));

    Set<AclPermissionModel> actualSet = actuals.stream()
        .map(ap -> new AclPermissionModel(ap.module(), ap.action()))
        .collect(Collectors.toSet());

    Set<AclPermissionModel> expectedSet = Set.copyOf(expected);
    Assertions.assertTrue(
        actualSet.containsAll(expected) && expected.containsAll(actualSet),
        "Permission not match!"
    );
  }

  public void verifyPermissions(List<AclPermissionModel> expected, String methodName,
                                Class<?> clazz) {
    Method method = Arrays.stream(clazz.getDeclaredMethods())
        .filter(m -> m.getName().equals(methodName))
        .findFirst()
        .orElseThrow(() -> new RuntimeException("Method not found: " + methodName));
    verifyPermissions(expected, method);
  }


  public void assertValidationsEquals(String messageExpect, Object dto) {
    ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    var validator = factory.getValidator();
    var violations = validator.validate(dto);

    if (KanbanCommonUtil.isEmpty(violations)) {
      Assertions.assertTrue(false);
    }
    var firstMessage = violations.stream().findFirst();
    Assertions.assertEquals(messageExpect, firstMessage.get().getMessage());

  }

  public void assertValidationsEmpty(Object dto) {
    ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    var validator = factory.getValidator();
    var violations = validator.validate(dto);

    if (KanbanCommonUtil.isEmpty(violations)) {
      Assertions.assertTrue(true);
    } else {
      Assertions.assertTrue(false);
    }
  }
}
