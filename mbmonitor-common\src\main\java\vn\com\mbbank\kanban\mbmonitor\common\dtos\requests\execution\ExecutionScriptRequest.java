package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;

/**
 * ExecutionScriptRequest.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionScriptRequest {
  @NotNull
  String executionId;
  @NotNull
  String executionBy;
  @Builder.Default
  List<ExecuteScriptParamModel> params = new ArrayList<>();
  // use for sql script
  PaginationRequestDTO paginationRequest;
  ApiInfoRequest apiInfo;
  List<VariableRequest> variables;
}
