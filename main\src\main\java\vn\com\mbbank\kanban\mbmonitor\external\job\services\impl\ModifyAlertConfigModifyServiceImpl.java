package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigModifyRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigModifyService;

@Service
@RequiredArgsConstructor
public class ModifyAlertConfigModifyServiceImpl extends BaseServiceImpl<ModifyAlertConfigModifyEntity, Long>
    implements ModifyAlertConfigModifyService {

  private final ModifyAlertConfigModifyRepository modifyAlertConfigModifyRepository;

  @Override
  protected JpaCommonRepository<ModifyAlertConfigModifyEntity, Long> getRepository() {
    return modifyAlertConfigModifyRepository;
  }


  @Override
  public List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigId(Long alertGroupConfigId) {
    return this.modifyAlertConfigModifyRepository.findAllByModifyAlertConfigId(alertGroupConfigId);
  }

  @Override
  public List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigIdIn(List<Long> modifyAlertConfigIds) {
    return this.modifyAlertConfigModifyRepository.findAllByModifyAlertConfigIdIn(modifyAlertConfigIds);
  }
}
