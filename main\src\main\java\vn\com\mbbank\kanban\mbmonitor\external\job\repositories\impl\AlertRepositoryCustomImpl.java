package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.repositories.AlertRepositoryQuery;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertRepositoryCustom;

/**
 * Implement AlertRepositoryCustom table Alert.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertRepositoryCustomImpl implements AlertRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;
  AlertRepositoryQuery alertRepositoryQuery;

  @Override
  public List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status) {
    var query = alertRepositoryQuery.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
        serviceIds, applicationIds, alertGroupHandleTriggerInterval, status
    );
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertEntity.class);
  }

  @Override
  public CursorPageResponse<AlertResponse, AlertCursor> findAll(AlertPaginationRequest paginationRequest) {
    var query = alertRepositoryQuery.findAll(paginationRequest);

    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertResponse.class);
    if (result.size() > paginationRequest.getPageSize()) {
      return new CursorPageResponse<>(result.subList(0, paginationRequest.getPageSize()),
          new AlertCursor(result.get(result.size() - 2).getId(), result.get(result.size() - 2).getCreatedDate())
      );
    }
    return new CursorPageResponse<>(result, null);
  }

  @Override
  public List<AlertEntity> findAlertKeyByGroupIdIn(List<Long> alertGroupIds) {
    var query = new PrepareQuery(
            """
            SELECT alert.*
            FROM ALERT alert
            WHERE alert.ID IN (
                SELECT alertGroup.PRIMARY_ALERT_ID
                FROM ALERT_GROUP alertGroup
                WHERE alertGroup.ID IN (:alertGroupIds)
            )
            """,
            Map.of("alertGroupIds", alertGroupIds));
    return sqlQueryUtil.queryModel()
            .queryForList(query.getQuery(), query.getParams(), AlertEntity.class);
  }

  @Override
  public List<AlertEntity> findTopAlertsByAlertGroupIdAndStatus(
      Long alertGroupId, AlertStatusEnum status, int numberOfResult) {
    var query = alertRepositoryQuery.findTopAlertsByAlertGroupIdAndStatus(alertGroupId,
        status, numberOfResult);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertEntity.class);
  }

}
