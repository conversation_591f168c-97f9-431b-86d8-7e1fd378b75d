package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.log.AlertLogModelMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.FilterAlertConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FilterAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;

/**
 * Service Logic FilterAlertConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class FilterAlertConfigServiceImpl extends BaseServiceImpl<FilterAlertConfigEntity, Long>
    implements FilterAlertConfigService {
  private final FilterAlertConfigRepository filterAlertConfigRepository;
  private final CustomObjectService customObjectService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final AlertLogModelMapper alertLogModelMapper = AlertLogModelMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<FilterAlertConfigEntity, Long> getRepository() {
    return filterAlertConfigRepository;
  }

  @Override
  public List<AlertBaseModel> updateAlertsForFilter(List<AlertBaseModel> alertRawValues) {
    if (CollectionUtils.isEmpty(alertRawValues)) {
      return new ArrayList<>();
    }
    List<FilterAlertConfigEntity> configs = filterAlertConfigRepository.findAllByActive(true);
    if (configs.isEmpty()) {
      return alertRawValues;
    }
    var customObjects = customObjectService.findAllByDeletedIsFalse();
    List<AlertBaseModel> results = new ArrayList<>();
    for (AlertBaseModel alert : alertRawValues) {
      var matchedConfig = configs.stream()
          .filter(config -> config.getRuleGroup()
              .check(getAlertConditionRawValueMap(alert, customObjects))
          ).findFirst();
      if (matchedConfig.isPresent()) {
        sysLogKafkaProducerService.send(
            LogActionEnum.DENY_ALERT_BY_FILTER_ALERT,
            alertLogModelMapper.map(alert),
            matchedConfig.get().getName());
      } else {
        results.add(alert);
      }
    }
    return results;
  }

  protected static Map<String, Object> getAlertConditionRawValueMap(AlertBaseModel alert,
                                                                    List<CustomObjectEntity> customObjects) {
    if (Objects.isNull(alert)) {
      return Collections.emptyMap();
    }
    var mapValue = new HashMap<String, Object>();
    mapValue.put(FilterAlertConfigConstants.FILTER_FIELD_NAME_SERVICE, alert.getServiceNameRaw());
    mapValue.put(FilterAlertConfigConstants.FILTER_FIELD_NAME_APPLICATION, alert.getApplicationNameRaw());
    mapValue.put(FilterAlertConfigConstants.FILTER_FIELD_NAME_CONTENT, alert.getContentRaw());
    mapValue.put(FilterAlertConfigConstants.FILTER_FIELD_NAME_PRIORITY, alert.getPriorityRaw());
    mapValue.put(FilterAlertConfigConstants.FILTER_FIELD_NAME_RECIPIENT, alert.getRecipientRaw());
    if (!CollectionUtils.isEmpty(customObjects)) {
      customObjects.forEach(customObject -> mapValue.put(String.valueOf(customObject.getId()),
          CustomObjectUtils.evaluate(alert.getContentRaw(), customObject)));
    }
    return mapValue;
  }
}
