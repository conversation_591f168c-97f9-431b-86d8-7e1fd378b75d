package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Dialog;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Frame;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.LoadState;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanStringFormatter;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.RpaConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ActionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MonitorTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertBaseModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.VariableUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.PlaywrightManager;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MonitorActionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MonitorWebConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.RpaConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MonitorWebService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.VariableService;

/**
 * Monitor web service impl.
 *
 * <AUTHOR>
 * @created_date 03/06/2025
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MonitorWebServiceImpl extends BaseServiceImpl<MonitorWebConfigEntity, String>
    implements MonitorWebService {
  private static final Set<ActionTypeEnum> ACTIONS_NOT_REQUIRING_LOCATOR = EnumSet.of(
      ActionTypeEnum.WAIT, ActionTypeEnum.ALERT_ACCEPT, ActionTypeEnum.ALERT_DISMISS, ActionTypeEnum.GO_TO_URL);
  
  private final MonitorWebConfigRepository monitorWebConfigRepository;
  private final MonitorActionRepository monitorActionRepository;
  private final RpaConfigRepository rpaConfigRepository;
  private final AlertService alertService;
  private final CommonRawAlertService commonRawAlertService;
  private final MaintenanceTimeConfigService maintenanceTimeConfigService;
  private final FilterAlertConfigService filterAlertConfigService;
  private final ModifyAlertConfigService modifyAlertConfigService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final PlaywrightManager playwrightManager;
  private final SysLogKafkaProducerService sysLogService;
  private final VariableService variableService;
  
  private Frame currentFrame;
  
  @Override
  protected JpaCommonRepository<MonitorWebConfigEntity, String> getRepository() {
    return monitorWebConfigRepository;
  }
  
  @Override
  @Transactional(rollbackFor = {Exception.class})
  public void collect(String id) throws BusinessException {
    // Retrieve the latest RPA configuration based on creation date.
    // If no config is found or the config is inactive, exit early.
    RpaConfigEntity rpaConfig = rpaConfigRepository.findFirstByOrderByCreatedDateDesc().orElse(null);
    if (rpaConfig == null || !rpaConfig.getActive()) {
      return;
    }

    // Retrieve the monitor web configuration by ID.
    // If not found or inactive, exit early.
    MonitorWebConfigEntity monitorWebConfig = monitorWebConfigRepository.findById(id).orElse(null);
    if (monitorWebConfig == null || !monitorWebConfig.getActive()) {
      return;
    }
    String errorMessage;
    int interval = rpaConfig.getInterval();
    int numberOfRetry = rpaConfig.getNumberOfRetry();
    List<MonitorActionEntity> authActions = List.of();
    if (MonitorTypeEnum.LOGIN.equals(monitorWebConfig.getMonitorType())) {
      authActions = monitorActionRepository.findByActionIdOrderByOrdersAsc(monitorWebConfig.getAuthActionId());
    }
    var actions = monitorActionRepository.findByActionIdOrderByOrdersAsc(monitorWebConfig.getActionId());
    
    // Create page and go to website need monitor
    BrowserContext context = playwrightManager.createPageWithCustomBrowser(monitorWebConfig.getBrowser());
    errorMessage = processActionsWithRetry(authActions, actions, context, numberOfRetry, interval, monitorWebConfig);
    if (!KanbanCommonUtil.isNullOrEmpty(errorMessage)) {
      collectAlerts(monitorWebConfig, errorMessage);
    }
  }
  
  @Override
  public List<MonitorWebConfigEntity> findAllByIsActiveTrue() {
    return monitorWebConfigRepository.findAllByActiveTrue();
  }
  
  @Override
  public List<AlertBaseModel> filterAlerts(List<AlertBaseModel> alerts) {
    return filterAlertConfigService.updateAlertsForFilter(alerts);
  }
  
  @Override
  public List<AlertBaseModel> modifyAlerts(List<AlertBaseModel> alerts) {
    return modifyAlertConfigService.updateAlertsForModify(alerts);
  }
  
  @Override
  public List<AlertBaseModel> maintenanceAlerts(List<AlertBaseModel> alerts) {
    return maintenanceTimeConfigService.updateAlertsForMaintenance(alerts);
  }
  
  @Override
  public List<AlertBaseModel> saveAlerts(List<AlertBaseModel> alerts) {
    var data = alertService.saveAll(AlertBaseModelToEntityMapper.INSTANCE.mapTo(alerts));
    return AlertBaseModelToEntityMapper.INSTANCE.map(data);
  }
  
  @Override
  public List<AlertBaseModel> collectFilters(List<AlertBaseModel> alerts) {
    return alerts.stream().filter(AlertBaseModel::getIsValid).toList();
  }
  
  private void collectAlerts(MonitorWebConfigEntity config, String exceptionMessage) {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId(config.getServiceId());
    alertBaseModel.setApplicationId(config.getApplicationId());
    alertBaseModel.setRecipient(config.getContact());
    
    KanbanStringFormatter formatter = KanbanStringFormatter.newBuilder()
        .regexReplace("@weburl", config.getWebUrl())
        .regexReplace("@timeout_time", config.getTimeout().toString())
        .regexReplace("@exception error", exceptionMessage)
        .build();
    
    String content = formatter.format(config.getContent()).toString();
    if (KanbanCommonUtil.isEmpty(content)) {
      return;
    }
    var priority = alertPriorityConfigService.findById(config.getPriorityId());
    
    alertBaseModel.setContent(content);
    alertBaseModel.setContentRaw(content);
    alertBaseModel.setStatus(AlertStatusEnum.NEW);
    alertBaseModel.setAlertGroupId(0L);
    alertBaseModel.setSource(AlertSourceTypeEnum.MONITOR_WEB);
    alertBaseModel.setPriorityRaw(KanbanCommonUtil.isEmpty(
        priority) ? AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_NAME : priority.getName());
    
    var alerts = commonRawAlertService.createRawData(List.of(alertBaseModel), AlertSourceTypeEnum.MONITOR_WEB);
    if (!KanbanCommonUtil.isEmpty(alerts)) {
      collectAlerts(alerts);
    }
  }
  
  /**
   * Executes a list of monitoring actions on the provided Playwright page with retry logic.
   * If any action throws an exception, the method retries the entire sequence until the
   * maximum number of retries is reached. Between retries, it waits for a specified interval.
   *
   * @param actions        The list of actions to perform, each representing a UI interaction step.
   * @param context    The Playwright page instance where the actions are executed.
   * @param numberOfRetry  The maximum number of retry attempts allowed in case of failure.
   * @param monitorWebConfig  The monitor web config.
   * @return null if all actions are executed successfully;
   * otherwise, returns the exception message after exhausting retries.
   */
  private String processActionsWithRetry(List<MonitorActionEntity> authActions, List<MonitorActionEntity> actions,
       BrowserContext context, int numberOfRetry, int interval, MonitorWebConfigEntity monitorWebConfig) {
    int attempt = 0;
    int timeout = monitorWebConfig.getTimeout() * 1000;
    String webUrl = monitorWebConfig.getWebUrl();
    String configName = monitorWebConfig.getName();
    MonitorActionEntity currentAction = new MonitorActionEntity();
    Page currentPage = context.newPage();
    var authActionsNormalize = normalizeAuthActions(authActions);
    while (attempt <= numberOfRetry) {
      currentFrame = null;
      try {
        currentPage.navigate(webUrl, new Page.NavigateOptions().setTimeout(timeout));
        
        // process auth actions
        for (MonitorActionEntity authAction : authActionsNormalize) {
          currentAction = authAction;
          performAction(authAction, currentPage, timeout);
        }
        
        // process monitor actions
        AtomicReference<Page> mainPageRef = new AtomicReference<>(null);
        for (MonitorActionEntity action : actions) {
          currentAction = action;
          ActionTypeEnum type = action.getActionType();
          switch (type) {
            case BACK_TO_MAIN, CLOSE_POPUP, SWITCH_TO_POPUP ->
                currentPage = resolveCurrentPageByActionType(type, currentPage, context, mainPageRef, timeout);
            default ->
                performAction(action, currentPage, timeout);
          }
        }
        sysLogService.send(LogActionEnum.SUCCESS_MONITOR_WEB_JOB, configName);
        safelyClosePage(currentPage);
        return null;
      } catch (Exception e) {
        attempt++;
        if (attempt > numberOfRetry) {
          sysLogService.send(LogActionEnum.LAST_ERROR_MONITOR_WEB_JOB, configName, currentAction, e.getMessage());
          safelyClosePage(currentPage);
          return e.getMessage();
        }
        if (attempt == 1) {
          sysLogService.send(LogActionEnum.FIRST_ERROR_MONITOR_WEB_JOB, configName, currentAction, e.getMessage());
        } else {
          sysLogService.send(LogActionEnum.ATTEMPT_ERROR_MONITOR_WEB_JOB,
              configName, currentAction, attempt - 1, e.getMessage());
        }
        cleanupSession(context);
        waitInterval(interval);
      }
    }
    return null;
  }
  
  /**
   * Executes a single user interaction based on the action type defined in the MonitorActionEntity.
   * Uses Playwright's Locator and Frame APIs to interact with the page.
   *
   * @param action   The monitoring action to perform, including type, selector info, and value.
   * @param page     The Playwright page instance on which to perform the action.
   * @param timeout  The timeout in milliseconds to wait for each action.
   * @throws Exception if the action fails or an unexpected error occurs during execution.
   */
  private void performAction(MonitorActionEntity action, Page page, int timeout) throws Exception {
    try {
      if (ACTIONS_NOT_REQUIRING_LOCATOR.contains(action.getActionType())) {
        performActionWithOutLocator(action, page, timeout);
        return;
      }
      if (currentFrame == null) {
        currentFrame = page.mainFrame();
      }
      Locator locator = currentFrame.locator(resolveSelector(action));
      
      switch (action.getActionType()) {
        case CLICK -> locator.click(new Locator.ClickOptions().setTimeout(timeout));
        case DOUBLE_CLICK -> locator.dblclick(new Locator.DblclickOptions().setTimeout(timeout));
        case SEND_KEY -> locator.fill(action.getValue(), new Locator.FillOptions().setTimeout(timeout));
        case CLEAR_INPUT -> locator.fill("", new Locator.FillOptions().setTimeout(timeout));
        case HOVER -> locator.hover(new Locator.HoverOptions().setTimeout(timeout));
        case FIND_ELEMENT -> {
          if (!locator.isVisible()) {
            throw new BusinessException(ErrorCode.MONITOR_WEB_JOB_ELEMENT_NOT_FOUND, action.getIdentifier());
          }
        }
        case SELECT_FROM_DROPDOWN -> {
          String[] values = action.getValue().split(CommonConstants.DEFAULT_DELIMITER);
          locator.selectOption(values, new Locator.SelectOptionOptions().setTimeout(timeout));
        }
        case WAITING_FOR_ELEMENT -> page.waitForSelector(resolveSelector(action),
          new Page.WaitForSelectorOptions().setTimeout(Double.parseDouble(action.getValue()) * 1000));
        case SWITCH_FRAME -> {
          ElementHandle iframe = page.waitForSelector(resolveSelector(action),
            new Page.WaitForSelectorOptions().setTimeout(timeout));
          Frame frame = iframe.contentFrame();
          frame.waitForLoadState(LoadState.LOAD, new Frame.WaitForLoadStateOptions().setTimeout(timeout));
          currentFrame = frame;
        }
        case SWITCH_TO_DEFAULT_FRAME -> currentFrame = page.mainFrame();
        
        default -> throw new BusinessException(ErrorCode.MONITOR_WEB_JOB_ACTION_NOT_SUPPORTED, action.getActionType());
      }
    } catch (Exception e) {
      log.error("Error performing action [{}]: with error : {}", action, e.getMessage());
      throw new Exception(e);
    }
  }
  
  /**
   * Perform actions that do not require a DOM locator.
   * These include actions like waiting, handling JavaScript dialogs (alert, confirm, prompt),
   * and navigating to a URL.
   *
   * @param action the monitor action to execute
   * @param page the Playwright page instance
   * @param timeout timeout in milliseconds
   */
  private void performActionWithOutLocator(MonitorActionEntity action, Page page, int timeout)
      throws BusinessException {
    switch (action.getActionType()) {
      case WAIT -> page.waitForTimeout(Double.parseDouble(action.getValue()) * 1000);
      case ALERT_ACCEPT -> page.onceDialog(dialog -> {
        if ("prompt".equals(dialog.type())) {
          dialog.accept(action.getValue());
        } else {
          dialog.accept();
        }
      });
      case ALERT_DISMISS -> page.onceDialog(Dialog::dismiss);
      case GO_TO_URL -> page.navigate(action.getValue(), new Page.NavigateOptions().setTimeout(timeout));
      default -> throw new BusinessException(ErrorCode.MONITOR_WEB_JOB_ACTION_NOT_SUPPORTED, action.getActionType());
    }
  }
  
  /**
   * Pauses the thread for a specified number of seconds.
   *
   * @param seconds Number of seconds to wait.
   */
  private void waitInterval(int seconds) {
    if (seconds <= 0) {
      return;
    }
    try {
      Thread.sleep(seconds * 1000L);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }

  /**
   * Resolves a string selector based on the strategy and identifier from the action.
   * Useful for Playwright methods that require a selector string instead of a Locator object.
   *
   * @param action The action entity containing selector strategy and identifier.
   * @return The resolved selector string.
   */
  private String resolveSelector(MonitorActionEntity action) {
    return switch (action.getFindElementBy()) {
      case XPATH -> "xpath=" + action.getIdentifier();
      case CSS_SELECTOR -> action.getIdentifier();
      case ID -> "#" + action.getIdentifier();
      case NAME -> "[name=\"" + action.getIdentifier() + "\"]";
    };
  }

  /**
   * Resolves and returns the correct browser page based on the specified action type.
   *
   * @param actionType     The type of page-related action (e.g., switch to pop up, back to the main page, etc.).
   * @param currentPage    The currently active page.
   * @param context        The browser context used to manage pages.
   * @param mainPageRef    A reference to the main page (used for switching between popup and main).
   * @param timeoutSeconds The maximum wait time in seconds when waiting for a new page (used for popups).
   * @return The resolved Page object based on the action type.
   * @throws BusinessException if the action cannot be performed due to invalid page state or logic errors.
   */
  private Page resolveCurrentPageByActionType(ActionTypeEnum actionType, Page currentPage, BrowserContext context,
        AtomicReference<Page> mainPageRef, int timeoutSeconds) throws BusinessException {
    switch (actionType) {
      case SWITCH_TO_POPUP -> {
        mainPageRef.set(currentPage);
        BrowserContext.WaitForPageOptions options = new BrowserContext.WaitForPageOptions().setTimeout(timeoutSeconds);
        return context.waitForPage(options, () -> {});
      }
      
      case BACK_TO_MAIN -> {
        Page mainPage = mainPageRef.get();
        if (mainPage == null) {
          throw new BusinessException(ErrorCode.MONITOR_WEB_JOB_BACK_TO_MAIN_INVALID);
        }
        mainPage.bringToFront();
        return mainPage;
      }
      
      case CLOSE_POPUP -> {
        Page mainPage = mainPageRef.get();
        if (currentPage == null || currentPage == mainPage) {
          throw new BusinessException(ErrorCode.MONITOR_WEB_JOB_CLOSE_POPUP_INVALID);
        }
        safelyClosePage(currentPage);
        return mainPage;
      }
      
      default -> {
        return currentPage;
      }
    }
  }
  
  /**
   * Safely closes a Playwright Page instance if it's not already closed.
   * This method prevents exceptions from propagating in case of errors during closure.
   *
   * @param page the Playwright Page to close; may be null or already closed
   */
  private void safelyClosePage(Page page) {
    try {
      if (page != null && !page.isClosed()) {
        page.close();
      }
    } catch (Exception e) {
      log.warn("Failed to close page: {}", e.getMessage());
    }
  }
  
  /**
   * Cleanup session: cookies and storage.
   *
   * @param context the Playwright context to clean.
   */
  private void cleanupSession(BrowserContext context) {
    try {
      context.clearCookies();
      for (Page page : context.pages()) {
        if (!page.isClosed()) {
          page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }");
        }
      }
    } catch (Exception cleanupEx) {
      log.warn("Session cleanup failed: {}", cleanupEx.getMessage());
    }
  }
  
  /**
   * Replace variable placeholders in auth action values using actual values from variable store.
   * Performs a single variable lookup for all used variables.
   *
   * @param authActions authActions
   */
  private List<MonitorActionEntity> normalizeAuthActions(List<MonitorActionEntity> authActions) {
    // Collect all variable names from auth actions
    Set<String> allVariableNames = authActions.stream()
        .map(MonitorActionEntity::getValue)
        .filter(value -> !KanbanCommonUtil.isEmpty(value))
        .flatMap(value -> VariableUtils.getVariableNames(value).stream())
        .collect(Collectors.toSet());
    
    // Lookup all variable values once
    if (!KanbanCommonUtil.isEmpty(allVariableNames)) {
      Map<String, String> variableValueMap = variableService.findAllByNameIn(List.copyOf(allVariableNames)).stream()
          .collect(Collectors.toMap(
            VariableEntity::getName,
            VariableUtils::getVariableValueByEntity
          ));
      
      if (!KanbanCommonUtil.isEmpty(variableValueMap)) {
        // Replace variables inline
        for (MonitorActionEntity action : authActions) {
          String value = action.getValue();
          if (KanbanCommonUtil.isEmpty(value)) {
            continue;
          }
          
          Set<String> usedVariables = VariableUtils.getVariableNames(value);
          if (KanbanCommonUtil.isEmpty(usedVariables)) {
            continue;
          }
          
          Map<String, String> replacements = usedVariables.stream()
              .filter(variableValueMap::containsKey)
              .collect(Collectors.toMap(v -> v, variableValueMap::get));
          
          if (!KanbanCommonUtil.isEmpty(replacements)) {
            String replaced = VariableUtils.replaceVariablesSimple(value, replacements);
            action.setValue(replaced);
          }
        }
      }
    }
    return authActions;
  }
}