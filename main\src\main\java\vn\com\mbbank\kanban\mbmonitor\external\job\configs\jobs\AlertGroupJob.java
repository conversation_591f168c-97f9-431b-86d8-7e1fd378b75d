package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.handler.AlertGroupHandler;

/**
 * EmailJob for scheduler config.
 *
 * <AUTHOR>
 * @created_date 11/22/2024
 */
@RequiredArgsConstructor
@Component(JobNameConstants.ALERT_GROUP)
public class AlertGroupJob extends JobConfig {
  private static final Logger logger = LoggerFactory.getLogger(AlertGroupJob.class);
  private final AlertGroupHandler alertGroupHandler;
  private static final Long INTERVAL_TIME = 30 * 1000L;

  private static final Long ALERT_GROUP_LOCK_TIME_OUT = 5 * 60 * 1000L;

  @Override
  public void executeJob(JobExecutionContext context) {
    Long startTime = System.nanoTime();
    alertGroupHandler.group();
    Long endTime = System.nanoTime();
    logger.info("Alert group job run with " + (endTime - startTime) / 1_000_000 + " milliseconds");
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    Map<String, Long> configMap = new HashMap<>();
    configMap.put(JobNameConstants.ALERT_GROUP, INTERVAL_TIME);
    return configMap;
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.ALERT_GROUP;
  }

  @Override
  public Long getLockTimeoutMs() {
    return ALERT_GROUP_LOCK_TIME_OUT;
  }
}
