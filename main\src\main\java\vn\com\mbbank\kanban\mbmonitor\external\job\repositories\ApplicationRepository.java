package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;


/**
 * Repository table  Application.
 */
@Repository
public interface ApplicationRepository
    extends BaseSoftRepository<ApplicationEntity, String>, ApplicationRepositoryCustom {

  /**
   * find data by name and serviceId.
   *
   * @param applicationName applicationName
   * @param serviceId       serviceId
   * @return ApplicationEntity
   */
  Optional<ApplicationEntity> findFirstByNameIgnoreCaseAndServiceIdAndDeletedFalse(
      String applicationName,
      String serviceId);

  /**
   * Counts the number of ApplicationEntity objects with id of service.
   *
   * @param serviceId id of service.
   * @param deleted   status delete of service.
   * @return The number of ApplicationEntity objects with id of service.
   */

  long countByServiceIdAndDeleted(String serviceId, boolean deleted);

  /**
   * Counts the number of ApplicationEntity objects with the specified name.
   *
   * @param name      The name to count by.
   * @param serviceId id of service
   * @return The number of ApplicationEntity objects with the specified name.
   */
  long countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String name, String serviceId);

  /**
   * Counts the number of ApplicationEntity objects with the specified name and excluding the specified id.
   *
   * @param name      The name to count by.
   * @param id        The id to exclude.
   * @param serviceId id of service
   * @return The number of ApplicationEntity objects with the specified name and excluding the specified id.
   */
  long countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String id, String name,
                                                                  String serviceId);


  /**
   * Finds all applications by  service ID.
   *
   * @param serviceId service ID
   * @param deleted   status delete of application
   * @return a list of ApplicationResponseDto
   */

  List<ApplicationEntity> findAllByServiceIdAndDeleted(String serviceId, boolean deleted);

  /**
   * Get the next sequence id of APPLICATION_SEQ.
   *
   * @return The next sequence value
   */
  @Query(value = "SELECT APPLICATION_SEQ.nextval FROM dual", nativeQuery = true)
  long getNextSequenceValue();

  /**
   * find all application by serviceIds.
   *
   * @param serviceIds list Service ids.
   * @param deleted    deleted status
   * @return List of ApplicationEntity.
   */
  List<ApplicationEntity> findAllByServiceIdInAndDeleted(List<String> serviceIds, boolean deleted);

  /**
   * Find all application by serviceIds and applicationNames.
   *
   * @param names          names
   * @param applicationIds applicationIds
   * @param serviceIds     serviceIds
   * @return list application
   */
  @Query(value = """
        SELECT * 
          FROM APPLICATION application
          WHERE 
          application.SERVICE_ID IN (:serviceIds) AND (LOWER(application.NAME) IN (:names) 
          OR 
          application.ID IN (:applicationIds)) AND  application.DELETED = 0
      """,
      nativeQuery = true)
  List<ApplicationEntity> findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
      @Param("names") List<String> names, @Param("applicationIds") List<String> applicationIds,
      @Param("serviceIds") List<String> serviceIds);

  /**
   * find all application name by ids.
   *
   * @param ids list ids.
   * @return Set of ApplicationEntity.
   */
  @Query(value = "SELECT application.NAME FROM APPLICATION application WHERE application.ID IN (:ids)",
      nativeQuery = true)
  Set<String> findApplicationNameByIdIn(List<String> ids);
}
