package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ServicePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ServiceWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonServiceService;


/**
 * interface logic service.
 */
public interface ServiceService extends BaseSoftService<ServiceEntity, String>,
    CommonServiceService {

  /**
   * Find fist by name ignore.
   *
   * @param serviceNameSource serviceNameSource
   * @return ServiceEntity
   */
  Optional<ServiceEntity> findFirstByNameIgnoreCase(String serviceNameSource);

  /**
   * Find service by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list service
   */
  List<ServiceWithPriorityResponse> findServiceWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus);


  /**
   * Finds all service.
   *
   * @param paginationRequest the pagination request containing paging details
   * @return a paginated list of ServiceResponseDto
   */
  Page<ServiceEntity> findWithPaging(ServicePaginationRequest paginationRequest);

  /**
   * Generates an ID with a prefix 'S' followed by a sequence number
   * formatted to a length of 5 digits with leading zeros.
   *
   * @return A formatted ID string.
   */
  String generateId();

  /**
   * Retrieves a list of names from the `SERVICE` table where the IDs match the provided list.
   *
   * @param ids a list of IDs to match against the `ID` field.
   * @return a list of names corresponding to the provided IDs.
   */
  List<String> findAllNameByIdIn(List<String> ids);

  /**
   * find all service by id in.
   *
   * @param ids a list of IDs to match against the `ID` field.
   * @return a list service.
   */
  List<ServiceEntity> findAllByIdIn(List<String> ids);
  

  /**
   * Find all by name ignore or serviceIds.
   *
   * @param serviceNames serviceNames
   * @param serviceIds   serviceIds
   * @return list entity
   */
  List<ServiceEntity> findAllByNameIgnoreCaseInOrServiceIdIn(List<String> serviceNames,
                                                             List<String> serviceIds);

  /**
   * Executes the logic to export data as a file and returns it ,
   * allowing the file to be streamed directly to the client without loading the entire file into memory.
   *
   * @param request  The request object containing pagination and filter details used to retrieve the data
   *                 that needs to be exported.
   * @param userName user name.
   * @param filePath  path of file.
   * @return file strorage entity.
   */
  FileStorageEntity exportFile(ExportFileServiceRequest request, String userName, String filePath)
      throws IOException, BusinessException;

}
