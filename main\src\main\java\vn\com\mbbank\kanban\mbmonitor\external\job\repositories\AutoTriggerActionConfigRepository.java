package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;

/**
 * Repository interface for managing auto trigger action configuration data in the system.
 */
@Repository
public interface AutoTriggerActionConfigRepository
    extends JpaCommonRepository<AutoTriggerActionConfigEntity, String>, AutoTriggerActionConfigRepositoryCustom {

}
