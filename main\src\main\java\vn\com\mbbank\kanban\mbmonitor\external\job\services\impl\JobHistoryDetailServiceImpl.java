package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryDetailEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.JobHistoryDetailRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryDetailService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 12/17/2024
 */
@Service
@RequiredArgsConstructor
public class JobHistoryDetailServiceImpl extends BaseServiceImpl<JobHistoryDetailEntity, Long>
    implements JobHistoryDetailService {
  private final JobHistoryDetailRepository repository;

  @Override
  protected JpaCommonRepository<JobHistoryDetailEntity, Long> getRepository() {
    return repository;
  }
}
