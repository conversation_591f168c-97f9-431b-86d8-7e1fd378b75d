package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;

/**
 * Repository table AlertGroupConfigConditionRepository.
 */
@Repository
public interface AlertGroupConfigConditionRepository
    extends JpaCommonRepository<AlertGroupConfigConditionEntity, Long> {

  /**
   * find all AlertGroupConfigConditionEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return list of AlertGroupConfigConditionEntity
   */
  List<AlertGroupConfigConditionEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

  /**
   * delete all by alertGroupConfigId.
   *
   * @param alertGroupConfigId alertGroupConfigId
   */
  void deleteAllByAlertGroupConfigId(Long alertGroupConfigId);
}
