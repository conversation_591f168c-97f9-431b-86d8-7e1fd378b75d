package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.persistence.EntityManager;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.TelegramService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TelegramAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TelegramConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/25/2025
 */
class TelegramAlertConfigServiceImplTest {
  @Mock
  TelegramAlertConfigRepository telegramAlertConfigRepository;
  @Mock
  TelegramConfigService telegramConfigService;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  TelegramService telegramService;
  @Mock
  Logger logger;
  @Mock
  EntityManager entityManager;
  @InjectMocks
  @Spy
  TelegramAlertConfigServiceImpl telegramAlertConfigServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    ReflectionTestUtils.setField(telegramAlertConfigServiceImpl, "alertSendLimit", 600);
  }

  @Test
  void getRepository_success() {
    JpaCommonRepository<TelegramAlertConfigEntity, String> result =
        telegramAlertConfigServiceImpl.getRepository();
    Assertions.assertEquals(telegramAlertConfigRepository, result);
  }

  @Test
  void sendMessageToTelegram_case_alert_null() {
    telegramAlertConfigServiceImpl.sendMessageToTelegram(null);
  }

  @Test
  void sendMessageToTelegram_case_alert_expired() {
    var alert = new AlertEntity();
    alert.setCreatedDate(new Date(new Date().getTime() - 11 * 60 * 1000));
    alert.setApplicationId("123");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
  }


  @Test
  void  sendMessageToTelegram_case_config_inactive() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(false);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.FALSE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setApplicationId("123");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
  }

  @Test
  void sendMessageToTelegram_case_config_null() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(false);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.FALSE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(null);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setApplicationId("123");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
  }


  @Test
  void sendMessageToTelegram_case_config_service() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.TRUE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setApplicationId("123");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
    verify(telegramService).sendMessage(any(), any(), any());
  }

  @Test
  void sendMessageToTelegram_case_config_service_inactive() {
    ReflectionTestUtils.setField(telegramAlertConfigServiceImpl, "alertSendLimit", 600);
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.FALSE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setApplicationId("123");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);

    verify(telegramConfigService).findFirstByType(any());
  }


  @Test
  void sendMessageToTelegram_case_config_application() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.TRUE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
    verify(telegramService).sendMessage(any(), any(), any());
  }

  @Test
  void sendMessageToTelegram_case_config_application_inactive() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.FALSE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
    verify(telegramConfigService).findFirstByType(any());
  }

  @Test
  void sendMessageToTelegram_case_default() {
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.TRUE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);
    ReflectionTestUtils.setField(telegramAlertConfigServiceImpl, "alertSendLimit", 300);
    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setServiceId("aa");
    alert.setApplicationId("aa");
    telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
    verify(telegramService).sendMessage(any(), any(), any());
  }

  @Test
  void sendMessageToTelegram_case_exception() {
    ReflectionTestUtils.setField(telegramAlertConfigServiceImpl, "alertSendLimit", 600);
    var telegramConfig = new TelegramConfigEntity();
    telegramConfig.setActive(true);
    var alertConfig = new TelegramAlertConfigEntity();
    alertConfig.setActive(Boolean.TRUE);
    when(telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(anyString(), any(),
        any())).thenReturn(
        List.of(alertConfig));
    when(telegramConfigService.findFirstByType(any())).thenReturn(telegramConfig);
    ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.BAD_GATEWAY);
    when(telegramService.sendMessage(any(), any(), any())).thenReturn(response);

    var alert = new AlertEntity();
    alert.setCreatedDate(new Date());
    alert.setServiceId("aa");
    alert.setApplicationId("aa");
    assertThrows(BusinessRuntimeException.class, () -> {
      telegramAlertConfigServiceImpl.sendMessageToTelegram(alert);
    });
  }

  @Test
  void kafkaExecute_success() throws Exception {
    AlertEntity alertEntity = new AlertBaseModel();
    alertEntity.setId(822L);
    alertEntity.setContent("abc");
    alertEntity.setRecipient("monitor");
    alertEntity.setAlertPriorityConfigId(972L);
    alertEntity.setStatus(AlertStatusEnum.NEW);
    alertEntity.setServiceId("monitor");
    alertEntity.setApplicationId("CMDB");
    alertEntity.setAlertGroupId(536L);
    alertEntity.setSource(AlertSourceTypeEnum.WEBHOOK);
    alertEntity.setClosedDate(new Date());
    alertEntity.setClosedBy("abc");
    alertEntity.setClosedDuration(195L);
    alertEntity.setCreatedDate(new Date());
    alertEntity.setCreatedBy("kanban");
    alertEntity.setModifiedBy("abc");
    alertEntity.setModifiedDate(new Date());

    BaseKafkaModel<Object> data = new BaseKafkaModel<>();
    data.setValue(KanbanMapperUtils.objectToJson(alertEntity));
    data.setType(KafkaTypeEnum.TELEGRAM);
    telegramAlertConfigServiceImpl.kafkaExecute(data);
    verify(telegramAlertConfigServiceImpl).sendMessageToTelegram(any());

  }


}
