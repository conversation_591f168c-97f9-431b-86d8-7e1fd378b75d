package vn.com.mbbank.kanban.mbmonitor.common.utils;

import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpHeaders;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;

import static org.junit.jupiter.api.Assertions.*;

public class ExceptionUtilsTest {

  @Test
  void fromHttpClientError_success_shouldReturnMatchedErrorCode() {
    // Given
    String errorJson = "{ \"errorCode\": \"" + ErrorCode.EXECUTION_RUN_FAILED.getCode() + "\" }";
    HttpClientErrorException ex = new HttpClientErrorException(
            HttpStatus.BAD_REQUEST,
            "Bad Request",
            errorJson.getBytes(),
            null
    );

    // When & Then
    BusinessException thrown = assertThrows(BusinessException.class, () -> {
      ExceptionUtils.fromHttpClientError(ex);
    });

    assertEquals(ErrorCode.EXECUTION_RUN_FAILED.getCode(), thrown.getCode());
  }

  @Test
  void fromHttpClientError_success_shouldReturnDefaultErrorCode_whenJsonInvalid() {
    // Given
    String invalidJson = "This is not JSON";
    HttpClientErrorException ex = new HttpClientErrorException(
            HttpStatus.BAD_REQUEST,
            "Bad Request",
            invalidJson.getBytes(),
            null
    );

    // When & Then
    BusinessException thrown = assertThrows(BusinessException.class, () -> {
      ExceptionUtils.fromHttpClientError(ex);
    });

    assertEquals(HttpStatus.BAD_REQUEST, thrown.getHttpStatus());
  }

  @Test
  void fromHttpClientError_failure_shouldReturnDefaultErrorCode_whenCodeNotMatchEnum() {
    // Given
    String errorJson = "{ \"errorCode\": \"UNKNOWN_ERROR_CODE\" }";
    HttpClientErrorException ex = new HttpClientErrorException(
            HttpStatus.BAD_REQUEST,
            "Bad Request",
            errorJson.getBytes(),
            null
    );

    // When & Then
    BusinessException thrown = assertThrows(BusinessException.class, () -> {
      ExceptionUtils.fromHttpClientError(ex);
    });

    assertEquals(HttpStatus.BAD_REQUEST, thrown.getHttpStatus());
  }
}
