package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ModifyAlertConfigModifyRepository;

@ExtendWith(MockitoExtension.class)
public class ModifyAlertConfigModifyServiceImplTest {

  @Mock
  private ModifyAlertConfigModifyRepository modifyAlertConfigModifyRepository;

  @InjectMocks
  private ModifyAlertConfigModifyServiceImpl modifyAlertConfigModifyServiceImpl;

  private ModifyAlertConfigModifyEntity entity1;
  private ModifyAlertConfigModifyEntity entity2;

  @BeforeEach
  public void setUp() {
    entity1 = new ModifyAlertConfigModifyEntity();
    entity1.setId(1L);

    entity2 = new ModifyAlertConfigModifyEntity();
    entity2.setId(2L);
  }

  @Test
  public void findAllByModifyAlertConfigId_success() {
    Long configId = 1L;
    List<ModifyAlertConfigModifyEntity> expectedList = List.of(entity1);

    when(modifyAlertConfigModifyRepository.findAllByModifyAlertConfigId(configId))
        .thenReturn(expectedList);

    List<ModifyAlertConfigModifyEntity> result = modifyAlertConfigModifyServiceImpl.findAllByModifyAlertConfigId(configId);
    assertEquals(expectedList, result, "Kết quả trả về phải khớp với danh sách expected");
  }

  @Test
  public void findAllByModifyAlertConfigIdIn_success() {
    List<Long> configIds = Arrays.asList(1L, 2L);
    List<ModifyAlertConfigModifyEntity> expectedList = List.of(entity1, entity2);

    when(modifyAlertConfigModifyRepository.findAllByModifyAlertConfigIdIn(configIds))
        .thenReturn(expectedList);

    List<ModifyAlertConfigModifyEntity> result = modifyAlertConfigModifyServiceImpl.findAllByModifyAlertConfigIdIn(configIds);
    assertEquals(expectedList, result, "Kết quả trả về phải khớp với danh sách expected");
  }
  @Test
  void getRepository_success(){
    assertEquals(modifyAlertConfigModifyRepository,modifyAlertConfigModifyServiceImpl.getRepository());
  }
}
