package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.mockito.ArgumentMatchers.anyString;

import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigConditionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;

@ExtendWith({MockitoExtension.class})
class AlertGroupConfigServiceImplTest {
  @Mock
  AlertGroupConfigRepository alertGroupConfigRepository;
  @Mock
  AlertGroupConfigConditionService alertGroupConfigConditionService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @InjectMocks
  AlertGroupConfigServiceImpl alertGroupConfigServiceImpl;

  @Test
  void getRepository_success() {
    BaseSoftRepository<AlertGroupConfigEntity, Long> result =
        alertGroupConfigServiceImpl.getRepository();
    Assertions.assertEquals(alertGroupConfigRepository, result);
  }

  @Test
  void findAllWithDeleted_success_caseWithDeleted() {
    Mockito.when(alertGroupConfigRepository.findAllByDeletedAndSearch(Mockito.isNull(), anyString()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllWithDeletedAndSearch(true, "");
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void findAllWithDeleted_success_caseWithoutDeleted() {
    Mockito.when(alertGroupConfigRepository.findAllByDeletedAndSearch(Mockito.anyBoolean(), anyString()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllWithDeletedAndSearch(false, "");
    Assertions.assertEquals(0, res.size());
  }


  @Test
  void findAllByServiceIdAndApplicationIdAndDeletedAndActive_success() {
    Mockito.when(alertGroupConfigRepository.findAllByServiceIdAndApplicationIdAndDeletedAndActive(Mockito.anyString(),
        Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllByServiceIdAndApplicationIdAndDeletedAndActive("a", "a", true, true);
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void findAllByDeletedAndActive_success() {
    Mockito.when(alertGroupConfigRepository.findAllByDeletedAndActive(Mockito.anyBoolean(), Mockito.anyBoolean()))
        .thenReturn(List.of());
    var res = alertGroupConfigServiceImpl.findAllByDeletedAndActive(true, true);
    Assertions.assertEquals(0, res.size());
  }
}
