package vn.com.mbbank.kanban.mbmonitor.external.job.configs;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailIntervalTimeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.EmailJob;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CollectEmailSchedulerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.RedisLockService;

@ExtendWith(MockitoExtension.class)
public class EmailJobTest {
  @Mock
  JobConfig jobConfig;
  @Mock
  private EmailConfigService emailConfigService;

  @Mock
  private CollectEmailSchedulerService collectEmailSchedulerService;
  @Mock
  private Scheduler scheduler;
  @Mock
  private JobExecutionContext jobExecutionContext;

  @Mock
  private JobDetail jobDetail;
  @Mock
  private RedisLockService redisLockService;
  @Mock
  private JobKey jobKey;

  @InjectMocks
  private EmailJob emailJob;

  @BeforeEach
  public void setUp() throws IllegalAccessException, NoSuchFieldException {
    Field schedulerField = jobConfig.getClass().getSuperclass().getDeclaredField("scheduler");
    schedulerField.setAccessible(true);
    schedulerField.set(emailJob, scheduler);
  }

  @Test
  public void executeJob_success() throws BusinessException, SchedulerException {
    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
    when(jobDetail.getKey()).thenReturn(jobKey);
    when(jobKey.getName()).thenReturn("123");
    doNothing().when(collectEmailSchedulerService).fetchAndProcessEmails(anyLong());
    emailJob.executeJob(jobExecutionContext);
    verify(collectEmailSchedulerService).fetchAndProcessEmails(123L);
  }

//  @Test
//  public void executeJob_throwsBusinessException() throws BusinessException, SchedulerException {
//    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
//    when(jobDetail.getKey()).thenReturn(jobKey);
//    when(jobKey.getName()).thenReturn("123");
//    doThrow(new BusinessException(ErrorCode.CUSTOM_MESSAGE)).when(collectEmailSchedulerService)
//        .fetchAndProcessEmails(any());
//    emailJob.executeJob(jobExecutionContext);
//  }

  @Test
  public void scheduleJobIfNotExists_success() throws SchedulerException {
    String jobName = "testJob";
    Long intervalTime = 10000L;
    when(scheduler.checkExists((JobKey) any())).thenReturn(false);
    emailJob.scheduleJobIfNotExists(jobName, intervalTime);
    verify(scheduler, times(1)).checkExists((JobKey) any());
  }

  @Test
  public void rescheduleJob_success() throws SchedulerException, BusinessException {
    String jobName = "testJob";
    String jobGroup = "testGroup";
    Long intervalTime = 10000L;
    emailJob.rescheduleJob(jobName, intervalTime);
    verify(collectEmailSchedulerService, never()).fetchAndProcessEmails(any());
  }

//  @Test
//  public void initScheduler_success_isSchedulerInitialized() throws SchedulerException {
//    EmailConfigEntity config1 = new EmailConfigEntity();
//    config1.setId(1L);
//    config1.setIntervalTime(CollectEmailIntervalTimeEnum.FIFTEEN_SECONDS);
//    config1.setProtocolType(EmailProtocolTypeEnum.IMAP);
//
//    EmailConfigEntity config2 = new EmailConfigEntity();
//    config2.setId(2L);
//    config2.setIntervalTime(CollectEmailIntervalTimeEnum.FIFTEEN_SECONDS);
//    config2.setProtocolType(EmailProtocolTypeEnum.EXCHANGE);
//
//    when(emailConfigService.findAllByProtocolTypeIn(anyList())).thenReturn(
//        List.of(config1, config2));
//
//    emailJob.initScheduler();
//    verify(emailConfigService, times(1)).findAllByProtocolTypeIn(anyList());
//  }

  @Test
  public void initScheduler_success() throws SchedulerException {
    EmailConfigEntity config1 = new EmailConfigEntity();
    config1.setId(1L);
    config1.setIntervalTime(15L);
    config1.setProtocolType(EmailProtocolTypeEnum.IMAP);

    EmailConfigEntity config2 = new EmailConfigEntity();
    config2.setId(2L);
    config2.setIntervalTime(15L);
    config2.setProtocolType(EmailProtocolTypeEnum.EXCHANGE);
    emailJob.initScheduler();
  }
}
