# Start with a base image containing Java runtime 
FROM k8sdev.mbbank.com.vn/common/eclipse-temurin:21-jdk-jammy-playwright

# Set workdir pointing to /app 
WORKDIR /app 

# Add argument spring profile
ARG ENV_PROFILE
ENV ENV_PROFILE=$ENV_PROFILE

# Add the application's jar to the container 
ADD main/target/*.jar app.jar 

# Create a group and user 
#Fix alpine.
RUN addgroup --system --gid 10000 appadmin 
RUN adduser --system --uid 10000 --group appadmin 
RUN chown -R appadmin:appadmin /app 
RUN mkdir -p /home/<USER>/logs/csp
RUN chown -R appadmin:appadmin /home/<USER>

# Set the default user. 
USER appadmin 

# Run the jar file
VOLUME /app /tmp /home/<USER>/