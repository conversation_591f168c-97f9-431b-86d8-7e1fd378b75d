package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigRepositoryCustom;

/**
 * AlertGroupConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AlertGroupConfigRepositoryCustomImpl
    implements AlertGroupConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(String serviceId,
                                                                                            String applicationId,
                                                                                            Boolean deleted,
                                                                                            Boolean active) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT_GROUP_CONFIG alertGroupConfig
        WHERE 1=1
        """)
        .append(buildServiceIdsLike(serviceId), LikeMatcher.CONTAINING)
        .append(buildApplicationIdsLike(applicationId), LikeMatcher.CONTAINING)
        .append(buildDeletedEqual(deleted))
        .append(buildActiveEqual(active));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupConfigEntity.class);
  }

  @Override
  public List<AlertGroupConfigEntity> findAllByDeletedAndSearch(Boolean deleted, String search) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT_GROUP_CONFIG alertGroupConfig
        WHERE 1=1
        """)
        .append(buildDeletedEqual(deleted))
        .append(buildNameOrDescriptionLike(search));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupConfigEntity.class);
  }

  private PrepareQuery buildNameOrDescriptionLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery(" AND (")
        .append(new PrepareQuery("alertGroupConfig.NAME LIKE :search", "search", search), LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.DESCRIPTION LIKE :search", "search", search), LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.ALERT_OUTPUT LIKE :search", "search", search),
            LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.TYPE LIKE :search", "search", search), LikeMatcher.CONTAINING)
        .append(")");
  }

  private PrepareQuery buildServiceIdsLike(String serviceId) {
    if (StringUtils.isBlank(serviceId)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.SERVICE_IDS LIKE :serviceId ",
        Map.of("serviceId", serviceId));
  }

  private PrepareQuery buildApplicationIdsLike(String applicationId) {
    if (StringUtils.isBlank(applicationId)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.APPLICATION_IDS LIKE :applicationId ",
        Map.of("applicationId", applicationId));
  }

  private PrepareQuery buildDeletedEqual(Boolean deleted) {
    if (Objects.isNull(deleted)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.DELETED = :deleted ",
        Map.of("deleted", deleted));
  }

  private PrepareQuery buildActiveEqual(Boolean active) {
    if (Objects.isNull(active)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.ACTIVE = :active ",
        Map.of("active", active ? 1 : 0));
  }
}
