package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * interface logic Variable.
 */
public interface VariableService extends BaseService<VariableEntity, String> {


  /**
   * find variable by names.
   *
   * @param names Variable name
   * @return list of variable
   */
  List<VariableEntity> findAllByNameIn(List<String> names);
}