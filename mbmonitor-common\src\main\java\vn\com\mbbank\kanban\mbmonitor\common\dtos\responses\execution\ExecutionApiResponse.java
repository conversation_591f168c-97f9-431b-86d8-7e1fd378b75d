package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;

/**
 * ExecutionApiResponse.
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class ExecutionApiResponse {
  HttpStatus status;
  Integer statusCode;
  String statusText;
  String contentType;
  long durationMillis;
  String body;
}
