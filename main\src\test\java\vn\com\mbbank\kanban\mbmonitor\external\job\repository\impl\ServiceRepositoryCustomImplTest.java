package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.ServiceRepositoryCustomImpl;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class})
public class ServiceRepositoryCustomImplTest {

  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;

  @InjectMocks
  ServiceRepositoryCustomImpl customServiceRepository;


  @Test
  void findServiceWithPriorityByAlertStatus_success() {
    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), any())).thenReturn(
        Collections.emptyList());
    var res = customServiceRepository.findServiceWithPriorityByAlertGroupStatus(
        AlertGroupStatusEnum.CLOSE);
    assertEquals(res.size(), 0);
  }

  @Test
  void buildQueryStatusEq_success_statusNull() {
    var result =
        ReflectionTestUtils.invokeMethod(customServiceRepository, "buildQueryStatusEq",
            new Object[] {null});
    assertNull(result);
  }

  @Test
  void buildQueryStatusEq_success_statusNonNull() {
    var result =
        ReflectionTestUtils.invokeMethod(customServiceRepository, "buildQueryStatusEq",
            AlertGroupStatusEnum.NEW);
    assertNotNull(result);
  }

}
