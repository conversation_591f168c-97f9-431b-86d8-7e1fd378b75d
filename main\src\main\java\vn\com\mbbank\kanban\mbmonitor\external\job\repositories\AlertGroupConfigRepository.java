package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;

/**
 * Repository table AlertGroupConfigRepository.
 */
@Repository
public interface AlertGroupConfigRepository
    extends BaseSoftRepository<AlertGroupConfigEntity, Long>, AlertGroupConfigRepositoryCustom {

  /**
   * find all AlertGroupConfigEntity By deleted.
   *
   * @param deleted option to fetch deleted item
   * @return list of AlertGroupConfigEntity
   */
  List<AlertGroupConfigEntity> findAllByDeleted(Boolean deleted);

  /**
   * check alertGroupConfig existed by id, name and deleted status.
   *
   * @param id      alertGroupConfigId
   * @param name    alertGroupConfig name
   * @param deleted deleted option
   * @return existed or not
   */
  boolean existsByIdNotAndNameAndDeleted(Long id, String name, Boolean deleted);

  /**
   * check alertGroupConfig existed by name and deleted status.
   *
   * @param name    alertGroupConfig name
   * @param deleted deleted option
   * @return existed or not
   */
  boolean existsByNameAndDeleted(String name, Boolean deleted);

  /**
   * get next position.
   *
   * @return next position.
   */
  @Query(value = "SELECT ALERT_GROUP_CONFIG_POSITION_SEQ.nextval FROM dual", nativeQuery = true)
  int getNextPositionValue();

  /**
   * find all alert group config by deleted and active.
   *
   * @param deleted deleted status
   * @param active  active status
   * @return next position.
   */
  List<AlertGroupConfigEntity> findAllByDeletedAndActive(Boolean deleted, Boolean active);

  /**
   * get all config in range.
   *
   * @param fromPosition a position.
   * @param toPosition   a position.
   * @return a list of config with position in range.
   */
  List<AlertGroupConfigEntity> findAllByPositionBetween(Integer fromPosition, Integer toPosition);

}
