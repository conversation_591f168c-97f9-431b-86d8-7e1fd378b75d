package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import jakarta.mail.Session;
import jakarta.mail.Store;
import java.util.Properties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolSecurityTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.EmailConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailService;


/**
 * Implementation of the MailServiceInterface to handle email operations using SMTP, IMAP, and Exchange protocols.
 */
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {
  @Override
  public Store connectImapServer(EmailConfigModel emailConfig) throws BusinessException {

    Properties properties = configureImapProperties(emailConfig);
    Session emailSession = Session.getInstance(properties);
    try {
      var username = emailConfig.getEmail().split("@")[0];
      Store store = emailSession.getStore(EmailConfigConstants.IMAP_PROTOCOL);

      store.connect(emailConfig.getHost(), username, emailConfig.getPassword());
      return store;
    } catch (Exception e) {
      throw new BusinessException(ErrorCode.EMAIL_IMAP_CONNECTION_FAIL);
    }
  }

  public Properties configureImapProperties(EmailConfigModel emailConfig) {
    Properties props = new Properties();
    props.put(EmailConfigConstants.KEY_TRANSPORT_PROTOCOL, EmailConfigConstants.IMAP_PROTOCOL);
    props.put(EmailConfigConstants.KEY_IMAP_CONNECTION_TIMEOUT, EmailConfigConstants.VALUE_IMAP_CONNECTION_TIMEOUT);
    props.put(EmailConfigConstants.KEY_IMAP_TIMEOUT, EmailConfigConstants.VALUE_IMAP_TIMEOUT);
    if (emailConfig.getSecurityType() == EmailProtocolSecurityTypeEnum.SSL_TLS) {
      props.put(EmailConfigConstants.KEY_IMAP_SSL_ENABLE, Boolean.TRUE.toString());
    }
    return props;
  }
}
