package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import org.quartz.SchedulerException;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.services.BaseCollectAlertService;

/**
 * Service interface for managing and scheduling email collection tasks based on configuration settings.
 */
public interface CollectEmailSchedulerService extends BaseCollectAlertService {

  /**
   * Performs the email fetching and processing operations for a given configuration.
   * This method is typically run as part of a scheduled task.
   *
   * @param emailConfigId the id of email configuration entity containing settings for the email collection task.
   *                      Must not be null.
   */
  void fetchAndProcessEmails(Long emailConfigId) throws BusinessException, SchedulerException;
}
