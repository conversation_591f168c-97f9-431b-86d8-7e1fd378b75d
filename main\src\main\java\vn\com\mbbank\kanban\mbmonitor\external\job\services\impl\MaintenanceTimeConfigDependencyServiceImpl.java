package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.MaintenanceTimeConfigDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigDependencyService;

@Service
@RequiredArgsConstructor
public class MaintenanceTimeConfigDependencyServiceImpl
    extends BaseServiceImpl<MaintenanceTimeConfigDependencyEntity, Long>
    implements MaintenanceTimeConfigDependencyService {

  private final MaintenanceTimeConfigDependencyRepository maintenanceTimeConfigDependencyRepository;

  @Override
  protected JpaCommonRepository<MaintenanceTimeConfigDependencyEntity, Long> getRepository() {
    return maintenanceTimeConfigDependencyRepository;
  }


  @Override
  public List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigId(Long alertGroupConfigId) {
    return maintenanceTimeConfigDependencyRepository.findAllByMaintenanceTimeConfigId(alertGroupConfigId);
  }

  @Override
  public List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigIdIn(
      List<Long> maintenanceTimeConfigIds) {
    return maintenanceTimeConfigDependencyRepository.findAllByMaintenanceTimeConfigIdIn(maintenanceTimeConfigIds);
  }
}
