package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@Repository
public interface DatabaseConnectionRepository extends
    JpaCommonRepository<DatabaseConnectionEntity, Long> {
  /**
   * set active database.
   *
   * @param id     id
   * @param active active
   * @return total row
   */
  @Transactional
  @Modifying
  @Query(value = "UPDATE DATABASE_CONNECTION SET IS_ACTIVE = :active WHERE id = :id", nativeQuery = true)
  int setActiveById(Long id, boolean active);

  /**
   * check exists by Name and id not.
   *
   * @param name Name
   * @param id   id
   * @return true/false
   */
  boolean existsByNameAndIdNot(String name, Long id);

  /**
   * check exists by Name.
   *
   * @param name Name
   * @return true/false
   */
  boolean existsByName(String name);

}
