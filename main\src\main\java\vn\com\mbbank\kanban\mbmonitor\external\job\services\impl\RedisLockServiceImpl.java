package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;


import java.util.Date;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.RedisLockService;

@Service
@RequiredArgsConstructor
public class RedisLockServiceImpl implements RedisLockService {

  private static final Logger logger = LoggerFactory.getLogger(RedisLockServiceImpl.class);
  private static final Long LOCK_EXPIRY_SECONDS = 10L;

  private final RedisAdapter redisAdapter;

  @Override
  public boolean tryLock(String key) {
    return tryLock(key, LOCK_EXPIRY_SECONDS);
  }

  @Override
  public boolean tryLock(String key, Long timeout) {
    boolean lockAcquired =
        Boolean.TRUE.equals(
            redisAdapter.getRedisTemplate().opsForValue()
                .setIfAbsent(key, "LOCKED", timeout, TimeUnit.MILLISECONDS));
    if (lockAcquired) {
      logger.info("Lock acquired for key: {} {}", key, DateUtils.formatDate(new Date()));
    } else {
      logger.warn("Failed to acquire lock for key: {} {}", key, DateUtils.formatDate(new Date()));
    }
    return lockAcquired;
  }

  @Override
  public void unlock(String key) {
    boolean deleted = Boolean.TRUE.equals(redisAdapter.getRedisTemplate().delete(key));
    if (deleted) {
      logger.info("Lock released for key: {} {}", key, DateUtils.formatDate(new Date()));
    } else {
      logger.warn("Failed to release lock for key: {} {}", key, DateUtils.formatDate(new Date()));
    }
  }
}
