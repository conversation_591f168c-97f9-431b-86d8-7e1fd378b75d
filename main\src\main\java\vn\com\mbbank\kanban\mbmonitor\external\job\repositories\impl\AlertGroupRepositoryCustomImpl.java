package vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupRepositoryCustom;

/**
 * AlertGroupRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AlertGroupRepositoryCustomImpl implements AlertGroupRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<String> findAlertRecipientByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    var query = new PrepareQuery("""
            SELECT DISTINCT alert.RECIPIENT AS recipient
            FROM ALERT alert
            JOIN ALERT_GROUP alertGroup
                on alertGroup.PRIMARY_ALERT_ID = alert.ID
            WHERE alert.STATUS = :status
            AND alert.RECIPIENT IS NOT NULL
        """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), Map.of("status", alertGroupStatus.name()), String.class);
  }

}
