package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import java.util.Optional;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;

/**
 * interface logic AlertGroupService.
 */
public interface AlertGroupService extends BaseService<AlertGroupEntity, Long> {

  /**
   * Find alert group by group config id and status.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @param status             status
   * @return list of alert group
   */
  Optional<AlertGroupEntity> findByAlertGroupConfigIdAndStatus(Long alertGroupConfigId, AlertGroupStatusEnum status);

  /**
   * Find alert group by alert group config id and match value and status.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @param matchValues        list of match value
   * @param status             status
   * @return list of alert group
   */
  List<AlertGroupEntity> findByAlertGroupConfigIdAndMatchValueInAndStatus(Long alertGroupConfigId,
                                                                          List<String> matchValues,
                                                                          AlertGroupStatusEnum status);

  /**
   * Find alert recipients by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list recipient
   */
  List<String> findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum alertGroupStatus);

}
