package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ApplicationWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;


/**
 * Custom Repo table Application.
 */
public interface ApplicationRepositoryCustom {

  /**
   * Finds all applications by a list of service IDs.
   *
   * @param applicationPaginationRequest the pagination request containing paging details
   *                                     and the list of service IDs to filter applications
   * @return a paginated list of ApplicationResponseDto
   */
  Page<ApplicationResponse> findAll(ApplicationPaginationRequest applicationPaginationRequest);

  /**
   * Find application by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list Application
   */
  List<ApplicationWithPriorityModel> findApplicationWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus);

  /**
   * Find application by id.
   *
   * @param id applicationId
   * @return ApplicationResponse
   */
  ApplicationResponse findApplicationById(String id);

  /**
   * Find application by list applicationId.
   *
   * @param ids applicationIds
   * @return ApplicationResponse
   */
  List<ApplicationResponse> findAllByIdIn(List<String> ids);
}
