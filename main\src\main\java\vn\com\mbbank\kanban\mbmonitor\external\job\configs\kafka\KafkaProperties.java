package vn.com.mbbank.kanban.mbmonitor.external.job.configs.kafka;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * KafkaConfigProperties for scheduler config.
 */
@Configuration
@Getter
@Setter
public class KafkaProperties {
  @Value("${kanban.kafka.topic.jobs}")
  private String topicJob;
  @Value("${kanban.kafka.key}")
  private String key;
}
