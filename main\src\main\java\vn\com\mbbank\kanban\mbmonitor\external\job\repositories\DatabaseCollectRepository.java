package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
public interface DatabaseCollectRepository extends
    JpaCommonRepository<DatabaseCollectEntity, Long> {
  /**
   * check name exists.
   *
   * @param name name
   * @return true/false
   */
  boolean existsByName(String name);

  /**
   * check name exist by not ID and name.
   *
   * @param id   id
   * @param name name
   * @return true/false
   */
  boolean existsByIdNotAndName(Long id, String name);

  /**
   * set active database.
   *
   * @param id     id
   * @param active active
   * @return total row
   */
  @Transactional
  @Modifying
  @Query(value = "UPDATE DATABASE_COLLECT SET IS_ACTIVE = :active WHERE id = :id", nativeQuery = true)
  int setActiveById(Long id, boolean active);

  /**
   * find all config active.
   *
   * @return lst
   */
  List<DatabaseCollectEntity> findAllByIsActiveTrue();
}
