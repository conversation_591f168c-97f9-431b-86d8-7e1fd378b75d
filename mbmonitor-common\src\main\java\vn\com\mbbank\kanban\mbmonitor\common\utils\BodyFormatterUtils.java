package vn.com.mbbank.kanban.mbmonitor.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.io.StringReader;
import java.io.StringWriter;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.InputSource;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiContentEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 24/7/2025
 */
public class BodyFormatterUtils {
  private static final ObjectMapper JSON_MAPPER = new ObjectMapper()
          .enable(SerializationFeature.INDENT_OUTPUT);

  private static final Logger logger = LoggerFactory.getLogger(BodyFormatterUtils.class);

  /**
   * Formats a raw string body based on its content type.
   *
   * <p>This method attempts to pretty-print the input {@code rawBody} if the {@code contentType}
   * indicates that the data is JSON, XML, or HTML. For unsupported or unknown content types, or
   * in case of any exception during formatting, it returns the original raw string.</p>
   *
   * @param rawBody     the raw string content to format
   * @param contentType the MIME type of the content (e.g., "application/json", "application/xml")
   * @return the formatted (pretty-printed) version of the input string if recognized;
   *         otherwise, returns the original raw string
   */
  public static String format(String rawBody, String contentType) {
    if (KanbanCommonUtil.isEmpty(rawBody)) {
      return "";
    }

    try {
      if (contentType == null) {
        return rawBody;
      }
      String lowered = contentType.toLowerCase();

      if (lowered.contains(ExecutionApiContentEnum.JSON.getValue())) {
        return formatJson(rawBody);
      } else if (lowered.contains(ExecutionApiContentEnum.XML.getValue())
              || lowered.contains(ExecutionApiContentEnum.TEXT_XML.getValue())) {
        return formatXml(rawBody);
      } else if (lowered.contains(ExecutionApiContentEnum.HTML.getValue())) {
        return formatHtml(rawBody);
      } else {
        return rawBody;
      }
    } catch (Exception e) {
      logger.info("Can not format raw body with content type {}", contentType);
      return rawBody;
    }
  }

  private static String formatJson(String json) throws Exception {
    Object obj = JSON_MAPPER.readValue(json, Object.class);
    return JSON_MAPPER.writerWithDefaultPrettyPrinter()
            .writeValueAsString(obj);
  }

  private static String formatXml(String xml) throws Exception {
    org.w3c.dom.Document doc = DocumentBuilderFactory.newInstance()
            .newDocumentBuilder().parse(new InputSource(new StringReader(xml)));
    doc.normalizeDocument();

    Transformer transformer = TransformerFactory.newInstance().newTransformer();
    transformer.setOutputProperty(OutputKeys.INDENT, "yes");
    transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

    StringWriter writer = new StringWriter();
    transformer.transform(new DOMSource(doc), new StreamResult(writer));
    return writer.toString();
  }

  private static String formatHtml(String html) {
    Document doc = Jsoup.parse(html);
    doc.outputSettings().prettyPrint(true).indentAmount(2);
    return doc.outerHtml();
  }
}
