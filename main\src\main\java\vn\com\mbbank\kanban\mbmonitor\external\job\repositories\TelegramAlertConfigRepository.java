package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Repository
public interface TelegramAlertConfigRepository
    extends JpaCommonRepository<TelegramAlertConfigEntity, String> {
  /**
   * Find all config by telegram config id exist serviceId or application.
   *
   * @param telegramConfigId telegramConfigId
   * @param serviceId        serviceId
   * @param applicationId    applicationId
   * @return list config
   */
  @Query("""
      SELECT 
        alertConfig FROM TelegramAlertConfigEntity alertConfig 
      WHERE 
        alertConfig.telegramConfigId = :telegramConfigId 
        AND (alertConfig.serviceId = :serviceId OR alertConfig.applicationId IN :applicationId)
      """)
  List<TelegramAlertConfigEntity> findByTelegramConfigAndServiceOrApp(
      @Param("telegramConfigId") String telegramConfigId,
      @Param("serviceId") String serviceId,
      @Param("applicationId") String applicationId
  );
}
