package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.FORMAT_YYYY_MM_DD_T_HH_MM_SS;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.convertDefaultStringDateToFormatedString;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.dtos.QueryMapDto;
import vn.com.mbbank.kanban.core.enums.SqlOperatorEnum;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ServiceWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ServicePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ServiceResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ServiceWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.EntityUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ServiceRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.BatchFetcher;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.ExportFileProcessor;

@Service
@RequiredArgsConstructor
public class ServiceServiceImpl extends BaseSoftServiceImpl<ServiceEntity, String>
    implements ServiceService {

  private final ServiceRepository serviceRepository;
  private final ExportFileProcessor exportFileProcessor;

  @Override
  public Optional<ServiceEntity> findFirstByNameIgnoreCase(String serviceNameSource) {
    return serviceRepository.findFirstByNameIgnoreCaseAndDeletedFalse(serviceNameSource);
  }

  @Override
  public List<ServiceWithPriorityResponse> findServiceWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    List<ServiceWithPriorityModel> services =
        serviceRepository.findServiceWithPriorityByAlertGroupStatus(alertGroupStatus);
    Map<String, ServiceWithPriorityResponse> results = new HashMap<>();
    for (ServiceWithPriorityModel service : services) {
      var serviceResponse = results.computeIfAbsent(service.getId(),
          id -> new ServiceWithPriorityResponse(
              service.getId(),
              service.getName(),
              0,
              new HashSet<>(Set.of(service.getAlertPriorityConfigId()))));
      serviceResponse.getAlertPriorityConfigIds().add(service.getAlertPriorityConfigId());
      serviceResponse.setAlertAmount(serviceResponse.getAlertAmount() + service.getAlertAmount());
    }
    return new ArrayList<>(results.values());
  }


  @Override
  protected BaseSoftRepository<ServiceEntity, String> getRepository() {
    return serviceRepository;
  }

  @Override
  public Page<ServiceEntity> findWithPaging(ServicePaginationRequest request) {
    List<QueryMapDto> queryMapDtos = new ArrayList<>();

    if (!KanbanCommonUtil.isEmpty(request.getName())) {
      QueryMapDto<String> nameQueryMapDto = new QueryMapDto<>();
      nameQueryMapDto.setColumn(KanbanEntityUtils.getColumnName("name", ServiceEntity.class));
      nameQueryMapDto.setOperator(SqlOperatorEnum.LIKE);
      nameQueryMapDto.setValue(request.getName());
      nameQueryMapDto.setAndWithCollectionOther(true);
      queryMapDtos.add(nameQueryMapDto);
    }

    return Boolean.TRUE.equals(request.getWithDeleted())
        ? super.findWithPagingAndWithDeleted(request, queryMapDtos)
        : super.findWithPaging(request, queryMapDtos);

  }

  @Override
  public String generateId() {
    Long seqValue = serviceRepository.getNextSequenceValue();
    return "S" + String.format("%05d", seqValue);
  }

  protected String genTitleForFileExport(ServicePaginationRequest request) {
    StringBuilder title = new StringBuilder("Service - Filter by condition: ");
    if (!KanbanStringUtils.isNullOrEmpty(request.getSearch())) {
      title.append("ServiceName/Description contain keyword ")
          .append(request.getSearch())
          .append(".");
    }
    return title.toString();
  }


  @Override
  public List<String> findAllNameByIdIn(List<String> ids) {
    if (!CollectionUtils.isEmpty(ids)) {
      List<ServiceEntity> entities = serviceRepository.findAllByIdIn(ids);
      return EntityUtils.getFieldsByOrder(ids, entities, ServiceEntity::getId,
          ServiceEntity::getName);
    }
    return Collections.emptyList();
  }

  @Override
  public List<ServiceEntity> findAllByIdIn(List<String> ids) {
    return CollectionUtils.isEmpty(ids) ? Collections.emptyList() :
        serviceRepository.findAllById(ids);
  }

  @Override
  public List<ServiceEntity> findAllByNameIgnoreCaseInOrServiceIdIn(List<String> serviceNames,
                                                                    List<String> serviceIds) {
    var lowerServiceNames = Optional.ofNullable(serviceNames).orElse(new ArrayList<>()).stream()
        .map(String::toLowerCase).toList();
    return serviceRepository.findAllByNameIgnoreCaseInOrServiceIdInAndDeletedFalse(
        lowerServiceNames,
        serviceIds);
  }



  @Override
  public FileStorageEntity exportFile(ExportFileServiceRequest request, String userName, String filePath)
      throws IOException, BusinessException {
    ExportFileDto fileDto = ExportFileDto.builder()
        .attributes(request.getAttributes())
        .title(List.of(genTitleForFileExport(request.getPaginationRequest())))
        .build();
    BatchFetcher<ServiceResponse> batchFetcher = (offset, limit) -> {
      request.getPaginationRequest().setPage(offset / limit);
      request.getPaginationRequest().setSize(limit);
      Page<ServiceEntity> pageResult = findWithPaging(request.getPaginationRequest());
      List<ServiceResponse> responses = new ArrayList<>();
      for (ServiceEntity service : pageResult.getContent()) {
        responses.add(ServiceResponse.builder()
            .id(service.getId())
            .name(service.getName())
            .description(service.getDescription())
            .createdDate(convertDefaultStringDateToFormatedString(
                service.getCreatedDate().toString(), FORMAT_YYYY_MM_DD_T_HH_MM_SS))
            .build());
      }
      return responses;
    };
    return exportFileProcessor.exportFileCommon(request, userName, filePath, fileDto, batchFetcher);
  }

}
