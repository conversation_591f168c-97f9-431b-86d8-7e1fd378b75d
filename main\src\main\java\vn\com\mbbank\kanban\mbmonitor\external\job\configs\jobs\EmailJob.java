package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.quartz.JobExecutionContext;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CollectEmailSchedulerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailConfigService;

/**
 * EmailJob for scheduler config.
 *
 * <AUTHOR>
 * @created_date 11/22/2024
 */
@Component(JobNameConstants.EMAIL_COLLECT)
@RequiredArgsConstructor
public class EmailJob extends JobConfig implements CommonBaseConsumerService {
  private final EmailConfigService emailConfigService;
  private final CollectEmailSchedulerService collectEmailSchedulerService;
  private static final Logger logger = LoggerFactory.getLogger(EmailJob.class);

  private static final Long EMAIL_COLLECT_LOCK_TIME_OUT = 15 * 60 * 1000L;

  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException, SchedulerException {
    collectEmailSchedulerService.fetchAndProcessEmails(
        Long.valueOf(context.getJobDetail().getKey().getName()));
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    List<EmailConfigEntity> emailConfigs = emailConfigService.findAllByProtocolTypeIn(
        List.of(EmailProtocolTypeEnum.EXCHANGE, EmailProtocolTypeEnum.IMAP)
    );
    if (CollectionUtils.isEmpty(emailConfigs)) {
      return null;
    }
    return emailConfigs.stream()
        .collect(Collectors.toMap(
            obj -> String.valueOf(obj.getId()),
            obj -> obj.getIntervalTime() * 1000
        ));
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.EMAIL_COLLECT;
  }

  @Override
  public Long getLockTimeoutMs() {
    return EMAIL_COLLECT_LOCK_TIME_OUT;
  }

  @Override
  public boolean isKafkaMultipleGroup() {
    return true;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {

    var kafkaJobModel =
        KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var collectId = kafkaJobModel.configId().toString();
    var config = emailConfigService.findById(Long.valueOf(collectId));
    if (!KanbanCommonUtil.isEmpty(config)) {
      var interval = config.getIntervalTime() * 1000;
      boolean isUpdate = KafkaJobTypeEnum.NEW_OR_UPDATE.equals(kafkaJobModel.type());
      changeConfigJob(isUpdate, config.isActive(), collectId, interval);
    }
  }


  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.EMAIL_COLLECT;
  }
}
