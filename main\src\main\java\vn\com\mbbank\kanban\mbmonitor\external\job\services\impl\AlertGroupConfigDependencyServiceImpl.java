package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertGroupConfigDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigDependencyService;

@Service
@RequiredArgsConstructor
public class AlertGroupConfigDependencyServiceImpl extends BaseServiceImpl<AlertGroupConfigDependencyEntity, Long>
    implements AlertGroupConfigDependencyService {

  private final AlertGroupConfigDependencyRepository alertGroupConfigDependencyRepository;

  @Override
  protected JpaCommonRepository<AlertGroupConfigDependencyEntity, Long> getRepository() {
    return alertGroupConfigDependencyRepository;
  }


  @Override
  public List<AlertGroupConfigDependencyEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId) {
    return alertGroupConfigDependencyRepository.findAllByAlertGroupConfigId(alertGroupConfigId);
  }
}
