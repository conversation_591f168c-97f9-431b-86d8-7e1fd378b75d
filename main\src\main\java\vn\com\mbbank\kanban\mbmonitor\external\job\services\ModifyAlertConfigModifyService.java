package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;

/**
 * Service interface for managing modify alert configurations modify in the system.
 */
public interface ModifyAlertConfigModifyService extends BaseService<ModifyAlertConfigModifyEntity, Long> {
  /**
   * find all ModifyAlertConfigModifyEntity.
   *
   * @param modifyAlertConfigId ModifyAlertConfigId
   * @return list of ModifyAlertConfigModifyEntity
   */
  List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigId(Long modifyAlertConfigId);

  /**
   * find all ModifyAlertConfigModifyEntity.
   *
   * @param modifyAlertConfigId ModifyAlertConfigId
   * @return list of ModifyAlertConfigModifyEntity
   */
  List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigIdIn(List<Long> modifyAlertConfigId);
}
