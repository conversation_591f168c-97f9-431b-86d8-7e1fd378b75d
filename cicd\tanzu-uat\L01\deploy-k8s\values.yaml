##### Các trư<PERSON>ng khai báo bắt buộc ######
# So luong pod duoc tao
replicaCount: 2

# Khai bao service
## Type = NodePort cap nhat cac truong (nodePort,port,targetPort)
## Type = LoadBalancer cap nhat cac truong (port,targetPort,loadBalancerIP)
## Type = ClusterIP cap nhat cac truong (port,targetPort)
service:
  type: ClusterIP
  nodePort: 9006
  port: 9006
  targetPort: 9006
  #loadBalancerIP: **********

# Khai bao hostnames
hostAliases:
  ***********: esbinternal.mbbank.com.vn
  ***********: esbinternet.mbbank.com.vn
  ***********: esbinternal.mbbank.com.vn
  **********: vault-dev.mbbank.com.vn
  ***********: api.telegram.org

# Khai bao su dung configmap
configMaps:
  - application.properties
  - log4j2.xml
  - jsonTemplateLayout.json

# Khai bao su dung PVC
pvc: true
namePVC:
  - pvc-kanban

# Khai bao volumeMount
volumeMountsMap:
  mountPathconfig: /deployment/config
  mountPathpodinfo: /deployment/podinfo
  mountPathcommon: /deployment/common/config
  mountPathPVC:
    pvc-kanban: /data

#Khai bao tai nguyen su dung cho pod
resources:
  requests:
    memory: 1Gi


# Khai bao promethus
promethus: true
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /api/external-job/actuator/prometheus
  prometheus.io/port: "9006"


# Khai bao label cho istio
labels: false
templateLabels:
  version: old
  sidecar.istio.io/inject: "true"

# Khai bao HealthCheck
healthCheck: true
startupProbe:
  failureThreshold: 30
  httpGet:
    path: /api/external-job/actuator/health
    port: 9006
    scheme: HTTP
  initialDelaySeconds: 30 #depends on time app need to start
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 2
livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /api/external-job/actuator/health
    port: 9006
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2
readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /api/external-job/actuator/health
    port: 9006
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2

Command:
  # -- Options: [java, golang, dotnet, python-uvicorn, python-gunicorn, python, angular, manual]
  option: java
  binaryfile: app.jar
  # manual: java $JVM_OPTS -jar partner-mic.jar #Su dung khi option: manual

# Khai bao bien moi truong
# Them active profile va config location
ENV_PROFILE: dev
# JVM_OPTS: -Xms512m -Xmx2048m -Dsun.net.http.retryPost=false -Dhttp.retryPost=false -noverify -Dspring.profiles.active=dev,common,sc,sc-common -Dspring.config.location=file:/deployment/config,file:/deployment/common/config/,file:/deployment/sc/
JAVA_TOOL_OPTIONS: -Xms512m -Xmx2048m -Dsun.net.http.retryPost=false -Djava.security.egd=file:/dev/./urandom -Dspring.config.location=file:/deployment/config/application.properties,file:/deployment/sc/application-sc-common.properties -Dlogging.config=file:/deployment/config/log4j2.xml -Dhttp.retryPost=false
# GO_OPTS: golang
# DOTNET_OPTS: donet
# PYTHON_OPTS: python

# Chi dinh pod chay tren node co dinh(chi dung tren live: default hoac ecm)
NodeSelectorEnable: false
nodeName: default

# Khai bao hpa thi de gia tri hpa true,,nguoc lai de false
autoscaling:
  enabled: false
  minPodsHPA: 2
  maxPodsHPA: 4
  cpuHPA: 80
  memoryHPA: 80

# So lan deploy Helm
numberHelm: H1

# Thong tin image
image:
  repository: image_version
  pullPolicy: Always
  tag: "image_tag"

# Khai bao secret-registry harbor
imagePullSecrets:
  - name: harbor-secret-registry

####### Các options được bổ sung và có thể được xóa bỏ khi không cần thiết #######
# Service Account
serviceAccount:
  create: true
  name: k8s_sa

# Common configmaps
ConfigCommon: false
ConfigMapsCommonName: <Configmap resource common name>
ConfigMapsCommonData:
  - esb-application-common.properties
  - application-common.properties

# Vault
VaultInject: true
# Run container as non root user
# User: appadmin, UID 10000
# User: nginx, UID 101
## Configure Pods Security Context
podSecurityContext:
  enabled: true
  fsGroup: 10000

## Configure Container Security Context
containerSecurityContext:
  enabled: true
  runAsUser: 10000
  runAsNonRoot: true
  readOnlyRootFilesystem: true
