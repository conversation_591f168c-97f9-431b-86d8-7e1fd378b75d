package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.text.MessageFormat;
import java.util.Date;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.TelegramService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TelegramAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TelegramAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TelegramConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 */
@Service
@RequiredArgsConstructor
public class TelegramAlertConfigServiceImpl
    extends BaseServiceImpl<TelegramAlertConfigEntity, String>
    implements TelegramAlertConfigService {
  private final TelegramAlertConfigRepository telegramAlertConfigRepository;
  private final TelegramConfigService telegramConfigService;
  private final TelegramService telegramService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  Logger logger = LoggerFactory.getLogger(this.getClass());

  @Override
  protected JpaCommonRepository<TelegramAlertConfigEntity, String> getRepository() {
    return telegramAlertConfigRepository;
  }

  @Value("${mbmonitor.telegram.alert.send.limit.seconds:600}")
  private int alertSendLimit;

  @Override
  public void sendMessageToTelegram(AlertEntity alert) {
    if (KanbanCommonUtil.isEmpty(alert)) {
      logger.info("Alert send telegram is null");
      return;
    }
    boolean isExpiredTime =
        new Date().getTime() - alert.getCreatedDate().getTime()
            >
            alertSendLimit * 1000;
    if (isExpiredTime) {
      logger.info("Alert send telegram is expired time", alert);
      return;
    }
    // find config telegram
    var config = telegramConfigService.findFirstByType(TelegramConfigTypeEnum.ALERT);
    if (KanbanCommonUtil.isEmpty(config) || !config.getActive()) {
      return;
    }
    // decrypt token bot
    config.setBotToken(KanbanEncryptorUtils.decrypt(config.getBotToken()));

    var alertConfigs =
        telegramAlertConfigRepository.findByTelegramConfigAndServiceOrApp(config.getId(),
            alert.getServiceId(), alert.getApplicationId());

    var applicationConfig = alertConfigs.stream()
        .filter(
            alertConfig -> Objects.equals(alertConfig.getApplicationId(),
                alert.getApplicationId()))
        .findFirst().orElse(null);

    var serviceConfig = alertConfigs.stream()
        .filter(
            alertConfig -> Objects.equals(alertConfig.getServiceId(),
                alert.getServiceId()))
        .findFirst().orElse(null);
    if (!KanbanCommonUtil.isEmpty(serviceConfig) && !serviceConfig.getActive()) {
      return;
    }

    if (!KanbanCommonUtil.isEmpty(applicationConfig) && !applicationConfig.getActive()) {
      return;
    }

    var groupChatIdService =
        !KanbanCommonUtil.isEmpty(serviceConfig) ? serviceConfig.getGroupChatId() : null;

    var groupChatIdApp =
        !KanbanCommonUtil.isEmpty(applicationConfig) ? applicationConfig.getGroupChatId() : null;

    if (!KanbanCommonUtil.isEmpty(applicationConfig)) {
      sendMessage(config.getBotToken(),
          StringUtils.firstNonBlank(groupChatIdApp, groupChatIdService,
              config.getDefaultGroupChatId()),
          alert.getContent());
      return;
    }
    if (!KanbanCommonUtil.isEmpty(serviceConfig)) {
      sendMessage(config.getBotToken(),
          StringUtils.firstNonBlank(groupChatIdService,
              config.getDefaultGroupChatId()),
          alert.getContent());
      return;
    }
    sendMessage(config.getBotToken(), config.getDefaultGroupChatId(),
        alert.getContent());
  }

  private void sendMessage(String token, String groupChatId, String message) {
    ResponseEntity<String> result;
    try {
      result = telegramService.sendMessage(token, groupChatId,
          message);
    } catch (Exception exception) {
      sysLogKafkaProducerService.send(LogActionEnum.SEND_TELEGRAM_FAILED, groupChatId, message,
          exception.getMessage());
      throw exception;
    }

    if (!HttpStatus.OK.equals(result.getStatusCode())) {
      logger.error(
          MessageFormat.format("Send message false {0} with status code {1}",
              KanbanMapperUtils.objectToJson(result),
              result.getStatusCode()));
      sysLogKafkaProducerService.send(LogActionEnum.SEND_TELEGRAM_FAILED, groupChatId, message,
          result);
      throw new BusinessRuntimeException(ErrorCode.TELEGRAM_ALERT_SEND_FALSE);
    }
  }


  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.TELEGRAM;
  }

  @Override
  public boolean isKafkaRetry() {
    return true;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    AlertEntity alert = KanbanMapperUtils.jsonToObject(data.getValue().toString(), AlertEntity.class);
    sendMessageToTelegram(alert);
  }
}
