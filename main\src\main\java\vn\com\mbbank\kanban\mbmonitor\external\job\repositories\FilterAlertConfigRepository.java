package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;

/**
 * Repository table FilterAlertConfigEntity.
 */
@Repository
public interface FilterAlertConfigRepository
    extends JpaCommonRepository<FilterAlertConfigEntity, Long> {
  /**
   * find all config by active.
   *
   * @param active active status
   * @return FilterAlertConfigEntity.
   */
  List<FilterAlertConfigEntity> findAllByActive(boolean active);

}
