package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigDependencyEntity;

/**
 * Repository interface for managing modify alert configuration dependency data in the system.
 */
@Repository
public interface ModifyAlertConfigDependencyRepository
    extends JpaCommonRepository<ModifyAlertConfigDependencyEntity, Long> {

  /**
   * find all ModifyAlertConfigDependencyEntity.
   *
   * @param modifyAlertConfigId id of config
   * @return list of ModifyAlertConfigDependencyEntity
   */
  List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigId(Long modifyAlertConfigId);


  /**
   * find all ModifyAlertConfigDependencyEntity.
   *
   * @param modifyAlertConfigIds list id of config
   * @return list of ModifyAlertConfigDependencyEntity
   */
  List<ModifyAlertConfigDependencyEntity> findAllByModifyAlertConfigIdIn(List<Long> modifyAlertConfigIds);

  /**
   * delete all dependency.
   *
   * @param modifyAlertConfigId id of config
   */
  void deleteAllByModifyAlertConfigId(Long modifyAlertConfigId);
}
