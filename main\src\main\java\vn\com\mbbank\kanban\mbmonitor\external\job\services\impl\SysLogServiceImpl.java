package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.Date;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysLogEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.SysLogEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.SysLogRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.SysLogService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Service
@RequiredArgsConstructor
public class SysLogServiceImpl extends BaseServiceImpl<SysLogEntity, String>
    implements SysLogService {
  static Logger logger = LoggerFactory.getLogger(ExportDataServiceImpl.class);
  private final SysLogRepository sysLogRepository;
  private final SysLogEntityMapper sysLogEntityMapper = SysLogEntityMapper.INSTANCE;

  @Override
  protected JpaCommonRepository<SysLogEntity, String> getRepository() {
    return sysLogRepository;
  }

  @Override
  public SysLogEntity saveLog(SysLogModel log) throws BusinessException {
    var sysLog = sysLogRepository.findById(log.getId());
    if (sysLog.isPresent()) {
      throw new BusinessException(ErrorCode.LOG_ALREADY_EXISTED);
    }
    return sysLogRepository.save(sysLogEntityMapper.map(log));
  }


  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    logger.info("Message log start at " + DateUtils.formatDate(new Date()));
    SysLogModel log = KanbanMapperUtils.jsonToObject(data.getValue().toString(), SysLogModel.class);
    saveLog(log);
    logger.info("Message log success at " + DateUtils.formatDate(new Date()));
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.LOG;
  }
}
