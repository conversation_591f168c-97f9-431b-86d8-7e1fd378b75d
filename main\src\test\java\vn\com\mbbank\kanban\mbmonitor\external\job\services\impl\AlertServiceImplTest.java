//package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.jupiter.api.io.TempDir;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.mockito.junit.jupiter.MockitoSettings;
//import org.mockito.quality.Strictness;
//import vn.com.mbbank.kanban.core.exceptions.BusinessException;
//import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
//import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertCursor;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertPaginationRequest;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertResponse;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
//import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
//import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
//import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
//import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
//import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareOperator;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareUnit;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
//import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
//import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
//import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
//import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
//import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
//import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertRepository;
//import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
//import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
//import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;
//import vn.com.mbbank.kanban.mbmonitor.external.job.services.NoteService;
//import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;
//import java.io.BufferedOutputStream;
//import java.io.IOException;
//import java.nio.file.Path;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyList;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.doReturn;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.spy;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
//public class AlertServiceImplTest {
//
//  @Mock
//  ServiceService serviceService;
//  @Mock
//  NoteService noteService;
//  @Mock
//  ApplicationService applicationService;
//  @Mock
//  AlertPriorityConfigService alertPriorityConfigService;
//  @Mock
//  FileStorageService fileStorageService;
//  @TempDir
//  Path tempDir;
//  @InjectMocks
//  private AlertServiceImpl alertService;
//  @Mock
//  private AlertRepository alertRepository;
//  private AlertEntity alertEntity1;
//  private AlertEntity alertEntity2;
//
//  @BeforeEach
//  void setUp() {
//    alertEntity1 = new AlertEntity();
//    alertEntity1.setId(1L);
//    alertEntity2 = new AlertEntity();
//    alertEntity2.setId(2L);
//  }
//
//  @Test
//  public void getRepository_test() {
//    JpaCommonRepository<AlertEntity, Long> repository = alertService.getRepository();
//    assertEquals(alertRepository, repository, "Repository should not be null");
//  }
//
//  @Test
//  public void findTopAlertsByAlertGroupIdAndStatus_success() {
//    Mockito.when(
//        alertRepository.findTopAlertsByAlertGroupIdAndStatus(Mockito.anyLong(), Mockito.any(),
//            Mockito.anyInt())).thenReturn(
//        List.of());
//    var res = alertService.findTopAlertsByAlertGroupIdAndStatus(1L, AlertStatusEnum.NEW, 1);
//    assertNotNull(res);
//  }
//
//  @Test
//  public void findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus_success() {
//    Mockito.when(
//        alertRepository.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
//            Mockito.anyList(), Mockito.anyList(), Mockito.anyInt(), Mockito.any())).thenReturn(
//        List.of());
//    var res = alertService.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
//        List.of(), List.of(), 1, AlertGroupStatusEnum.NEW);
//    assertNotNull(res);
//  }
//
//  @Test
//  void findTopAlertsByAlertGroupIdAndStatus() {
//    Long alertGroupId = 100L;
//    AlertStatusEnum status = AlertStatusEnum.NEW;
//    int numberOfResult = 5;
//    List<AlertEntity> expected = Arrays.asList(alertEntity1, alertEntity2);
//
//    when(alertRepository.findTopAlertsByAlertGroupIdAndStatus(alertGroupId, status, numberOfResult))
//        .thenReturn(expected);
//
//    List<AlertEntity> result =
//        alertService.findTopAlertsByAlertGroupIdAndStatus(alertGroupId, status, numberOfResult);
//    assertEquals(expected, result);
//    verify(alertRepository).findTopAlertsByAlertGroupIdAndStatus(alertGroupId, status,
//        numberOfResult);
//  }
//
//  @Test
//  void findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus() {
//    List<String> serviceIds = Arrays.asList("s1", "s2");
//    List<String> applicationIds = Arrays.asList("a1", "a2");
//    int alertGroupHandleTriggerInterval = 10;
//    AlertGroupStatusEnum status = AlertGroupStatusEnum.NEW;
//    List<AlertEntity> expected = Collections.singletonList(alertEntity1);
//
//    when(alertRepository.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
//        serviceIds, applicationIds, alertGroupHandleTriggerInterval, status))
//        .thenReturn(expected);
//
//    List<AlertEntity> result = alertService
//        .findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(serviceIds,
//            applicationIds, alertGroupHandleTriggerInterval, status);
//    assertEquals(expected, result);
//    verify(alertRepository)
//        .findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(serviceIds,
//            applicationIds, alertGroupHandleTriggerInterval, status);
//  }
//
//  @TestForUser
//  public void genTitleForFileExport_WithAllConditions() {
//    AlertPaginationRequest request = new AlertPaginationRequest();
//    request.setRangeDate("All time");
//    request.setServiceIds(Arrays.asList("1", "2"));
//    request.setApplicationIds(Arrays.asList("10", "20"));
//    request.setAlertPriorityConfigIds(Arrays.asList(100L, 200L));
//    request.setStatuses(List.of(AlertStatusEnum.NEW));
//    request.setContent("Test content");
//    request.setRecipient("John Doe");
//    when(serviceService.findAllNameByIdIn(Arrays.asList("1", "2"))).thenReturn(
//        Arrays.asList("Service A", "Service B"));
//    when(applicationService.findAllNameByIdIn(Arrays.asList("10", "20"))).thenReturn(
//        Arrays.asList("App X", "App Y"));
//    when(alertPriorityConfigService.findAllNameByIdIn(Arrays.asList(100L, 200L))).thenReturn(
//        Arrays.asList("High", "Low"));
//    List<String> titles = alertService.genTitleForFileExport(request);
//    assertNotNull(titles);
//    assertEquals(8, titles.size());
//    assertEquals("Alert - Filter by condition: ", titles.get(0));
//    assertTrue(titles.contains("Range date = All time"));
//    assertTrue(titles.contains("Service Name = [Service A, Service B]"));
//    assertTrue(titles.contains("Application Name = [App X, App Y]"));
//    assertTrue(titles.contains("Priorities = [High, Low]"));
//    assertTrue(titles.contains("Status = [NEW]"));
//    assertTrue(titles.contains("Alert Content = Test content"));
//    assertTrue(titles.contains("Contact = John Doe"));
//    verify(serviceService).findAllNameByIdIn(Arrays.asList("1", "2"));
//    verify(applicationService).findAllNameByIdIn(Arrays.asList("10", "20"));
//    verify(alertPriorityConfigService).findAllNameByIdIn(Arrays.asList(100L, 200L));
//  }
//
//  @TestForDev
//  public void genTitleForFileExport_WithEmptyRequest() {
//    AlertPaginationRequest request = new AlertPaginationRequest();
//    List<String> titles = alertService.genTitleForFileExport(request);
//    assertNotNull(titles);
//    assertFalse(titles.isEmpty(), "Expected empty titles list when no conditions are present");
//  }
//
//  @TestForDev
//  public void genTitleForFileExport_WithSomeNullValues_sucess() {
//    AlertPaginationRequest request = new AlertPaginationRequest();
//    request.setRangeDate(null);
//    request.setServiceIds(null);
//    request.setStatuses(List.of(AlertStatusEnum.NEW));
//    List<String> titles = alertService.genTitleForFileExport(request);
//    assertNotNull(titles);
//    assertEquals(2, titles.size());
//    assertEquals("Alert - Filter by condition: ", titles.get(0));
//    assertTrue(titles.contains("Status = [NEW]"));
//  }
//
//  @TestForDev
//  void addCondition_withEmptyString_success() {
//    List<String> conditions = new ArrayList<>();
//    String label = "testLabel";
//    Object value = "";
//    AlertServiceImpl.addCondition(conditions, label, value);
//    assertTrue(conditions.isEmpty(), "Conditions should be empty when value is an empty string");
//  }
//
//  @TestForDev
//  void addCondition_withNonEmptyObject_success() {
//    List<String> conditions = new ArrayList<>();
//    String label = "testLabel";
//    Object value = 123;
//    AlertServiceImpl.addCondition(conditions, label, value);
//    assertEquals(1, conditions.size());
//    assertEquals("testLabel = 123", conditions.get(0));
//  }
//
//  @TestForDev
//  void addDurationCondition_success_inputEmpty() {
//    var condition = new ArrayList<String>();
//    alertService.addDurationCondition(condition, null, 1L, AlertDurationCompareUnit.HOUR);
//    alertService.addDurationCondition(condition, AlertDurationCompareOperator.EQUAL, null,
//        AlertDurationCompareUnit.HOUR);
//    alertService.addDurationCondition(condition, AlertDurationCompareOperator.EQUAL, 1L, null);
//    assertEquals(0, condition.size());
//  }
//
//  @TestForDev
//  void addDurationCondition_success() {
//    var condition = new ArrayList<String>();
//    alertService.addDurationCondition(condition, AlertDurationCompareOperator.EQUAL, 1L,
//        AlertDurationCompareUnit.HOUR);
//    assertEquals(1, condition.size());
//  }
//
//  @Test
//  void findAll_success() {
//    var alertResponse = new AlertResponse();
//    alertResponse.setApplicationId("a");
//    alertResponse.setServiceId("s");
//    alertResponse.setPriorityConfigId(1L);
//    alertResponse.setAlertGroupId(1L);
//    var alertResponse1 = new AlertResponse();
//    alertResponse1.setApplicationId("a");
//    alertResponse1.setServiceId("s");
//    alertResponse1.setPriorityConfigId(1L);
//    alertResponse1.setAlertGroupId(1L);
//    alertResponse1.setClosedDuration("200");
//    var application = new ApplicationResponse();
//    application.setId("a");
//    var priority = new AlertPriorityConfigEntity();
//    priority.setId(1L);
//    var note = new NoteEntity();
//    note.setAlertGroupId(1L);
//    when(alertRepository.findAll(any(AlertPaginationRequest.class))).thenReturn(
//        new CursorPageResponse<>(List.of(alertResponse, alertResponse1), null));
//    when(noteService.findAllByAlertGroupIdInOrderByCreatedDateDesc(anyList())).thenReturn(List.of(note));
//    var res = alertService.findAll(new AlertPaginationRequest());
//    verify(alertRepository, times(1)).findAll(any(AlertPaginationRequest.class));
//    assertEquals(2, res.getData().size());
//  }
//
//  @Test
//  void findAll_success_caseEmpty() {
//    when(alertRepository.findAll(any(AlertPaginationRequest.class))).thenReturn(
//        new CursorPageResponse<>(List.of(), null));
//    var res = alertService.findAll(new AlertPaginationRequest());
//    assertNotNull(res);
//  }
//
//  @Test
//  void findAll_success_caseEmptyDependence() {
//    var alertResponse = new AlertResponse();
//    alertResponse.setApplicationId("a");
//    alertResponse.setServiceId("s");
//    alertResponse.setPriorityConfigId(1L);
//    alertResponse.setAlertGroupId(1L);
//    when(alertRepository.findAll(any(AlertPaginationRequest.class))).thenReturn(
//        new CursorPageResponse<>(List.of(alertResponse), null));
//    when(noteService.findAllByAlertGroupIdInOrderByCreatedDateDesc(anyList())).thenReturn(List.of());
//    var res = alertService.findAll(new AlertPaginationRequest());
//    verify(alertRepository, times(1)).findAll(any(AlertPaginationRequest.class));
//    assertEquals(res.getData().size(), 1);
//  }
//
//  @TestForUser
//  void exportFile_success() throws IOException, BusinessException {
//    ExportFileAlertRequest request = new ExportFileAlertRequest();
//    request.setNumberOfResults(10);
//    AlertPaginationRequest paginationRequest = new AlertPaginationRequest();
//    request.setPaginationRequest(paginationRequest);
//    ExportDataModel exportDataModel = new ExportDataModel();
//    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
//    request.setExportDataModel(exportDataModel);
//    String userName = "testUser";
//    String filePathStr = tempDir.resolve("export_test.csv").toString();
//
//    FileExporter exporter = mock(FileExporter.class);
//    doNothing().when(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).writeBatch(anyList(), any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).close(any(BufferedOutputStream.class));
//
//    try (MockedStatic<FileExporterFactory> mockedFactory = mockStatic(FileExporterFactory.class)) {
//      mockedFactory.when(() -> FileExporterFactory.getFileExporter(ExportFileTypeEnum.CSV)).thenReturn(exporter);
//
//      CursorPageResponse<AlertResponse, AlertCursor> response = new CursorPageResponse<>();
//      List<AlertResponse> alerts = new ArrayList<>();
//      AlertResponse alertResponse = new AlertResponse();
//      alertResponse.setCreatedDate("2021-01-01T00:00:00");
//      alertResponse.setClosedDate("2021-01-02T00:00:00");
//      alerts.add(alertResponse);
//      AlertCursor nextCursor = new AlertCursor();
//      response.setData(alerts);
//      response.setNextCursor(nextCursor);
//
//      AlertServiceImpl spyService = spy(alertService);
//      doReturn(response).when(spyService).findAll(any(AlertPaginationRequest.class));
//
//      FileStorageEntity fileStorageEntity = new FileStorageEntity();
//      fileStorageEntity.setPath(filePathStr);
//      when(fileStorageService.registerFile(filePathStr, CommonConstants.MODULE_EXPORT, userName))
//          .thenReturn(fileStorageEntity);
//
//      FileStorageEntity result = spyService.exportFile(request, userName, filePathStr);
//
//      assertNotNull(result);
//      assertEquals(filePathStr, result.getPath());
//      verify(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//      verify(exporter).writeBatch(anyList(), any(ExportFileDto.class), any(BufferedOutputStream.class));
//      verify(exporter).close(any(BufferedOutputStream.class));
//    }
//  }
//
//  @TestForDev
//  void exportFile_success_alertEmpty() throws IOException, BusinessException {
//    ExportFileAlertRequest request = new ExportFileAlertRequest();
//    request.setNumberOfResults(10);
//    AlertPaginationRequest paginationRequest = new AlertPaginationRequest();
//    request.setPaginationRequest(paginationRequest);
//    ExportDataModel exportDataModel = new ExportDataModel();
//    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
//    request.setExportDataModel(exportDataModel);
//    String userName = "testUser";
//    String filePathStr = tempDir.resolve("export_test.csv").toString();
//
//    FileExporter exporter = mock(FileExporter.class);
//    doNothing().when(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).writeBatch(anyList(), any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).close(any(BufferedOutputStream.class));
//
//    try (MockedStatic<FileExporterFactory> mockedFactory = mockStatic(FileExporterFactory.class)) {
//      mockedFactory.when(() -> FileExporterFactory.getFileExporter(ExportFileTypeEnum.CSV)).thenReturn(exporter);
//
//      CursorPageResponse<AlertResponse, AlertCursor> response = new CursorPageResponse<>();
//      List<AlertResponse> alerts = new ArrayList<>();
//      AlertResponse alertResponse = new AlertResponse();
//      alertResponse.setCreatedDate("2021-01-01T00:00:00");
//      alertResponse.setClosedDate("2021-01-02T00:00:00");
//      AlertCursor nextCursor = new AlertCursor();
//      response.setData(alerts);
//      response.setNextCursor(nextCursor);
//
//      AlertServiceImpl spyService = spy(alertService);
//      doReturn(response).when(spyService).findAll(any(AlertPaginationRequest.class));
//
//      FileStorageEntity fileStorageEntity = new FileStorageEntity();
//      fileStorageEntity.setPath(filePathStr);
//      when(fileStorageService.registerFile(filePathStr, CommonConstants.MODULE_EXPORT, userName))
//          .thenReturn(fileStorageEntity);
//
//      FileStorageEntity result = spyService.exportFile(request, userName, filePathStr);
//
//      assertNotNull(result);
//      assertEquals(filePathStr, result.getPath());
//      verify(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//      verify(exporter).close(any(BufferedOutputStream.class));
//    }
//  }
//
//  @TestForDev
//  void exportFile_success_alertEmptyAndCursorNull() throws IOException, BusinessException {
//    ExportFileAlertRequest request = new ExportFileAlertRequest();
//    request.setNumberOfResults(10);
//    AlertPaginationRequest paginationRequest = new AlertPaginationRequest();
//    request.setPaginationRequest(paginationRequest);
//    ExportDataModel exportDataModel = new ExportDataModel();
//    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
//    request.setExportDataModel(exportDataModel);
//    String userName = "testUser";
//    String filePathStr = tempDir.resolve("export_test.csv").toString();
//
//    FileExporter exporter = mock(FileExporter.class);
//    doNothing().when(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).writeBatch(anyList(), any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).close(any(BufferedOutputStream.class));
//
//    try (MockedStatic<FileExporterFactory> mockedFactory = mockStatic(FileExporterFactory.class)) {
//      mockedFactory.when(() -> FileExporterFactory.getFileExporter(ExportFileTypeEnum.CSV)).thenReturn(exporter);
//
//      CursorPageResponse<AlertResponse, AlertCursor> response = new CursorPageResponse<>();
//      List<AlertResponse> alerts = new ArrayList<>();
//      AlertResponse alertResponse = new AlertResponse();
//      alertResponse.setCreatedDate("2021-01-01T00:00:00");
//      alertResponse.setClosedDate("2021-01-02T00:00:00");
//      AlertCursor nextCursor = new AlertCursor();
//      response.setData(alerts);
//      response.setNextCursor(null);
//
//      AlertServiceImpl spyService = spy(alertService);
//      doReturn(response).when(spyService).findAll(any(AlertPaginationRequest.class));
//
//      FileStorageEntity fileStorageEntity = new FileStorageEntity();
//      fileStorageEntity.setPath(filePathStr);
//      when(fileStorageService.registerFile(filePathStr, CommonConstants.MODULE_EXPORT, userName))
//          .thenReturn(fileStorageEntity);
//
//      FileStorageEntity result = spyService.exportFile(request, userName, filePathStr);
//
//      assertNotNull(result);
//      assertEquals(filePathStr, result.getPath());
//      verify(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//      verify(exporter).close(any(BufferedOutputStream.class));
//    }
//  }
//
//  @TestForUser
//  void exportFile_ioExceptionInWriteBatch() throws Exception {
//    ExportFileAlertRequest request = new ExportFileAlertRequest();
//    request.setNumberOfResults(10);
//    AlertPaginationRequest paginationRequest = new AlertPaginationRequest();
//    request.setPaginationRequest(paginationRequest);
//    ExportDataModel exportDataModel = new ExportDataModel();
//    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
//    request.setExportDataModel(exportDataModel);
//    String userName = "testUser";
//    String filePathStr = tempDir.resolve("export_test.csv").toString();
//
//    FileExporter exporter = mock(FileExporter.class);
//    doNothing().when(exporter).init(any(ExportFileDto.class), any(BufferedOutputStream.class));
//    org.mockito.Mockito.doThrow(new IOException("Simulated writeBatch error"))
//        .when(exporter).writeBatch(anyList(), any(ExportFileDto.class), any(BufferedOutputStream.class));
//    doNothing().when(exporter).close(any(BufferedOutputStream.class));
//
//    try (MockedStatic<FileExporterFactory> mockedFactory = mockStatic(FileExporterFactory.class)) {
//      mockedFactory.when(() -> FileExporterFactory.getFileExporter(ExportFileTypeEnum.CSV)).thenReturn(exporter);
//
//      CursorPageResponse<AlertResponse, AlertCursor> response = new CursorPageResponse<>();
//      List<AlertResponse> alerts = new ArrayList<>();
//      AlertResponse alertResponse = new AlertResponse();
//      alertResponse.setCreatedDate("2021-01-01T00:00:00");
//      alertResponse.setClosedDate("2021-01-02T00:00:00");
//      alerts.add(alertResponse);
//      response.setData(alerts);
//      response.setNextCursor(null);
//
//      when(alertRepository.findAll(any(AlertPaginationRequest.class))).thenReturn(response);
//      when(fileStorageService.registerFile(filePathStr, CommonConstants.MODULE_EXPORT, userName))
//          .thenReturn(new FileStorageEntity());
//
//      assertThrows(IOException.class, () -> {
//        alertService.exportFile(request, userName, filePathStr);
//      });
//    }
//  }
//}
