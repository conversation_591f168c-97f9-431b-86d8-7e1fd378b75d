package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsConfigService;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 11:37:47
 */
@Service
@RequiredArgsConstructor
public class TeamsConfigServiceImpl extends BaseServiceImpl<TeamsConfigEntity, String>
    implements TeamsConfigService {

  private final TeamsConfigRepository teamsConfigRepository;

  @Override
  protected JpaCommonRepository<TeamsConfigEntity, String> getRepository() {
    return teamsConfigRepository;
  }

  @Override
  public Optional<TeamsConfigEntity> findAlertConfig() {
    return teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT);
  }
}
