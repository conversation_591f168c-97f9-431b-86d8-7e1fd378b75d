package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FilterAlertConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;

@ExtendWith(MockitoExtension.class)
class FilterAlertConfigServiceImplTest {

  @Mock
  private RuleGroupType ruleGroup;
  @InjectMocks
  private FilterAlertConfigServiceImpl filterAlertConfigService;
  @Mock
  private CustomObjectService customObjectService;
  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private FilterAlertConfigRepository filterAlertConfigRepository;

  @Test
  void getRepository() {
    JpaCommonRepository<FilterAlertConfigEntity, Long> result =
        filterAlertConfigService.getRepository();
    assertEquals(filterAlertConfigRepository, result);
  }

  @Test
  void testUpdateAlertsForFilter_EmptyInputList() {
    // Given
    List<AlertBaseModel> alertRawValue = new ArrayList<>();

    // When
    List<AlertBaseModel> result =
        filterAlertConfigService.updateAlertsForFilter(alertRawValue);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  void testUpdateAlertsForFilter_NoActiveConfigs() {
    // Given
    List<AlertBaseModel> alertRawValue = List.of(new AlertBaseModel());
    when(filterAlertConfigRepository.findAllByActive(true)).thenReturn(Collections.emptyList());

    // When
    List<AlertBaseModel> result = filterAlertConfigService.updateAlertsForFilter(alertRawValue);

    // Then
    assertEquals(1, result.size());
  }

  @Test
  void testGetAlertConditionRawValueMap_NullAlert() {
    // When
    Map<String, Object> result =
        FilterAlertConfigServiceImpl.getAlertConditionRawValueMap(null, null);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  void getAlertConditionRawValueMap_ValidAlert_EmptyCustomObjects() {
    // Given
    AlertBaseModel alert = new AlertBaseModel();
    alert.setServiceNameRaw("Service A");
    alert.setApplicationNameRaw("App A");
    alert.setContentRaw("Alert content");
    alert.setPriorityRaw("High");
    alert.setRecipientRaw("<EMAIL>");

    // When
    Map<String, Object> result =
        FilterAlertConfigServiceImpl.getAlertConditionRawValueMap(alert, Collections.emptyList());
    // Then
    assertEquals(5, result.size());
    assertEquals("Service A", result.get("serviceName"));
    assertEquals("App A", result.get("applicationName"));
    assertEquals("Alert content", result.get("content"));
    assertEquals("High", result.get("priority"));
    assertEquals("<EMAIL>", result.get("recipient"));
  }

  @Test
  void testGetAlertConditionRawValueMap_ValidAlert_WithCustomObjects() {
    // Given
    AlertBaseModel alert = new AlertBaseModel();
    alert.setServiceNameRaw("Service A");
    alert.setApplicationNameRaw("App A");
    alert.setContentRaw("Alert content");
    alert.setPriorityRaw("High");
    alert.setRecipientRaw("<EMAIL>");


    CustomObjectEntity regexCustomObject = new CustomObjectEntity();
    regexCustomObject.setRegex(
        "Cảnh báo bảng (?<=Cảnh báo bảng )(.*?)(?=\\s+có) có \\d+ records, vượt ngưỡng \\d+");
    regexCustomObject.setType(CustomObjectTypeEnum.REGEX);

    // When
    Map<String, Object> result =
        FilterAlertConfigServiceImpl.getAlertConditionRawValueMap(alert,
            List.of(regexCustomObject));

    // Then
    assertEquals(6, result.size());

  }

  @Test
  void alertsMatchingMaintenance_NEW() throws Exception {
    Date now = new Date();
    AlertBaseModel alert = new AlertBaseModel();
    alert.setContentRaw("Critical Alert 1");
    alert.setServiceNameRaw("AlertStatusEnum.NEW");
    alert.setApplicationNameRaw("1L");
    alert.setRecipientRaw("1L");
    alert.setPriorityRaw("2001");
    List<AlertBaseModel> alerts = new ArrayList<>();
    alerts.add(alert);
    ruleGroup = new RuleGroupType();

    ruleGroup.setRules(List.of(new RuleGroupType()));
    ruleGroup.setCombinator(ConditionCombinatorEnum.OR);
    FilterAlertConfigEntity filterAlertConfig = new FilterAlertConfigEntity();
    filterAlertConfig.setId(1L);
    filterAlertConfig.setRuleGroup(ruleGroup);
    filterAlertConfig.setRuleGroup(RuleConverterUtils.convertStringToRuleGroupType(
        "{\"combinator\":\"AND\",\"rules\":[{\"field\":\"priority\",\"operator\":\"IS_ONE_OF\",\"value\":[\"2001\"]}]}"));

    FilterAlertConfigResponse response = new FilterAlertConfigResponse();
    response.setRuleGroup(filterAlertConfig.getRuleGroup());
    Mockito.when(filterAlertConfigRepository.findAllByActive(true))
        .thenReturn(Collections.singletonList(filterAlertConfig));

    List<AlertBaseModel> result = filterAlertConfigService.updateAlertsForFilter(alerts);
    Assertions.assertNotNull(result);
    Mockito.verify(filterAlertConfigRepository).findAllByActive(true);
  }

}
