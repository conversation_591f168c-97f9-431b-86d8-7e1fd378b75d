package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
public interface TelegramConfigService extends BaseService<TelegramConfigEntity, String> {
  /**
   * Find first config by type.
   *
   * @param type TelegramConfigTypeEnum
   * @return telegram config
   */
  TelegramConfigEntity findFirstByType(TelegramConfigTypeEnum type);
}
