package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareOperator;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareUnit;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.ApplicationTest;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.AlertRepositoryCustomImpl;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AlertRepositoryCustomImplTest extends ApplicationTest {
  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;

  @InjectMocks
  AlertRepositoryCustomImpl customAlertRepository;

}
