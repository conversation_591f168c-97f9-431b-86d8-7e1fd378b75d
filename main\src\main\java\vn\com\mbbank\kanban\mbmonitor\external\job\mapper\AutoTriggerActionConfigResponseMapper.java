package vn.com.mbbank.kanban.mbmonitor.external.job.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.AutoTriggerActionConfigResponse;

/**
 * Mapper interface for mapping between `AutoTriggerActionConfigResponse` and `AutoTriggerActionConfigEntity`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AutoTriggerActionConfigResponseMapper extends
    KanbanBaseMapper<AutoTriggerActionConfigResponse, AutoTriggerActionConfigEntity> {
  AutoTriggerActionConfigResponseMapper INSTANCE = Mappers.getMapper(AutoTriggerActionConfigResponseMapper.class);

}
