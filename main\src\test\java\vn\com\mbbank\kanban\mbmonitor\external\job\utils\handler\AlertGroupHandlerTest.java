package vn.com.mbbank.kanban.mbmonitor.external.job.utils.handler;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigConditionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;

@ExtendWith({MockitoExtension.class})
class AlertGroupHandlerTest {
  @Mock
  AlertGroupConfigService alertGroupConfigService;
  @Mock
  AlertGroupConfigConditionService alertGroupConfigConditionService;
  @Mock
  AlertGroupService alertGroupService;
  @Mock
  CustomObjectService customObjectService;
  @Mock
  AlertService alertService;
  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @Mock
  ApplicationService applicationService;

  @Mock
  AlertGroupConfigDependencyService alertGroupConfigDependencyService;

  @InjectMocks
  AlertGroupHandler alertGroupHandler;

  @BeforeEach
  void setup() {
    ReflectionTestUtils.setField(alertGroupHandler, "processAlertAmounts", 100);
  }

  @Test
  void findNewPrimaryAlert_success_caseListEmpty() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var oldPrimaryAlertId = 1L;
    var alerts = new ArrayList<AlertEntity>();
    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(null);
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNull(res);
  }

  @Test
  void findNewPrimaryAlert_success_caseLastedAlert() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var oldPrimaryAlertId = 1L;
    var alert1 = new AlertEntity();
    alert1.setCreatedDate(new Date());
    alert1.setId(1L);
    var alert2 = new AlertEntity();
    alert2.setCreatedDate(new Date());
    alert2.setId(2L);
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(alert1);
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNotNull(res);
  }

  @Test
  void findNewPrimaryAlert_success_caseHighestPriority() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var oldPrimaryAlertId = 1L;
    var alert1 = new AlertEntity();
    alert1.setAlertPriorityConfigId(1L);
    alert1.setCreatedDate(new Date());
    var alert2 = new AlertEntity();
    alert2.setCreatedDate(new Date());
    alert2.setAlertPriorityConfigId(-1L);
    var alert3 = new AlertEntity();
    alert3.setCreatedDate(new Date());
    alert3.setAlertPriorityConfigId(3L);
    var alert4 = new AlertEntity();
    alert4.setCreatedDate(new Date());
    alert4.setAlertPriorityConfigId(4L);
    var alert5 = new AlertEntity();
    alert5.setCreatedDate(new Date());
    alert5.setAlertPriorityConfigId(5L);
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    alerts.add(alert3);
    alerts.add(alert4);
    alerts.add(alert5);
    var alertPriority1 = new AlertPriorityConfigEntity();
    alertPriority1.setId(1L);
    alertPriority1.setPosition(1);
    var alertPriority2 = new AlertPriorityConfigEntity();
    alertPriority2.setId(-1L);
    alertPriority2.setPosition(2);
    var alertPriority3 = new AlertPriorityConfigEntity();
    alertPriority3.setId(3L);
    alertPriority3.setPosition(3);
    var alertPriority4 = new AlertPriorityConfigEntity();
    alertPriority4.setId(4L);
    alertPriority4.setPosition(5);
    var alertPriority5 = new AlertPriorityConfigEntity();
    alertPriority5.setId(5L);
    alertPriority5.setPosition(4);
    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(alert1);
    Mockito.when(alertPriorityConfigService.findAll())
        .thenReturn(List.of(alertPriority1, alertPriority2, alertPriority3, alertPriority4, alertPriority5));
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNotNull(res);
  }

  @Test
  void findNewPrimaryAlert_success_caseLowestPriority() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.LOWEST_PRIORITY);
    var oldPrimaryAlertId = 1L;
    var alert1 = new AlertEntity();
    alert1.setAlertPriorityConfigId(1L);
    alert1.setCreatedDate(new Date());
    var alert2 = new AlertEntity();
    alert2.setCreatedDate(new Date());
    alert2.setAlertPriorityConfigId(-1L);
    var alert3 = new AlertEntity();
    alert3.setCreatedDate(new Date());
    alert3.setAlertPriorityConfigId(3L);
    var alert4 = new AlertEntity();
    alert4.setCreatedDate(new Date());
    alert4.setAlertPriorityConfigId(4L);
    var alert5 = new AlertEntity();
    alert5.setCreatedDate(new Date());
    alert5.setAlertPriorityConfigId(5L);
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    alerts.add(alert3);
    alerts.add(alert4);
    alerts.add(alert5);
    var alertPriority1 = new AlertPriorityConfigEntity();
    alertPriority1.setId(1L);
    alertPriority1.setPosition(1);
    var alertPriority2 = new AlertPriorityConfigEntity();
    alertPriority2.setId(-1L);
    alertPriority2.setPosition(2);
    var alertPriority3 = new AlertPriorityConfigEntity();
    alertPriority3.setId(3L);
    alertPriority3.setPosition(3);
    var alertPriority4 = new AlertPriorityConfigEntity();
    alertPriority4.setId(4L);
    alertPriority4.setPosition(5);
    var alertPriority5 = new AlertPriorityConfigEntity();
    alertPriority5.setId(5L);
    alertPriority5.setPosition(4);
    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(alert1);
    Mockito.when(alertPriorityConfigService.findAll())
        .thenReturn(List.of(alertPriority1, alertPriority2, alertPriority3, alertPriority4, alertPriority5));
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNotNull(res);
  }

  @Test
  void findNewPrimaryAlert_success_caseCustom() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    var oldPrimaryAlertId = 1L;
    var alert1 = new AlertEntity();
    alert1.setCreatedDate(new Date());
    var alert2 = new AlertEntity();
    alert2.setCreatedDate(new Date());
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(alert1);
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNotNull(res);
  }

  @Test
  void findNewPrimaryAlert_success_caseNotFoundPriorityConfig() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var oldPrimaryAlertId = 1L;
    var alert1 = new AlertEntity();
    alert1.setAlertPriorityConfigId(1L);
    alert1.setCreatedDate(new Date());
    var alert2 = new AlertEntity();
    alert2.setCreatedDate(new Date());
    alert2.setAlertPriorityConfigId(-1L);
    var alert3 = new AlertEntity();
    alert3.setCreatedDate(new Date());
    alert3.setAlertPriorityConfigId(3L);
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    alerts.add(alert3);
    var alertPriority1 = new AlertPriorityConfigEntity();
    alertPriority1.setId(5L);
    alertPriority1.setPosition(1);

    Mockito.when(alertService.findById(Mockito.anyLong())).thenReturn(alert1);
    Mockito.when(alertPriorityConfigService.findAll())
        .thenReturn(List.of(alertPriority1));
    var res = alertGroupHandler.findNewPrimaryAlert(alertGroupConfig, alerts, oldPrimaryAlertId);
    Assertions.assertNotNull(res);
  }


  @Test
  void createSingleAlertGroups_success() {
    var alert1 = new AlertEntity();
    alert1.setCreatedDate(new Date());
    alert1.setServiceId("1");
    alert1.setApplicationId("1");
    alert1.setId(1L);
    var alert2 = new AlertEntity();
    alert2.setId(2L);
    alert2.setServiceId("1");
    alert2.setApplicationId("1");
    alert2.setCreatedDate(new Date());
    var alerts = new ArrayList<AlertEntity>();
    alerts.add(alert2);
    alerts.add(alert1);
    Mockito.when(alertService.saveAll(Mockito.anyList())).thenReturn(List.of());
    Mockito.when(alertGroupService.saveAll(Mockito.anyList())).thenReturn(List.of());
    Assertions.assertDoesNotThrow(() -> alertGroupHandler.createSingleAlertGroups(alerts));
  }

  @Test
  void addAlertsToNewGroup_success_case1Alert() {
    var alerts = List.<AlertEntity>of();
    var res = alertGroupHandler.addAlertsToNewGroup(alerts, new AlertGroupConfigEntity(), "");
    Assertions.assertEquals(0, res.size());
  }

  @Test
  void addAlertsToNewGroup_success_case2Alert() {
    var alerts = List.of(new AlertEntity(), new AlertEntity());
    var groupConfig = new AlertGroupConfigEntity();
    groupConfig.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var res = alertGroupHandler.addAlertsToNewGroup(alerts, groupConfig, "");
    Assertions.assertEquals(2, res.size());
  }

  @Test
  void addAlertsToNewGroup_success_caseCustom() {
    var alerts = List.of(new AlertEntity(), new AlertEntity());
    var groupConfig = new AlertGroupConfigEntity();
    groupConfig.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    var newPrimaryAlert = new AlertEntity();
    newPrimaryAlert.setAlertPriorityConfigId(1L);
    newPrimaryAlert.setServiceId("1");
    newPrimaryAlert.setApplicationId("1");
    newPrimaryAlert.setStatus(AlertStatusEnum.NEW);
    newPrimaryAlert.setContent("abc");
    newPrimaryAlert.setRecipient("abc");
    Mockito.when(alertService.save(Mockito.any())).thenReturn(newPrimaryAlert);
    var res = alertGroupHandler.addAlertsToNewGroup(alerts, groupConfig, "");
    Assertions.assertEquals(2, res.size());
  }

  @Test
  void addAlertsToExistedGroup_success() {
    var groupConfig = new AlertGroupConfigEntity();
    groupConfig.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    Mockito.when(alertService.saveAll(Mockito.anyList())).thenReturn(List.of(new AlertEntity()));
    var res =
        alertGroupHandler.addAlertsToExistedGroup(List.of(new AlertEntity()), groupConfig, new AlertGroupEntity());
    Assertions.assertNotNull(res);
  }

  @Test
  void addAlertsToExistedGroup_success_casePrimaryAlertNull() {
    Mockito.when(alertService.saveAll(Mockito.anyList())).thenReturn(List.of());
    var res =
        alertGroupHandler.addAlertsToExistedGroup(List.of(), new AlertGroupConfigEntity(), new AlertGroupEntity());
    Assertions.assertNotNull(res);
  }

  @Test
  void getAlertValueMap_success() {
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var alert = new AlertEntity();
    alert.setContent("abc");
    alert.setAlertPriorityConfigId(1L);
    var res = CustomObjectUtils.getAlertConditionValueMap(alert, List.of(customObject));
    Assertions.assertNotNull(res);
  }

  @Test
  void getGroupValue_success() {
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var res = alertGroupHandler.getGroupValue(List.of(customObject), "abc");
    Assertions.assertEquals("bc", res);
  }

  @Test
  void getCustomObjectIds_success() {
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    var ruleGroupType2 = new RuleGroupType();
    ruleGroupType2.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroupType2.setRules(List.of());
    var ruleCondition = new RuleCondition<>();
    ruleCondition.setField("123");
    ruleCondition.setOperator(OperatorEnum.IS);
    ruleCondition.setValue("123");
    var ruleCondition1 = new RuleCondition<String>();
    ruleCondition1.setField("abc");
    ruleCondition1.setOperator(OperatorEnum.IS);
    ruleCondition1.setValue("abc");
    ruleGroupType.setRules(List.of(ruleCondition, ruleGroupType2, ruleCondition1));

    var res = alertGroupHandler.getCustomObjectIds(ruleGroupType);
    Assertions.assertEquals(123, res.get(0));
  }

  @Test
  void handleMultipleConditionGroup_success() {
    var alert1 = new AlertEntity();
    alert1.setCreatedDate(new Date());
    alert1.setServiceId("1");
    alert1.setApplicationId("1");
    alert1.setContent("123");
    alert1.setId(1L);
    var alert2 = new AlertEntity();
    alert1.setCreatedDate(new Date());
    alert1.setServiceId("1");
    alert1.setApplicationId("1");
    alert1.setContent("123");
    alert1.setId(2L);
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.CUSTOM);
    var alertGroup = new AlertGroupEntity();
    var customObject = new CustomObjectEntity();
    customObject.setId(1L);
    customObject.setType(CustomObjectTypeEnum.REGEX);
    customObject.setRegex("");
    var groupConfigCondition = new AlertGroupConfigConditionEntity();
    groupConfigCondition.setId(1L);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    var ruleGroupType2 = new RuleGroupType();
    ruleGroupType2.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroupType2.setRules(List.of());
    var ruleCondition = new RuleCondition<String>();
    ruleCondition.setField("content");
    ruleCondition.setOperator(OperatorEnum.IS);
    ruleCondition.setValue("123");
    var ruleCondition1 = new RuleCondition<String>();
    ruleCondition1.setField("1");
    ruleCondition1.setOperator(OperatorEnum.IS);
    ruleCondition1.setValue("abc");
    ruleGroupType.setRules(List.of(ruleCondition, ruleGroupType2, ruleCondition1));
    groupConfigCondition.setRuleGroup(ruleGroupType);
    Mockito.when(alertGroupService.findByAlertGroupConfigIdAndStatus(Mockito.anyLong(), Mockito.any())).thenReturn(
        Optional.of(alertGroup));
    Mockito.when(customObjectService.findAll()).thenReturn(List.of(customObject));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(groupConfigCondition));
    var res = alertGroupHandler.handleMultipleConditionGroup(alertGroupConfig, List.of(alert1, alert2));
    Assertions.assertNotNull(res);
  }

  @Test
  void handleMultipleConditionGroup_success_caseAlertGroupNotFound() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    var alertGroup = new AlertGroupEntity();
    var customObject = new CustomObjectEntity();
    customObject.setId(1L);
    var groupConfigCondition = new AlertGroupConfigConditionEntity();
    groupConfigCondition.setId(1L);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
    var ruleGroupType2 = new RuleGroupType();
    ruleGroupType2.setCombinator(ConditionCombinatorEnum.AND);
    ruleGroupType2.setRules(List.of());
    var ruleCondition = new RuleCondition<String>();
    ruleCondition.setField("123");
    ruleCondition.setOperator(OperatorEnum.IS);
    ruleCondition.setValue("123");
    var ruleCondition1 = new RuleCondition<String>();
    ruleCondition1.setField("abc");
    ruleCondition1.setOperator(OperatorEnum.IS);
    ruleCondition1.setValue("abc");
    ruleGroupType.setRules(List.of(ruleCondition, ruleGroupType2, ruleCondition1));
    groupConfigCondition.setRuleGroup(ruleGroupType);
    Mockito.when(alertGroupService.findByAlertGroupConfigIdAndStatus(Mockito.anyLong(), Mockito.any())).thenReturn(
        Optional.empty());
    Mockito.when(customObjectService.findAll()).thenReturn(List.of(customObject));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(groupConfigCondition));
    var res = alertGroupHandler.handleMultipleConditionGroup(alertGroupConfig, List.of());
    Assertions.assertNotNull(res);
  }

  @Test
  void handleMultipleConditionGroup_success_caseCreateANewGroup() {
    var alert = new AlertEntity();
    alert.setId(1L);
    alert.setContent("abc");
    alert.setAlertPriorityConfigId(1L);
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    var alertGroup = new AlertGroupEntity();
    var customObject = new CustomObjectEntity();
    customObject.setId(1L);
    var groupConfigCondition = new AlertGroupConfigConditionEntity();
    groupConfigCondition.setId(1L);
    var ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    var ruleGroupType2 = new RuleGroupType();
    ruleGroupType2.setCombinator(ConditionCombinatorEnum.OR);
    ruleGroupType2.setRules(List.of());
    var ruleCondition = new RuleCondition<String>();
    ruleCondition.setField("content");
    ruleCondition.setOperator(OperatorEnum.IS);
    ruleCondition.setValue("abc");
    var ruleCondition1 = new RuleCondition<String>();
    ruleCondition1.setField("abc");
    ruleCondition1.setOperator(OperatorEnum.IS);
    ruleCondition1.setValue("abc");
    ruleGroupType.setRules(List.of(ruleCondition, ruleGroupType2, ruleCondition1));
    groupConfigCondition.setRuleGroup(ruleGroupType);
    Mockito.when(alertGroupService.findByAlertGroupConfigIdAndStatus(Mockito.anyLong(), Mockito.any())).thenReturn(
        Optional.empty());
    Mockito.when(customObjectService.findAll()).thenReturn(List.of(customObject));
    Mockito.when(alertGroupConfigConditionService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(groupConfigCondition));
    var res = alertGroupHandler.handleMultipleConditionGroup(alertGroupConfig, List.of(alert));
    Assertions.assertNotNull(res);
  }

  @Test
  void group() {
    var alert = new AlertEntity();
    alert.setServiceId("2");
    alert.setApplicationId("2");
    alert.setAlertGroupId(0L);
    var alert2 = new AlertEntity();
    alert2.setServiceId("3");
    alert2.setApplicationId("3");
    alert2.setAlertGroupId(1L);
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setPosition(1);
    alertGroupConfig.setId(1L);
    var alertConfigs = new ArrayList<AlertGroupConfigEntity>();
    alertConfigs.add(alertGroupConfig);
    Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    Mockito.when(alertService.findTopAlertsByAlertGroupIdAndStatus(Mockito.anyLong(), Mockito.any(), Mockito.anyInt()))
        .thenReturn(List.of(alert, alert2));
    Mockito.when(applicationService.findAllByServiceIdInAndDeleted(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(List.of());
    Mockito.when(alertGroupConfigService.findAllByDeletedAndActive(Mockito.anyBoolean(), Mockito.anyBoolean()))
        .thenReturn(alertConfigs);
    alertGroupHandler.group();
  }

  @Test
  void handleAlertGroupConfig_success_caseHaveNotAlertToGroup() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    var alert = new AlertEntity();
    alert.setServiceId("2");
    alert.setApplicationId("2");
    var serviceDependency = new AlertGroupConfigDependencyEntity();
    serviceDependency.setId(1L);
    serviceDependency.setDependencyId("2");
    serviceDependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE);
    var appDependency = new AlertGroupConfigDependencyEntity();
    appDependency.setId(2L);
    appDependency.setDependencyId("2");
    appDependency.setType(AlertGroupConfigDependencyTypeEnum.APPLICATION);
    var serviceWithAppDependency = new AlertGroupConfigDependencyEntity();
    serviceWithAppDependency.setId(2L);
    serviceWithAppDependency.setDependencyId("2");
    serviceWithAppDependency.setType(AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of(serviceDependency, appDependency, serviceWithAppDependency));
    Mockito.when(
        alertService.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(Mockito.anyList(),
            Mockito.anyList(),
            Mockito.anyInt(), Mockito.any())).thenReturn(List.of());
    var res = alertGroupHandler.handleAlertGroupConfig(alertGroupConfig, List.of(alert));
    Assertions.assertEquals(1, res.size());
  }

  @Test
  void handleAlertGroupConfig_success_caseHaveAlertToGroup() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    var alert = new AlertEntity();
    alert.setServiceId("1");
    alert.setApplicationId("1");
    Mockito.when(alertGroupConfigDependencyService.findAllByAlertGroupConfigId(Mockito.anyLong()))
        .thenReturn(List.of());
    Mockito.when(
        alertService.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(Mockito.anyList(),
            Mockito.anyList(),
            Mockito.anyInt(), Mockito.any())).thenReturn(List.of());
    var res = alertGroupHandler.handleAlertGroupConfig(alertGroupConfig, List.of(alert));
    Assertions.assertEquals(1, res.size());
  }

  @Test
  void handleSameObjectValueGroup_success_caseHave1AlertMatch() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    var alert = new AlertEntity();
    alert.setId(1L);
    alert.setContent("aalertcontent");
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue("a");
    alertGroup.setId(1L);
    Mockito.when(customObjectService.findAllByAlertGroupConfigId(Mockito.anyLong())).thenReturn(List.of(customObject));
    Mockito.when(
        alertGroupService.findByAlertGroupConfigIdAndMatchValueInAndStatus(Mockito.anyLong(), Mockito.anyList(),
            Mockito.any())).thenReturn(List.of(alertGroup));
    var res = alertGroupHandler.handleSameObjectValueGroup(alertGroupConfig, List.of(alert));
    Assertions.assertNotNull(res);
  }

  @Test
  void handleSameObjectValueGroup_success_caseHave1AlertMatchAddToOldGroup() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.LASTED_ALERT);
    var alert = new AlertEntity();
    alert.setId(1L);
    alert.setContent("aalertcontent");
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(1);
    customObject.setFromIndex(1);
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue("a");
    alertGroup.setId(1L);
    Mockito.when(customObjectService.findAllByAlertGroupConfigId(Mockito.anyLong())).thenReturn(List.of(customObject));
    Mockito.when(
        alertGroupService.findByAlertGroupConfigIdAndMatchValueInAndStatus(Mockito.anyLong(), Mockito.anyList(),
            Mockito.any())).thenReturn(List.of(alertGroup));
    var res = alertGroupHandler.handleSameObjectValueGroup(alertGroupConfig, List.of(alert));
    Assertions.assertNotNull(res);
  }

  @Test
  void handleSameObjectValueGroup_success_caseHave2AlertMatch() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var alert = new AlertEntity();
    alert.setId(1L);
    alert.setContent("aalertcontent");
    var alert1 = new AlertEntity();
    alert1.setId(2L);
    alert1.setContent("aalertcontent");
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue("a");
    alertGroup.setId(1L);
    Mockito.when(customObjectService.findAllByAlertGroupConfigId(Mockito.anyLong())).thenReturn(List.of(customObject));
    Mockito.when(
        alertGroupService.findByAlertGroupConfigIdAndMatchValueInAndStatus(Mockito.anyLong(), Mockito.anyList(),
            Mockito.any())).thenReturn(List.of(alertGroup));
    var res = alertGroupHandler.handleSameObjectValueGroup(alertGroupConfig, List.of(alert, alert1));
    Assertions.assertNotNull(res);
  }

  @Test
  void handleSameObjectValueGroup_success_caseCreateANewGroup() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    alertGroupConfig.setId(1L);
    alertGroupConfig.setAlertOutput(AlertGroupOutputEnum.HIGHEST_PRIORITY);
    var alert = new AlertEntity();
    alert.setId(1L);
    alert.setContent("aalertcontent");
    alert.setAlertPriorityConfigId(1L);
    var alert1 = new AlertEntity();
    alert1.setId(2L);
    alert1.setContent("aalertcontent");
    alert.setAlertGroupId(3L);
    CustomObjectEntity customObject = new CustomObjectEntity();
    customObject.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObject.setToIndex(2);
    customObject.setFromIndex(1);
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue("n");
    alertGroup.setId(1L);
    Mockito.when(customObjectService.findAllByAlertGroupConfigId(Mockito.anyLong())).thenReturn(List.of(customObject));
    Mockito.when(
        alertGroupService.findByAlertGroupConfigIdAndMatchValueInAndStatus(Mockito.anyLong(), Mockito.anyList(),
            Mockito.any())).thenReturn(List.of(alertGroup));
    var res = alertGroupHandler.handleSameObjectValueGroup(alertGroupConfig, List.of(alert, alert1));
    Assertions.assertNotNull(res);
  }

  @Test
  void getLowestPriorityAlert_success() {
    var alert = new AlertEntity();
    alert.setAlertPriorityConfigId(1L);
    var alert2 = new AlertEntity();
    alert2.setAlertPriorityConfigId(-1L);
    var alert3 = new AlertEntity();
    alert2.setAlertPriorityConfigId(-1L);
    var alertPriority1 = new AlertPriorityConfigEntity();
    alertPriority1.setId(1L);
    alertPriority1.setPosition(1);
    var alertPriority2 = new AlertPriorityConfigEntity();
    alertPriority2.setId(-1L);
    alertPriority2.setPosition(2);
    var alertPriority4 = new AlertPriorityConfigEntity();
    alertPriority4.setId(-4L);
    alertPriority4.setPosition(4);
    var alertPriority3 = new AlertPriorityConfigEntity();
    alertPriority3.setId(3L);
    alertPriority3.setPosition(3);
    Mockito.when(alertPriorityConfigService.findAll())
        .thenReturn(List.of(alertPriority1, alertPriority2, alertPriority3, alertPriority4));
    AlertEntity alertEntity = ReflectionTestUtils.invokeMethod(
        alertGroupHandler, "getLowestPriorityAlert", List.of(alert, alert2, alert3)
    );
    assertNotNull(alertEntity);
  }

  @Test
  void getLowestPriorityAlert_success_case() {
    var alert = new AlertEntity();
    alert.setAlertPriorityConfigId(1L);
    var alert2 = new AlertEntity();
    alert2.setAlertPriorityConfigId(-1L);
    var alert3 = new AlertEntity();
    alert2.setAlertPriorityConfigId(-1L);
    var alertPriority2 = new AlertPriorityConfigEntity();
    alertPriority2.setId(-1L);
    alertPriority2.setPosition(2);
    var alertPriority4 = new AlertPriorityConfigEntity();
    alertPriority4.setId(-4L);
    alertPriority4.setPosition(4);
    var alertPriority3 = new AlertPriorityConfigEntity();
    alertPriority3.setId(3L);
    alertPriority3.setPosition(3);
    Mockito.when(alertPriorityConfigService.findAll())
        .thenReturn(List.of(alertPriority2, alertPriority3, alertPriority4));
    AlertEntity alertEntity = ReflectionTestUtils.invokeMethod(
        alertGroupHandler, "getLowestPriorityAlert", List.of(alert, alert2, alert3)
    );
    assertNotNull(alertEntity);
  }

  @Test
  void getCustomPrimaryAlert_success_caseOldPrimaryAlertExist() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    var oldPrimaryAlert = new AlertEntity();
    AlertEntity alertEntity = ReflectionTestUtils.invokeMethod(
        alertGroupHandler, "getCustomPrimaryAlert", alertGroupConfig, oldPrimaryAlert
    );
    assertNotNull(alertEntity);
  }

  @Test
  void getCustomPrimaryAlert_success_caseOldPrimaryAlertNotExist() {
    var alertGroupConfig = new AlertGroupConfigEntity();
    Mockito.when(alertService.save(Mockito.any())).thenReturn(new AlertEntity());
    AlertEntity alertEntity = ReflectionTestUtils.invokeMethod(
        alertGroupHandler, "getCustomPrimaryAlert", alertGroupConfig, (Object) null
    );
    assertNotNull(alertEntity);
  }
}

