package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import jakarta.mail.Address;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Store;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.search.FlagTerm;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.EmailConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models.EmailModel;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.CollectEmailConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.EmailService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.EmailUtils;

@ExtendWith(MockitoExtension.class)
public class CollectEmailSchedulerServiceImplTest {

  @Mock
  private EmailService emailService;

  @Mock
  private EmailConfigService emailConfigService;
  @Mock
  private CollectEmailConfigRepository collectEmailConfigRepository;

  @Mock
  private FilterAlertConfigService filterAlertConfigService;
  @Mock
  private ModifyAlertConfigService modifyAlertConfigService;
  @Mock
  private MaintenanceTimeConfigService maintenanceTimeConfigService;

  @Mock
  private AlertService alertService;

  @Mock
  private CustomObjectService customObjectService;
  @Mock
  private CommonRawAlertService commonRawAlertService;
  @Mock
  private AlertPriorityConfigService alertPriorityConfigService;

  @Mock
  private Executor taskExecutor;

  @InjectMocks
  private CollectEmailSchedulerServiceImpl collectEmailSchedulerService;
  @Mock
  private Store store;

  @Mock
  private Folder folder;

  @Captor
  private ArgumentCaptor<List<AlertEntity>> alertEntitiesCaptor;

  private static final Long TEST_EMAIL_CONFIG_ID = 1L;

  @BeforeEach
  public void setUp() throws Exception {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(50);
    executor.setQueueCapacity(500);
    executor.setThreadNamePrefix("Request-");
    executor.initialize();
    taskExecutor = executor;
    setPrivateField(collectEmailSchedulerService, "commonTaskExecutor", taskExecutor);


  }

  private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
    Field field = target.getClass().getDeclaredField(fieldName);
    field.setAccessible(true);
    field.set(target, value);
  }

  @Test
  void processMessagesInFolder_executed_FlagsMessages() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(true);
    when(folder.search(any(FlagTerm.class))).thenReturn(new Message[1]);
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      // Act
      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );

      // Assert
      verify(folder).open(Folder.READ_WRITE);
    }
  }

  @Test
  void processMessagesInFolder_executed_processesMessages() throws Exception {
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any()))
          .thenReturn(new EmailModel());

      EmailConfigModel emailConfigModel = new EmailConfigModel();
      Message[] messages = new Message[1];
      Message message = mock(MimeMessage.class);
//      when(message.getSubject()).thenReturn("12");
//      when(message.getContent()).thenReturn("12312321312312");
//      when(message.getFrom()).thenReturn(new Address[0]);
      messages[0] = message;
      emailConfigModel.setExecuted(true);
      when(folder.search(any(FlagTerm.class))).thenReturn(messages);
//      when(customObjectService.calculatorCustomObjectValue(any(), any())).thenReturn("12");
      RuleGroupType ruleGroupType = RuleGroupType.builder().build();
      RuleCondition ruleCondition1 = new RuleCondition<>("subject", OperatorEnum.IS, "12");
      RuleCondition ruleCondition = new RuleCondition<>("12", OperatorEnum.IS, "12");
      ruleGroupType.setRules(List.of(ruleCondition, ruleCondition1));
      ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);
      CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
      collectEmailConfigEntity.setRuleGroup(ruleGroupType);
      collectEmailConfigEntity.setContentType(CollectEmailAlertContentTypeEnum.SAME_BODY);

      // Act
      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfigEntity)
      );

      verify(folder).open(Folder.READ_WRITE);
    }
  }

  @Test
  void processMessagesInFolder_Executed_ProcessesMessages_customObject() throws Exception {
    Message[] messages = new Message[1];
    Message message = mock(MimeMessage.class);
    when(message.getFrom()).thenReturn(new Address[0]);
    when(message.getSubject()).thenReturn("");
    messages[0] = message;
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(MimeMessage.class)))
          .thenReturn(EmailModel.builder().textBody("123").subject("1").build());

      EmailConfigModel emailConfigModel = new EmailConfigModel();
      emailConfigModel.setExecuted(true);
      when(folder.search(any(FlagTerm.class))).thenReturn(messages);

      RuleGroupType ruleGroupType = RuleGroupType.builder().build();
      RuleGroupType ruleGroupType2 = RuleGroupType.builder().build();
      ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
      ruleGroupType2.setRules(List.of(new RuleCondition<>("subject", OperatorEnum.IS, "22222")));
      RuleCondition ruleCondition1 = new RuleCondition<>("subject", OperatorEnum.IS, "");
      RuleCondition ruleCondition2 = new RuleCondition<>("1", OperatorEnum.IS, "");
      when(customObjectService.calculatorCustomObjectValue(any(),any())).thenReturn("");
      when(customObjectService.replaceCustomObjectIdsWithCustomObjectValues(any(),any())).thenReturn("bao ne");
      ruleGroupType.setRules(List.of(ruleCondition1,ruleCondition2));
      ruleGroupType.setCombinator(ConditionCombinatorEnum.AND);

      CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
      collectEmailConfigEntity.setRuleGroup(ruleGroupType);
      collectEmailConfigEntity.setServiceId("testServiceId");
      collectEmailConfigEntity.setApplicationId("testApplicationId");
      collectEmailConfigEntity.setRecipient("testRecipient");
      collectEmailConfigEntity.setType(CollectEmailConfigTypeEnum.EVENT_BASE_ALERT);
      CollectEmailConfigEntity collectEmailConfigEntity2 = new CollectEmailConfigEntity();
      collectEmailConfigEntity2.setRuleGroup(ruleGroupType);
      collectEmailConfigEntity2.setServiceId("testServiceId");
      collectEmailConfigEntity2.setApplicationId("testApplicationId");
      collectEmailConfigEntity2.setRecipient("testRecipient");
      collectEmailConfigEntity2.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
      CollectEmailConfigEntity collectEmailConfigEntity3 = new CollectEmailConfigEntity();
      collectEmailConfigEntity3.setRuleGroup(ruleGroupType);
      collectEmailConfigEntity3.setServiceId("testServiceId");
      collectEmailConfigEntity3.setApplicationId("testApplicationId");
      collectEmailConfigEntity3.setRecipient("testRecipient");
      collectEmailConfigEntity3.setContentType(CollectEmailAlertContentTypeEnum.CUSTOM_CONTENT);
      collectEmailConfigEntity3.setContentValue("123");
      collectEmailConfigEntity3.setType(CollectEmailConfigTypeEnum.EVENT_BASE_ALERT);
      CollectEmailConfigEntity collectEmailConfigEntity4 = new CollectEmailConfigEntity();
      collectEmailConfigEntity4.setRuleGroup(ruleGroupType2);
      collectEmailConfigEntity4.setServiceId("testServiceId");
      collectEmailConfigEntity4.setApplicationId("testApplicationId");
      collectEmailConfigEntity4.setRecipient("testRecipient");
      collectEmailConfigEntity4.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
      when(commonRawAlertService.createRawData(any(),any())).thenReturn(List.of(new AlertBaseModel()));
      // Act
      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfigEntity,collectEmailConfigEntity3,collectEmailConfigEntity2,collectEmailConfigEntity4)
      );

      verify(folder).open(Folder.READ_WRITE);
      verify(folder).close();
    }
  }

  @Test
  void processMessagesInFolder_ExceptionHandling() throws Exception {
    Message[] messages = new Message[1];
    Message message = mock(MimeMessage.class);
    messages[0] = message;
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));


      EmailConfigModel emailConfigModel = new EmailConfigModel();
      emailConfigModel.setExecuted(true);
      when(folder.search(any(FlagTerm.class))).thenReturn(messages);

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      // Act
      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );
      verify(folder).open(Folder.READ_WRITE);
    }
  }

  @Test
  void processMessagesInFolder_NoMessages() throws Exception {

    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(false);
    when(folder.search(any(FlagTerm.class))).thenReturn(new Message[0]);
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      // Act
      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );
    }

    verify(folder).open(Folder.READ_WRITE);
  }

  @Test
  void fetchAndProcessEmails_EmailConfigNotFound() throws BusinessException {
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(null);

    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);

    verifyNoInteractions(emailService, collectEmailConfigRepository);
  }

  @Test
  void fetchAndProcessEmails_NoActiveCollectEmailConfigs() throws BusinessException {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(
        TEST_EMAIL_CONFIG_ID)).thenReturn(
        Collections.emptyList());
    emailConfigEntity.setActive(true);

    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);

    verifyNoInteractions(emailService);
  }

  @Test
  void fetchAndProcessEmails_ValidProcess() throws Exception {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(false);
    emailConfigEntity.setActive(true);
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(false);

    CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
    collectEmailConfig.setServiceId("testServiceId");
    collectEmailConfig.setApplicationId("testApplicationId");
    collectEmailConfig.setRecipient("testRecipient");

    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailService.connectImapServer(any())).thenReturn(store);
    when(store.getFolder(anyString())).thenReturn(folder);
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(TEST_EMAIL_CONFIG_ID))
        .thenReturn(Collections.singletonList(collectEmailConfig));
    when(emailService.connectImapServer(emailConfigModel)).thenReturn(store);

    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);

  }

  @Test
  void processFolders_ProcessesAllFolders() throws MessagingException {
    when(store.getFolder(anyString())).thenReturn(folder);
    when(folder.list()).thenReturn(new Folder[0]);
    when(folder.getType()).thenReturn(Folder.HOLDS_MESSAGES);
    collectEmailSchedulerService.processFolders(store, new EmailConfigModel(), new ArrayList<>());

    verify(folder).open(Folder.READ_WRITE);
  }
  @TestForUser
  void processFolders_configAbsenceAlert() throws MessagingException {
    var config1 = new CollectEmailConfigEntity();
    Date lastReceived = new Date(System.currentTimeMillis() - 10_000);
    config1.setLastReceivedEmailDate(lastReceived);
    config1.setAbsenceInterval(8L);
    config1.setAlertRepeatInterval(10L);
    config1.setIntervalTime(3L);
    var config2 = new CollectEmailConfigEntity();
    config2.setLastReceivedEmailDate(lastReceived);
    config2.setAbsenceInterval(5L);
    config2.setAlertRepeatInterval(9L);
    config2.setIntervalTime(3L);
    var config3 = new CollectEmailConfigEntity();
    config3.setLastReceivedEmailDate(lastReceived);
    config3.setAbsenceInterval(5L);
    config3.setAlertRepeatInterval(14L);
    config3.setIntervalTime(3L);
    config1.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
    config2.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
    config3.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
    var config4 = new CollectEmailConfigEntity();
    config4.setType(CollectEmailConfigTypeEnum.EVENT_BASE_ALERT);
    var config5 = new CollectEmailConfigEntity();
    config5.setLastReceivedEmailDate(lastReceived);
    config5.setAbsenceInterval(5L);
    config5.setAlertRepeatInterval(14L);
    config5.setIntervalTime(3L);
    config1.setType(CollectEmailConfigTypeEnum.ABSENCE_ALERT);
    List<CollectEmailConfigEntity> configs = new ArrayList<>(List.of(config1, config2, config3, config4, config5));
    when(store.getFolder(anyString())).thenReturn(folder);
    when(folder.list()).thenReturn(new Folder[0]);
    when(folder.getType()).thenReturn(Folder.HOLDS_MESSAGES);
    collectEmailSchedulerService.processFolders(store, new EmailConfigModel(), configs);

    verify(folder).open(Folder.READ_WRITE);
  }

  @Test
  void processMessagesInFolder_HandlesMessages() throws MessagingException {
    Message message = mock(MimeMessage.class);
    when(folder.search(any(FlagTerm.class))).thenReturn(new Message[] {message});

    List<AlertEntity> alertEntities = new ArrayList<>();
    collectEmailSchedulerService.processMessagesInFolder(folder, new EmailConfigModel(),
        new ArrayList<>());

  }

  @Test
  void fetchAndProcessEmails_ThrowsExceptionOnError() {
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenThrow(
        new RuntimeException("Test exception"));

    RuntimeException exception = assertThrows(
        RuntimeException.class,
        () -> collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID)
    );

  }

  @Test
  void fetchAndProcessEmails_throwsExceptionReadFail() throws BusinessException {
    var emailConfig = new EmailConfigEntity();
    emailConfig.setActive(true);
    when(emailConfigService.findById(any())).thenReturn(emailConfig);
    var collectConfig = new CollectEmailConfigEntity();
    collectConfig.setActive(true);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(any())).thenReturn(
        List.of(collectConfig));
    when(emailService.connectImapServer(any())).thenThrow(
        new BusinessException("Test exception", HttpStatus.BAD_REQUEST));

    assertThrows(
        BusinessException.class,
        () -> collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID)
    );

  }

  @Test
  public void fetchAndProcessEmails_success_withMatchingEmails()
      throws BusinessException, MessagingException {
    store = mock(Store.class);
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailService.connectImapServer(any())).thenReturn(store);
    when(store.getFolder(anyString())).thenReturn(folder);
    Long emailConfigId = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(true);
    emailConfigEntity.setActive(true);
    RuleGroupType ruleGroupType = RuleGroupType.builder().build();
    CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
    collectEmailConfigEntity.setRuleGroup(ruleGroupType);
    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(emailConfigId))
        .thenReturn(List.of(collectEmailConfigEntity));

    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void fetchAndProcessEmails_success_emailConfigEntityIsNull()
      throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    store = mock(Store.class);
    folder = mock(Folder.class);
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(true);
    emailConfigEntity.setActive(true);
    RuleGroupType ruleGroupType = RuleGroupType.builder().build();
    CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
    collectEmailConfigEntity.setRuleGroup(ruleGroupType);
    when(emailConfigService.findById(emailConfigId)).thenReturn(null);
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
    verify(emailConfigService, times(1)).findById(any());
  }

  @Test
  public void fetchAndProcessEmails_noEmailsFound() throws BusinessException {
    Long emailConfigId = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();

    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);

    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);

    verify(alertService, never()).save(any(AlertEntity.class));

  }

  @Test
  public void fetchAndProcessEmails_noMailsIsEmpty() throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailService.connectImapServer(any())).thenReturn(store);
    when(store.getFolder(anyString())).thenReturn(folder);
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setActive(true);

    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(any())).thenReturn(
        List.of(new CollectEmailConfigEntity()));
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void fetchAndProcessEmails_configIsExecuted()
      throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(store.getFolder(anyString())).thenReturn(folder);
    when(emailService.connectImapServer(any())).thenReturn(store);
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(true);
    emailConfigEntity.setActive(true);
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    RuleCondition ruleCondition = new RuleCondition<>("1", OperatorEnum.GREATER_THAN, "21");
    ruleGroupType.setRules(List.of(ruleCondition));
    CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
    collectEmailConfigEntity.setRuleGroup(ruleGroupType);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(any())).thenReturn(
        List.of(collectEmailConfigEntity));
    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void fetchAndProcessEmails_configIsNotExecuted()
      throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(store.getFolder(anyString())).thenReturn(folder);
    when(emailService.connectImapServer(any())).thenReturn(store);
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(false);
    emailConfigEntity.setActive(true);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(any())).thenReturn(
        List.of(new CollectEmailConfigEntity()));
    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void fetchAndProcessEmails_configIsExecuted_createMail()
      throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(true);
    emailConfigEntity.setActive(true);
    RuleGroupType ruleGroupType = new RuleGroupType();
    ruleGroupType.setCombinator(ConditionCombinatorEnum.OR);
    RuleCondition ruleCondition = new RuleCondition<>("1", OperatorEnum.GREATER_THAN, "21");
    ruleGroupType.setRules(List.of(ruleCondition));
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(store.getFolder(anyString())).thenReturn(folder);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailService.connectImapServer(any())).thenReturn(store);
    CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
    collectEmailConfigEntity.setRuleGroup(ruleGroupType);
    when(emailService.connectImapServer(any())).thenReturn(store);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(any())).thenReturn(
        List.of(collectEmailConfigEntity));
    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void fetchAndProcessEmails_noMatchingCollectEmailConfigs() throws BusinessException {
    Long emailConfigId = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setActive(true);

    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(emailConfigId))
        .thenReturn(Collections.emptyList());

    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);

    verify(alertService, never()).save(any(AlertEntity.class));
  }

  @Test
  public void fetchAndProcessEmails_noRulesMatched() throws BusinessException, MessagingException {
    Long emailConfigId = 1L;
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(true);
    emailConfigEntity.setActive(true);
    store = mock(Store.class);
    folder = mock(Folder.class);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailService.connectImapServer(any())).thenReturn(store);
    when(store.getFolder(anyString())).thenReturn(folder);
    CollectEmailConfigEntity collectEmailConfigEntity = new CollectEmailConfigEntity();
    EmailModel emailModel = new EmailModel();
    emailModel.setTextBody("3423432");
    RuleGroupType ruleGroupType =
        RuleGroupType.builder().rules(List.of()).combinator(ConditionCombinatorEnum.OR).build();
    collectEmailConfigEntity.setRuleGroup(ruleGroupType);
    when(emailConfigService.findById(emailConfigId)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(emailConfigId))
        .thenReturn(List.of(collectEmailConfigEntity));
    collectEmailSchedulerService.fetchAndProcessEmails(emailConfigId);
  }

  @Test
  public void createAlert_success() {
    EmailModel emailModel = new EmailModel();
    emailModel.setTextBody("Test content");
    emailModel.setSubject("123");
    CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
    collectEmailConfig.setContentValue("Buồn ngủ quá@123");
    collectEmailConfig.setContentType(CollectEmailAlertContentTypeEnum.CUSTOM_CONTENT);
    collectEmailConfig.setPriorityConfigId(1L);
    var priority = new AlertPriorityConfigEntity();
    priority.setName("213");
    priority.setId(1L);
    when(alertPriorityConfigService.findById(any())).thenReturn(priority);
    when(customObjectService.replaceCustomObjectIdsWithCustomObjectValues(any(),any())).thenReturn("12");
    when(commonRawAlertService.createRawData(any(),any())).thenReturn(null);
    var res = collectEmailSchedulerService.createAlerts(emailModel, collectEmailConfig);
    Assertions.assertNull(res);
  }
  @Test
  public void createAlert_success_priorityNull() {
    EmailModel emailModel = new EmailModel();
    emailModel.setTextBody("Test content");
    emailModel.setSubject("123");
    CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
    collectEmailConfig.setContentValue("Buồn ngủ quá@123");
    collectEmailConfig.setContentType(CollectEmailAlertContentTypeEnum.CUSTOM_CONTENT);
    collectEmailConfig.setPriorityConfigId(1L);
    var priority = new AlertPriorityConfigEntity();
    priority.setName("213");
    priority.setId(1L);
    when(alertPriorityConfigService.findById(any())).thenReturn(null);
    when(customObjectService.replaceCustomObjectIdsWithCustomObjectValues(any(),any())).thenReturn("12");
    when(commonRawAlertService.createRawData(any(),any())).thenReturn(null);
    var res = collectEmailSchedulerService.createAlerts(emailModel, collectEmailConfig);
    Assertions.assertNull(res);
  }


  @Test
  public void maintenanceAlert_success() {
    when(maintenanceTimeConfigService.updateAlertsForMaintenance(any())).thenReturn(null);
    assertNull(collectEmailSchedulerService.maintenanceAlerts(List.of(new AlertBaseModel())));
  }
  @Test
  public void filterAlert_success() {
    when(filterAlertConfigService.updateAlertsForFilter(any())).thenReturn(null);
    assertNull(collectEmailSchedulerService.filterAlerts(List.of(new AlertBaseModel())));
  }
  @Test
  public void collectFilters_success() {
    var alert = new AlertBaseModel();
    alert.setIsValid(true);
    var alert2= new AlertBaseModel();
    alert2.setIsValid(false);
    assertNotNull(collectEmailSchedulerService.collectFilters(List.of(alert,alert2)));
  }

  @Test
  public void modifyAlerts_success() {
    when(modifyAlertConfigService.updateAlertsForModify(any())).thenReturn(null);
    assertNull(collectEmailSchedulerService.modifyAlerts(List.of(new AlertBaseModel())));
  }

  @Test
  public void saveAlerts_success() {
    when(alertService.saveAll(any())).thenReturn(null);
    assertNull(collectEmailSchedulerService.saveAlerts(List.of(new AlertBaseModel())));
  }

  @Test
  void processFolders_processesAllFolders() throws MessagingException {
    when(store.getFolder(anyString())).thenReturn(folder);
    var subFolder = mock(Folder.class);
    when(subFolder.list()).thenReturn(new Folder[0]);
    when(subFolder.getType()).thenReturn(Folder.HOLDS_MESSAGES);
    var lstFolder = new Folder[1];
    lstFolder[0] = subFolder;
    when(folder.list()).thenReturn(lstFolder);
    when(folder.getType()).thenReturn(Folder.HOLDS_MESSAGES);
    collectEmailSchedulerService.processFolders(store, new EmailConfigModel(), new ArrayList<>());
    verify(folder).open(Folder.READ_WRITE);
  }

  @Test
  void processMessagesInFolder_noMessages() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(false);
    when(folder.search(any(FlagTerm.class))).thenReturn(new Message[0]);
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );
    }
    verify(folder).open(Folder.READ_WRITE);
  }

  @Test
  void processMessagesInFolder_notExecuted_flagsMessages() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(false);
    Message[] messages = new Message[1];
    Message message = Mockito.mock(MimeMessage.class);
    message.setContent(new Object(), "1232");
    messages[0] = message;
    when(folder.search(any(FlagTerm.class))).thenReturn(messages);

    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      // Giả lập parseEmail ném exception
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );

      verify(folder).open(Folder.READ_WRITE);
    }
  }

  @Test
  void processMessagesInFolder_exceptionHandling() throws Exception {
    Message[] messages = new Message[1];
    Message message = Mockito.mock(MimeMessage.class);
    messages[0] = message;
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(any(Message.class)))
          .thenThrow(new MessagingException("Test exception"));

      EmailConfigModel emailConfigModel = new EmailConfigModel();
      emailConfigModel.setExecuted(true);
      when(folder.search(any(FlagTerm.class))).thenReturn(messages);

      CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
      collectEmailConfig.setServiceId("testServiceId");
      collectEmailConfig.setApplicationId("testApplicationId");
      collectEmailConfig.setRecipient("testRecipient");

      ReflectionTestUtils.invokeMethod(
          collectEmailSchedulerService,
          "processMessagesInFolder",
          folder,
          emailConfigModel,
          List.of(collectEmailConfig)
      );
      verify(folder).open(Folder.READ_WRITE);
    }
  }

  @Test
  void fetchAndProcessEmails_emailConfigNotFound() throws BusinessException {
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(null);
    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);
    verifyNoInteractions(emailService, collectEmailConfigRepository);
  }

  @Test
  void processFolders_handlesMessagingException() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    List<CollectEmailConfigEntity> collectEmailConfigs = Collections.emptyList();

    Folder rootFolder = mock(Folder.class);
    when(rootFolder.getType()).thenReturn(Folder.HOLDS_MESSAGES);
    when(rootFolder.getFullName()).thenReturn("rootFolder");
    when(rootFolder.list()).thenThrow(new MessagingException("Simulated Exception"));

    when(store.getFolder(eq(EmailConfigConstants.DEFAULT_READ_FOLDER))).thenReturn(rootFolder);

    assertDoesNotThrow(() -> {
      collectEmailSchedulerService.processFolders(store, emailConfigModel, collectEmailConfigs);
    });
  }

  @Test
  void processMessagesInFolder_messageNotInstanceOfMimeMessage() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(true);

    // Sử dụng mock(Message.class) để đảm bảo không phải instance của MimeMessage
    Message plainMessage = mock(Message.class);
    plainMessage.setContent(new Object(), "123123");
//    when(plainMessage.getFrom()).thenReturn(new Address[0]);

    Message[] messages = new Message[] {plainMessage};

    Folder folder = mock(Folder.class);
    when(folder.search(any())).thenReturn(messages);
    doNothing().when(folder).open(Folder.READ_WRITE);
    doNothing().when(folder).close();

    collectEmailSchedulerService.processMessagesInFolder(folder, emailConfigModel,
        Collections.emptyList());

    verify(folder).open(Folder.READ_WRITE);
    verify(folder).close();
  }


  @Test
  void processMessagesInFolder_messagingExceptionOrIOException() throws Exception {
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(true);

    MimeMessage mimeMessage = mock(MimeMessage.class);
    when(mimeMessage.getFrom()).thenThrow(new MessagingException("Simulated Exception"));
    // Sử dụng MockedStatic để giả lập EmailUtils.parseEmail ném MessagingException
    try (MockedStatic<EmailUtils> mockedStatic = Mockito.mockStatic(EmailUtils.class)) {
      mockedStatic.when(() -> EmailUtils.parseEmail(mimeMessage))
          .thenThrow(new MessagingException("Simulated Exception"));

      Message[] messages = new Message[] {mimeMessage};

      Folder folder = mock(Folder.class);
      when(folder.search(any())).thenReturn(messages);
      doNothing().when(folder).open(Folder.READ_WRITE);
      doNothing().when(folder).close();

      assertDoesNotThrow(() ->
          collectEmailSchedulerService.processMessagesInFolder(folder, emailConfigModel,
              Collections.emptyList())
      );

      verify(folder).open(Folder.READ_WRITE);
      verify(folder).close();
    }
  }


  @Test
  void fetchAndProcessEmails_noActiveCollectEmailConfigs() throws BusinessException {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setActive(true);
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(TEST_EMAIL_CONFIG_ID))
        .thenReturn(Collections.emptyList());
    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);
    verifyNoInteractions(emailService);
  }

  @Test
  void fetchAndProcessEmails_validProcess() throws Exception {
    EmailConfigEntity emailConfigEntity = new EmailConfigEntity();
    emailConfigEntity.setExecuted(false);
    emailConfigEntity.setActive(true);
    EmailConfigModel emailConfigModel = new EmailConfigModel();
    emailConfigModel.setExecuted(false);

    CollectEmailConfigEntity collectEmailConfig = new CollectEmailConfigEntity();
    collectEmailConfig.setServiceId("testServiceId");
    collectEmailConfig.setApplicationId("testApplicationId");
    collectEmailConfig.setRecipient("testRecipient");

    when(emailService.connectImapServer(any())).thenReturn(store);
    when(store.getFolder(anyString())).thenReturn(folder);
    when(folder.list()).thenReturn(new Folder[0]);
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenReturn(emailConfigEntity);
    when(collectEmailConfigRepository.findByEmailConfigIdAndActiveIsTrue(TEST_EMAIL_CONFIG_ID))
        .thenReturn(Collections.singletonList(collectEmailConfig));

    collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID);
  }

  @Test
  void fetchAndProcessEmails_throwsExceptionOnError() {
    when(emailConfigService.findById(TEST_EMAIL_CONFIG_ID)).thenThrow(
        new RuntimeException("Test exception"));
    assertThrows(RuntimeException.class, () ->
        collectEmailSchedulerService.fetchAndProcessEmails(TEST_EMAIL_CONFIG_ID)
    );
  }
  @TestForDev
  void createAlerts_ShouldReturnCreatedAlert() {
    // Arrange
    CollectEmailConfigEntity mockEntity = mock(CollectEmailConfigEntity.class);
    when(mockEntity.getContentValue()).thenReturn("Test Content");

    AlertBaseModel mockAlert = new AlertBaseModel();
    List<AlertBaseModel> createdAlerts = List.of(mockAlert);
    when(commonRawAlertService.createRawData(anyList(), eq(AlertSourceTypeEnum.EMAIL)))
        .thenReturn(createdAlerts);

    // Act
    AlertBaseModel result = collectEmailSchedulerService.createAlerts(mockEntity);

    // Assert
    assertNotNull(result);
    assertEquals(mockAlert, result);

    verify(mockEntity).getContentValue();
    verify(commonRawAlertService).createRawData(anyList(), eq(AlertSourceTypeEnum.EMAIL));
  }
}
