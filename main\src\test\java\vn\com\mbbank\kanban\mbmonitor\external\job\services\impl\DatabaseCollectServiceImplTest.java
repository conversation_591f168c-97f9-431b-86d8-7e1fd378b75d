package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockConstruction;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManager;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseCollectRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectTempService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryDetailService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/25/2024
 */
class DatabaseCollectServiceImplTest {
  @Mock
  DatabaseCollectRepository databaseCollectRepository;
  @Mock
  DatabaseConnectionService databaseConnectionService;
  @Mock
  CommonRawAlertService commonRawAlertService;
  @Mock
  CommonDatabaseConnectionService commonDatabaseConnectionService;
  @Mock
  DatabaseQueryService superiorsService;

  @Mock
  MaintenanceTimeConfigService maintenanceTimeConfigService;
  @Mock
  ModifyAlertConfigService modifyAlertConfigService;
  @Mock
  FilterAlertConfigService filterAlertConfigService;
  @Mock
  DatabaseCollectTempService databaseCollectTempService;
  @Mock
  ServiceService serviceService;
  @Mock
  ApplicationService applicationService;
  @Mock
  AlertPriorityConfigService alertPriorityConfigService;
  @Mock
  AlertService alertService;
  @Mock
  EntityManager entityManager;
  @InjectMocks
  @Spy
  DatabaseCollectServiceImpl databaseCollectServiceImpl;
  @Mock
  JobHistoryService jobHistoryService;
  @Mock
  JobHistoryDetailService jobHistoryDetailService;

  @Mock
  QueryHikariDataSourceConfig queryHikariDataSourceConfig;


  @Mock
  private HikariDataSource hikariDataSource;

  @Mock
  private Connection connection;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    ReflectionTestUtils.setField(databaseCollectServiceImpl, "rowLimit", 500);
  }

  @Test
  void getRepository() {
    JpaCommonRepository<DatabaseCollectEntity, Long> result =
        databaseCollectServiceImpl.getRepository();
    Assertions.assertEquals(databaseCollectRepository, result);
  }

  @Test
  void collect_collect_not_found() throws BusinessException {
    when(databaseCollectRepository.findById(any())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> {
      databaseCollectServiceImpl.collect(1L);
    });
  }

  @Test
  void collect_collect_not_active() throws BusinessException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setIsActive(false);
    when(databaseCollectRepository.findById(any())).thenReturn(Optional.of(databaseCollectEntity));
    Assertions.assertThrows(BusinessException.class, () -> {
      databaseCollectServiceImpl.collect(1L);
    });
  }

  @Test
  void collect_connection_not_found() throws BusinessException {
    var entity = new DatabaseCollectEntity();
    entity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(entity));

    when(databaseConnectionService.findById(any())).thenReturn(null);
    Assertions.assertThrows(BusinessException.class, () -> {
      databaseCollectServiceImpl.collect(1L);
    });
  }

  @Test
  void collect_connection_active_false() throws BusinessException {
    var entity = new DatabaseCollectEntity();
    entity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(entity));
    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(false);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    Assertions.assertThrows(BusinessException.class, () -> {
      databaseCollectServiceImpl.collect(1L);
    });
  }

  @Test
  void collect_success() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setModifiedDate(new Date());
    databaseCollectEntity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));

    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder().build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);
    when(databaseCollectTempService.findAllByDatabaseCollectId(anyLong())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(null);
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(null);
    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());

    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void collect_case_table_temp_not_null() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    calendar.add(Calendar.DAY_OF_MONTH, 1);
    Date tomorrow = calendar.getTime();
    databaseCollectEntity.setModifiedDate(tomorrow);
    databaseCollectEntity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder().build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(null);
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(null);
    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());

    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_query_with_data() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    when(modifyAlertConfigService.updateAlertsForModify(any())).thenReturn(
        List.of(new AlertBaseModel()));
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_query_with_data_map_custom() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);
    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(any(),
            any())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));

    AlertPriorityConfigEntity alertPriorityConfig = new AlertPriorityConfigEntity();
    alertPriorityConfig.setId(1L);
    alertPriorityConfig.setName("thangnv");
    alertPriorityConfig.setPosition(1);
    alertPriorityConfig.setColor("black");
    when(alertPriorityConfigService.findById(any())).thenReturn(alertPriorityConfig);
    when(modifyAlertConfigService.updateAlertsForModify(any())).thenReturn(
        List.of(new AlertBaseModel()));
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_query_get_success_alert() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setModifiedDate(new Date());
    databaseCollectEntity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(List.of("id", "alert", "created_date", "service", "application"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(null);
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(null);
    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));

    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void collect_case_getAlertEntity_exception() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setModifiedDate(new Date());
    databaseCollectEntity.setIsActive(true);
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));

    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(null);
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(null);
    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));

    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_get_service_exception() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID1");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void collect_case_get_service_not_exists_database_exception()
      throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id1", "alert", "createDate", "service", "application", "priority",
                "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.empty());
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_get_appliation_exception() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void collect_case_get_application_not_exists_database_exception()
      throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(new ServiceEntity()));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.empty());

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setIsValid(true);

    AlertBaseModel alertBaseModel1 = new AlertBaseModel();
    alertBaseModel1.setIsValid(false);
    when(commonRawAlertService.createRawData(any(), any())).thenReturn(
        List.of(alertBaseModel, alertBaseModel1));
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_getPriorityConfig_column_data_not_found() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_getAlertCollectId_column_data_not_found() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    when(commonRawAlertService.createRawData(any(), any())).thenReturn(
        List.of(new AlertBaseModel()));
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void collect_getAlertCollectDate_column_data_not_found() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("2");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
            anyString())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));
    when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_executeSql_runtime() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    try (MockedConstruction<HikariDataSource> mocked = mockConstruction(HikariDataSource.class)) {

      when(superiorsService.executeQuery(any(), any(), any(), any(),
          any())).thenThrow(new RuntimeException());

      DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
      collectTemp.setAlertCollectId("2");
      collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
      when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
          List.of(collectTemp));
      when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
          Integer.valueOf(0));
      when(databaseCollectTempService.saveAll(any())).thenReturn(
          List.of(new DatabaseCollectTempEntity()));
      when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

      ServiceEntity serviceEntity = new ServiceEntity();
      serviceEntity.setId("1");
      when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
          Optional.of(serviceEntity));
      when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());

      ApplicationEntity applicationEntity = new ApplicationEntity();
      applicationEntity.setServiceId("1");
      when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
          anyString())).thenReturn(Optional.of(applicationEntity));

      when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(
          alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
              anyString())).thenReturn(
          List.of(
              new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
      when(alertPriorityConfigService.saveAll(any())).thenReturn(
          List.of(new AlertPriorityConfigEntity()));
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
      when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
      when(alertService.findById(any())).thenReturn(new AlertEntity());
      Assertions.assertThrows(BusinessRuntimeException.class, () -> {
        databaseCollectServiceImpl.collect(1L);
      });
    }
  }


  @Test
  void collect_executeSql_sql_exception() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    try (MockedConstruction<HikariDataSource> mocked = mockConstruction(HikariDataSource.class)) {

      when(superiorsService.executeQuery(any(), any(), any(), any(),
          any())).thenThrow(new SQLException());

      DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
      collectTemp.setAlertCollectId("2");
      collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
      when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
          List.of(collectTemp));
      when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
          Integer.valueOf(0));
      when(databaseCollectTempService.saveAll(any())).thenReturn(
          List.of(new DatabaseCollectTempEntity()));
      when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

      ServiceEntity serviceEntity = new ServiceEntity();
      serviceEntity.setId("1");
      when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
          Optional.of(serviceEntity));
      when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());

      ApplicationEntity applicationEntity = new ApplicationEntity();
      applicationEntity.setServiceId("1");
      when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
          anyString())).thenReturn(Optional.of(applicationEntity));

      when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(
          alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
              anyString())).thenReturn(
          List.of(
              new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
      when(alertPriorityConfigService.saveAll(any())).thenReturn(
          List.of(new AlertPriorityConfigEntity()));
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
      when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
      when(alertService.findById(any())).thenReturn(new AlertEntity());
      Assertions.assertThrows(BusinessRuntimeException.class, () -> {
        databaseCollectServiceImpl.collect(1L);
      });
    }
  }


  @Test
  void collect_executeSql_sql_connection_null_exception() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.FROM_SOURCE);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(null);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    try (MockedConstruction<HikariDataSource> mocked = mockConstruction(HikariDataSource.class)) {

      when(superiorsService.executeQuery(any(), any(), any(), any(),
          any())).thenThrow(new SQLException());

      DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
      collectTemp.setAlertCollectId("2");
      collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
      when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
          List.of(collectTemp));
      when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
          Integer.valueOf(0));
      when(databaseCollectTempService.saveAll(any())).thenReturn(
          List.of(new DatabaseCollectTempEntity()));
      when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

      ServiceEntity serviceEntity = new ServiceEntity();
      serviceEntity.setId("1");
      when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
          Optional.of(serviceEntity));
      when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
      when(serviceService.findById(any())).thenReturn(new ServiceEntity());

      ApplicationEntity applicationEntity = new ApplicationEntity();
      applicationEntity.setServiceId("1");
      when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
          anyString())).thenReturn(Optional.of(applicationEntity));

      when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
      when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
      when(
          alertPriorityConfigService.findAllMatchPriorityConfig(anyString(),
              anyString())).thenReturn(
          List.of(
              new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
      when(alertPriorityConfigService.saveAll(any())).thenReturn(
          List.of(new AlertPriorityConfigEntity()));
      when(alertPriorityConfigService.findById(any())).thenReturn(new AlertPriorityConfigEntity());
      when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
      when(alertService.findById(any())).thenReturn(new AlertEntity());
      Assertions.assertThrows(BusinessException.class, () -> {
        databaseCollectServiceImpl.collect(1L);
      });
    }
  }


  @Test
  void collect_case_query_with_alert_not_data() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    databaseConnectionEntity.setIsActive(true);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
    collectTemp.setAlertCollectId("1");
    collectTemp.setAlertCollectDate(new Timestamp(new Date().getTime()));
    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        List.of(collectTemp));
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(any(),
            any())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));

    AlertPriorityConfigEntity alertPriorityConfig = new AlertPriorityConfigEntity();
    alertPriorityConfig.setId(1L);
    alertPriorityConfig.setName("thangnv");
    alertPriorityConfig.setPosition(1);
    alertPriorityConfig.setColor("black");
    when(alertPriorityConfigService.findById(any())).thenReturn(alertPriorityConfig);
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }


  @Test
  void collect_case_data_temp_empty() throws BusinessException, SQLException {
    DatabaseCollectEntity databaseCollectEntity = new DatabaseCollectEntity();
    databaseCollectEntity.setConnectionId(1L);
    databaseCollectEntity.setSqlCommand("select * from dual");
    databaseCollectEntity.setCreatedDateField("createDate");
    databaseCollectEntity.setAlertIdField("ID");
    databaseCollectEntity.setInterval(10L);
    databaseCollectEntity.setServiceNameType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setServiceId("11");
    databaseCollectEntity.setServiceMapValue("service");
    databaseCollectEntity.setApplicationType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setApplicationId("app");
    databaseCollectEntity.setApplicationMapValue("application");
    databaseCollectEntity.setAlertMapValue("alert");
    databaseCollectEntity.setPriorityType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setPriorityId(1L);
    databaseCollectEntity.setPriorityMapValue("priority");
    databaseCollectEntity.setContactType(ConfigCollectMapTypeEnum.CUSTOM);
    databaseCollectEntity.setContactMapValue("contact");
    databaseCollectEntity.setContactCustomValue("contact");
    databaseCollectEntity.setIsActive(true);

    databaseCollectEntity.setModifiedDate(new Date());
    when(databaseCollectRepository.findById(any())).thenReturn(
        Optional.of(databaseCollectEntity));
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(new HikariConfig());
    when(databaseConnectionService.saveAll(any())).thenReturn(
        List.of(new DatabaseConnectionEntity()));


    DatabaseConnectionEntity databaseConnectionEntity = new DatabaseConnectionEntity();
    databaseConnectionEntity.setIsActive(true);
    databaseConnectionEntity.setType(DatabaseConnectionTypeEnum.ORACLE);
    when(databaseConnectionService.findById(any())).thenReturn(databaseConnectionEntity);
    doNothing().when(databaseConnectionService)
        .checkConnection(Mockito.any(DatabaseConnectionRequest.class));
    when(queryHikariDataSourceConfig.getDataSource(any())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(
            List.of("id", "alert", "createDate", "service", "application", "priority", "contact"))
        .listDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(List.of(
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("ID").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("alert").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("service").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("application")
                        .value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("priority").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("contact").value("1")
                        .build(),
                    SqlExecutionResponse.SqlMappingColumnData.builder().column("createDate")
                        .value("2024-11-21 16:56:35")
                        .build()))
                .build())).build();
    when(superiorsService.executeQuery(any(), any(), any(), any(),
        any())).thenReturn(sqlExecutionResponse);

    when(databaseCollectTempService.findAllByDatabaseCollectId(any())).thenReturn(
        new ArrayList<>());
    when(databaseCollectTempService.deleteAllByDatabaseCollectId(anyLong())).thenReturn(
        Integer.valueOf(0));
    when(databaseCollectTempService.saveAll(any())).thenReturn(
        List.of(new DatabaseCollectTempEntity()));
    when(databaseCollectTempService.findById(any())).thenReturn(new DatabaseCollectTempEntity());

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    when(serviceService.findFirstByNameIgnoreCase(anyString())).thenReturn(
        Optional.of(serviceEntity));
    when(serviceService.saveAll(any())).thenReturn(List.of(new ServiceEntity()));
    when(serviceService.findById(any())).thenReturn(new ServiceEntity());

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("1");
    when(applicationService.findFirstByNameIgnoreCaseAndServiceId(anyString(),
        anyString())).thenReturn(Optional.of(applicationEntity));

    when(applicationService.saveAll(any())).thenReturn(List.of(new ApplicationEntity()));
    when(applicationService.findById(any())).thenReturn(new ApplicationEntity());
    when(
        alertPriorityConfigService.findAllMatchPriorityConfig(any(),
            any())).thenReturn(
        List.of(
            new AlertPriorityConfigEntity(Long.valueOf(1), "name", "color", Integer.valueOf(0))));
    when(alertPriorityConfigService.saveAll(any())).thenReturn(
        List.of(new AlertPriorityConfigEntity()));

    AlertPriorityConfigEntity alertPriorityConfig = new AlertPriorityConfigEntity();
    alertPriorityConfig.setId(1L);
    alertPriorityConfig.setName("thangnv");
    alertPriorityConfig.setPosition(1);
    alertPriorityConfig.setColor("black");
    when(alertPriorityConfigService.findById(any())).thenReturn(alertPriorityConfig);
    when(alertService.saveAll(any())).thenReturn(List.of(new AlertEntity()));
    when(alertService.findById(any())).thenReturn(new AlertEntity());
    when(jobHistoryService.save(any())).thenReturn(new JobHistoryEntity());
    when(commonRawAlertService.createRawData(any(), any())).thenReturn(
        List.of(new AlertBaseModel()));
    databaseCollectServiceImpl.collect(Long.valueOf(1));
  }

  @Test
  void findAllByIsActiveTrue() {
    when(databaseCollectRepository.findAllByIsActiveTrue()).thenReturn(new ArrayList<>());
    databaseCollectServiceImpl.findAllByIsActiveTrue();
    verify(databaseCollectRepository).findAllByIsActiveTrue();
  }


}
