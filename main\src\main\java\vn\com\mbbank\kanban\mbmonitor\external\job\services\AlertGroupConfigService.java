package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;

/**
 * interface logic AlertGroupConfigService.
 */
public interface AlertGroupConfigService extends BaseSoftService<AlertGroupConfigEntity, Long> {

  /**
   * find all alert group config.
   *
   * @param withDeleted get with deleted item or not.
   * @param search      search
   * @return a list of config updated.
   */
  List<AlertGroupConfigEntity> findAllWithDeletedAndSearch(Boolean withDeleted, String search);

  /**
   * find all alert group config by conditions.
   *
   * @param serviceId     serviceId.
   * @param applicationId applicationId
   * @param deleted       deleted status
   * @param active        active status
   * @return a list of config updated.
   */
  List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(String serviceId,
                                                                                     String applicationId,
                                                                                     Boolean deleted, Boolean active);

  /**
   * find alla alert group config.
   *
   * @param deleted deleted status.
   * @param active  active status
   * @return a list of config updated.
   */
  List<AlertGroupConfigEntity> findAllByDeletedAndActive(Boolean deleted, Boolean active);
}
