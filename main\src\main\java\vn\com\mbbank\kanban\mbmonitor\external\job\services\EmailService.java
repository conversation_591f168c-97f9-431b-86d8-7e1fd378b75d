package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import jakarta.mail.Store;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.EmailConfigModel;

/**
 * Interface for handling email operations across different protocols such as SMTP, IMAP, and Exchange.
 * This interface provides methods for sending and receiving emails based on the user-provided configuration.
 */
public interface EmailService {

  /**
   * Configures the properties for the IMAP connection based on the given email configuration.
   *
   * @param emailConfig the configuration containing details for connecting to the email server.
   * @return the configured properties.
   */
  Store connectImapServer(EmailConfigModel emailConfig) throws BusinessException;

}
