package vn.com.mbbank.kanban.mbmonitor.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;

public class StringUtilsTest {
  @Test
  void constructor() {
    new StringUtils();
  }

  @Test
  void capitalizeFirstLetter_NullInput() {
    assertNotNull(StringUtils.capitalizeFirstLetter("  "));
  }

  @Test
  void capitalizeFirstLetter_EmptyInput() {
    assertEquals("", StringUtils.capitalizeFirstLetter(""));
  }

  @Test
  void capitalizeFirstLetter_SingleLowercaseCharacter() {
    assertEquals("A", StringUtils.capitalizeFirstLetter("a"));
  }

  @Test
  void capitalizeFirstLetter_SingleUppercaseCharacter() {
    assertEquals("A", StringUtils.capitalizeFirstLetter("A"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithLowercase() {
    assertEquals("Hello", StringUtils.capitalizeFirstLetter("hello"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithUppercase() {
    assertEquals("Hello", StringUtils.capitalizeFirstLetter("Hello"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithNonAlphabetic() {
    assertEquals("1hello", StringUtils.capitalizeFirstLetter("1hello"));
  }
  @Test
  void sanitizeFileName_success_validFileName() throws BusinessException {
    String originalFilename = "valid_file-name123.txt";
    String sanitized = StringUtils.sanitizeFileName(originalFilename);
    assertEquals("valid_file-name123.txt", sanitized);
  }

  @Test
  void sanitizeFileName_error_nullFileName() {
    BusinessException exception = assertThrows(BusinessException.class,
        () -> StringUtils.sanitizeFileName(null));
    assertEquals(ErrorCode.INVALID_FILE_NAME.getCode(), exception.getCode());
  }

  @Test
  void sanitizeFileName_error_emptyFileName() {
    BusinessException exception = assertThrows(BusinessException.class,
        () -> StringUtils.sanitizeFileName(""));
    assertEquals(ErrorCode.INVALID_FILE_NAME.getCode(), exception.getCode());
  }

  @Test
  void sanitizeFileName_error_fileNameWithTraversal() {
    BusinessException exception = assertThrows(BusinessException.class,
        () -> StringUtils.sanitizeFileName("../file.txt"));
    assertEquals(ErrorCode.INVALID_FILE_NAME.getCode(), exception.getCode());
  }

  @Test
  void sanitizeFileName_error_invalidPattern() {
    String originalFilename = "file:name?.txt";
    BusinessException exception = assertThrows(BusinessException.class,
        () -> StringUtils.sanitizeFileName(originalFilename));
    assertEquals(ErrorCode.INVALID_FILE_NAME.getCode(), exception.getCode());
  }

  @Test
  void sanitizeFileName_success_filenameWithSpaces() throws BusinessException {
    String originalFilename = "file name with spaces.txt";
    String sanitized = StringUtils.sanitizeFileName(originalFilename);
    assertEquals("file_name_with_spaces.txt", sanitized);
  }
}
