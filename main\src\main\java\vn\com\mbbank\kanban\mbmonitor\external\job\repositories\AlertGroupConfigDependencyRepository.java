package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;

/**
 * Repository table AlertGroupConfigDependencyRepository.
 */
@Repository
public interface AlertGroupConfigDependencyRepository
    extends JpaCommonRepository<AlertGroupConfigDependencyEntity, Long> {

  /**
   * find all AlertGroupConfigDependencyEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return list of AlertGroupConfigDependencyEntity
   */
  List<AlertGroupConfigDependencyEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

}
