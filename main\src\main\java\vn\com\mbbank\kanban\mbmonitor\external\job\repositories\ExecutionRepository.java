package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;

/**
 * Repository table Execution.
 */
@Repository
public interface ExecutionRepository
    extends JpaCommonRepository<ExecutionEntity, String> {

  /**
   * Finds by executionGroupId.
   *
   * @param executionGroupId the ID of group
   * @return list of Execution
   */
  List<ExecutionEntity> findAllByExecutionGroupId(String executionGroupId);

  /**
   * find all by ids.
   *
   * @param ids for find
   * @return a list of execution
   */
  List<ExecutionEntity> findAllByIdIn(List<String> ids);
}
