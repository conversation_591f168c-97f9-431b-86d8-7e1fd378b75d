package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.AlertGroupRepositoryCustomImpl;

@ExtendWith(MockitoExtension.class)
class AlertGroupRepositoryCustomImplTest {
  @Mock
  SqlQueryUtil sqlQueryUtil;

  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;
  @InjectMocks
  AlertGroupRepositoryCustomImpl alertGroupRepositoryCustomImpl;

  @Test
  void findAlertRecipientByAlertStatus_success() {
    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), eq(String.class))).thenReturn(Collections.emptyList());
    var res = alertGroupRepositoryCustomImpl.findAlertRecipientByAlertGroupStatus(AlertGroupStatusEnum.CLOSE);
    Assertions.assertEquals(res.size(), 0);
  }
}
