package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * table AUTO_TRIGGER_ACTION_CONFIG.
 */
@Data
@Entity
@Table(name = TableName.AUTO_TRIGGER_ACTION_CONFIG)
@EqualsAndHashCode(callSuper = true)
public class AutoTriggerActionConfigEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Column(name = "RULE_GROUP")
  @Convert(converter = RuleGroupConverter.class)
  private RuleGroupType ruleGroup;

  @Column(name = "TIME_SINCE_LAST_TRIGGER")
  private Long timeSinceLastTrigger;

  @Column(name = "LAST_RUN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date lastRun;

  @Column(name = "TRIGGER_TYPE")
  @Enumerated(EnumType.STRING)
  private AutoTriggerTypeEnum triggerType;

  @Column(name = "CRON_EXPRESSION")
  private String cronExpression;

  @Override
  public String getId() {
    return id;
  }
}