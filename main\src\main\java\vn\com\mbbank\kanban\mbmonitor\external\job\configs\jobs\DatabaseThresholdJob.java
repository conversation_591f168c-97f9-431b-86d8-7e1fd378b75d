package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import java.util.stream.Collectors;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseThresholdService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 05/05/2025
 */
@Component(JobNameConstants.DATABASE_THRESHOLD)
public class DatabaseThresholdJob extends JobConfig implements CommonBaseConsumerService {
  @Autowired
  private DatabaseThresholdService databaseThresholdService;
  @Value("${monitor.redis.lock.database.timeout:10000}")
  private Long lockTimeoutMs;

  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException {
    String key = context.getJobDetail().getKey().getName();
    databaseThresholdService.collect(key);
  }

  @Override
  public Map<String, String> getMappingJobNameAndCronTime() {
    var lstConfig = databaseThresholdService.findAllByActiveTrue();
    return lstConfig.stream().collect(Collectors.toMap(DatabaseThresholdConfigEntity::getId,
        e -> CronUtils.convertUnixToQuartz(e.getCronTime())));
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.DATABASE_THRESHOLD;
  }

  @Override
  public Long getLockTimeoutMs() {
    return this.lockTimeoutMs;
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    return Map.of();
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    var kafkaJobModel = KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var configId = String.valueOf(kafkaJobModel.configId());
    var cronTime = CronUtils.convertUnixToQuartz(kafkaJobModel.cronTime());
    changeConfigJob(kafkaJobModel.type(), configId, cronTime);
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.DATABASE_THRESHOLD;
  }
}