package vn.com.mbbank.kanban.mbmonitor.external.job.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import jakarta.mail.Address;
import jakarta.mail.BodyPart;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMultipart;
import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models.EmailModel;

class EmailUtilsTest {

  @Test
  void parseEmail_success() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("Test Subject");
    when(message.getFrom()).thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.TO))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.CC))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getContent()).thenReturn("This is a plain text email.");
    when(message.isMimeType("text/plain")).thenReturn(true);

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Test Subject", emailModel.getSubject());
    assertEquals("<EMAIL>", emailModel.getSender());
    assertEquals(List.of("<EMAIL>"), emailModel.getTo());
    assertEquals(List.of("<EMAIL>"), emailModel.getCc());
    assertEquals("This is a plain text email.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_error() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenThrow(new MessagingException("Error parsing subject"));

    MessagingException exception = assertThrows(MessagingException.class, () -> EmailUtils.parseEmail(message));
    assertEquals("Error parsing subject", exception.getMessage());
  }

  @Test
  void getTextFromMessage_success() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenReturn("Plain text content");

    String result = EmailUtils.getTextFromMessage(message);
    assertEquals("Plain text content", result);
  }

  @Test
  void getTextFromMessage_htmlSuccess() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.isMimeType("text/html")).thenReturn(true);
    when(message.getContent()).thenReturn("<html><body><p>HTML content</p></body></html>");

    String result = EmailUtils.getTextFromMessage(message);
    assertEquals("HTML content", result);
  }

  @Test
  void getTextFromMessage_multipartSuccess() throws MessagingException, IOException {
    Message message = mock(Message.class);
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart bodyPart = mock(BodyPart.class);

    when(message.isMimeType("multipart/*")).thenReturn(true);
    when(message.getContent()).thenReturn(mimeMultipart);
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(bodyPart);
    when(bodyPart.isMimeType("text/plain")).thenReturn(true);
    when(bodyPart.getContent()).thenReturn("Multipart plain text");
    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");

    String result = EmailUtils.getTextFromMessage(message);
    assertEquals("Multipart plain text", result);
  }

  @Test
  void getTextFromMessage_unknownMimeType() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.isMimeType("application/octet-stream")).thenReturn(true);
    when(message.getContent()).thenReturn("Binary content");

    String result = EmailUtils.getTextFromMessage(message);
    assertEquals("", result);
  }

  @Test
  void getTextFromMessage_error() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenThrow(new IOException("Error fetching content"));

    IOException exception = assertThrows(IOException.class, () -> EmailUtils.getTextFromMessage(message));
    assertEquals("Error fetching content", exception.getMessage());
  }

  @Test
  void getTextFromMimeMultipart_success() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart bodyPart = mock(BodyPart.class);

    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(bodyPart);
    when(bodyPart.isMimeType("text/plain")).thenReturn(true);
    when(bodyPart.getContent()).thenReturn("Plain text part");
    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");
    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("Plain text part", result);
  }

  @Test
  void getTextFromMimeMultipart_alternativeHtmlPriority() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart plainTextPart = mock(BodyPart.class);
    BodyPart htmlPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/alternative");
    when(mimeMultipart.getCount()).thenReturn(2);
    when(mimeMultipart.getBodyPart(0)).thenReturn(plainTextPart);
    when(mimeMultipart.getBodyPart(1)).thenReturn(htmlPart);

    when(plainTextPart.isMimeType("text/plain")).thenReturn(true);
    when(plainTextPart.getContent()).thenReturn("Plain text content");

    when(htmlPart.isMimeType("text/html")).thenReturn(true);
    when(htmlPart.getContent()).thenReturn("<html><body><p>HTML content</p></body></html>");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("HTML content", result);
  }

  @Test
  void getTextFromMimeMultipart_alternativePlainTextOnly() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart plainTextPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/alternative");
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(plainTextPart);

    when(plainTextPart.isMimeType("text/plain")).thenReturn(true);
    when(plainTextPart.getContent()).thenReturn("Plain text content");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("Plain text content", result);
  }

  @Test
  void getTextFromMimeMultipart_nestedMultipart() throws MessagingException, IOException {
    MimeMultipart parentMultipart = mock(MimeMultipart.class);
    MimeMultipart nestedMultipart = mock(MimeMultipart.class);
    BodyPart nestedBodyPart = mock(BodyPart.class);
    BodyPart parentBodyPart = mock(BodyPart.class);

    when(parentMultipart.getContentType()).thenReturn("multipart/mixed");
    when(parentMultipart.getCount()).thenReturn(1);
    when(parentMultipart.getBodyPart(0)).thenReturn(parentBodyPart);

    when(parentBodyPart.getContent()).thenReturn(nestedMultipart);
    when(nestedMultipart.getContentType()).thenReturn("multipart/alternative");
    when(nestedMultipart.getCount()).thenReturn(1);
    when(nestedMultipart.getBodyPart(0)).thenReturn(nestedBodyPart);

    when(nestedBodyPart.isMimeType("text/plain")).thenReturn(true);
    when(nestedBodyPart.getContent()).thenReturn("Nested plain text");

    String result = EmailUtils.getTextFromMimeMultipart(parentMultipart);
    assertEquals("Nested plain text", result);
  }

  @Test
  void getTextFromMimeMultipart_nonAlternativeHtmlContent2() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart htmlPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(htmlPart);

    when(htmlPart.isMimeType("text/html")).thenReturn(true);
    when(htmlPart.getContent()).thenReturn("<html><body><p>Non-alternative HTML content</p></body></html>");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("Non-alternative HTML content", result);
  }



  @Test
  void getTextFromMimeMultipart_alternativeNoTextParts() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart attachmentPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/alternative");
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(attachmentPart);

    when(attachmentPart.isMimeType("application/octet-stream")).thenReturn(true);
    when(attachmentPart.getContent()).thenReturn("binary data");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("", result);
  }

  @Test
  void getTextFromMimeMultipart_nonAlternativeNoTextParts() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart attachmentPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(attachmentPart);

    when(attachmentPart.isMimeType("application/octet-stream")).thenReturn(true);
    when(attachmentPart.getContent()).thenReturn("binary data");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("", result);
  }

  @Test
  void getTextFromMimeMultipart_nestedMultipartReturnsEmpty() throws MessagingException, IOException {
    MimeMultipart parentMultipart = mock(MimeMultipart.class);
    MimeMultipart nestedMultipart = mock(MimeMultipart.class);
    BodyPart parentBodyPart = mock(BodyPart.class);

    when(parentMultipart.getContentType()).thenReturn("multipart/mixed");
    when(parentMultipart.getCount()).thenReturn(1);
    when(parentMultipart.getBodyPart(0)).thenReturn(parentBodyPart);

    when(parentBodyPart.getContent()).thenReturn(nestedMultipart);
    when(nestedMultipart.getContentType()).thenReturn("multipart/alternative");
    when(nestedMultipart.getCount()).thenReturn(0); // Nested multipart is empty

    String result = EmailUtils.getTextFromMimeMultipart(parentMultipart);
    assertEquals("", result);
  }


  @Test
  void getTextFromMimeMultipart_nonAlternativeHtmlContent() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart htmlPart = mock(BodyPart.class);

    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed"); // Not multipart/alternative
    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(htmlPart);

    when(htmlPart.isMimeType("text/html")).thenReturn(true);
    when(htmlPart.getContent()).thenReturn("<html><body><p>Non-alternative HTML content</p></body></html>");

    String result = EmailUtils.getTextFromMimeMultipart(mimeMultipart);
    assertEquals("Non-alternative HTML content", result);
  }


  @Test
  void getTextFromMimeMultipart_error() throws MessagingException, IOException {
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);

    when(mimeMultipart.getCount()).thenThrow(new MessagingException("Error fetching parts"));
    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");
    MessagingException exception =
        assertThrows(MessagingException.class, () -> EmailUtils.getTextFromMimeMultipart(mimeMultipart));
    assertEquals("Error fetching parts", exception.getMessage());
  }

  @Test
  void parseEmail_htmlContent() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("HTML Subject");
    when(message.getFrom()).thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.TO))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.CC))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.isMimeType("text/html")).thenReturn(true);
    when(message.getContent()).thenReturn("<html><body><p>This is HTML content.</p></body></html>");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("HTML Subject", emailModel.getSubject());
    assertEquals("<EMAIL>", emailModel.getSender());
    assertEquals(List.of("<EMAIL>"), emailModel.getTo());
    assertEquals(List.of("<EMAIL>"), emailModel.getCc());
    assertEquals("This is HTML content.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_multipartContent() throws MessagingException, IOException {
    Message message = mock(Message.class);
    MimeMultipart mimeMultipart = mock(MimeMultipart.class);
    BodyPart plainTextPart = mock(BodyPart.class);

    when(message.getSubject()).thenReturn("Multipart Subject");
    when(message.getFrom()).thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.TO))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.CC))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.isMimeType("multipart/*")).thenReturn(true);
    when(message.getContent()).thenReturn(mimeMultipart);

    when(mimeMultipart.getCount()).thenReturn(1);
    when(mimeMultipart.getBodyPart(0)).thenReturn(plainTextPart);
    when(plainTextPart.isMimeType("text/plain")).thenReturn(true);
    when(plainTextPart.getContent()).thenReturn("Multipart plain text content.");
    when(mimeMultipart.getContentType()).thenReturn("multipart/mixed");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Multipart Subject", emailModel.getSubject());
    assertEquals("<EMAIL>", emailModel.getSender());
    assertEquals(List.of("<EMAIL>"), emailModel.getTo());
    assertEquals(List.of("<EMAIL>"), emailModel.getCc());
    assertEquals("Multipart plain text content.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_nullRecipients() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("Null Recipients Subject");
    when(message.getFrom()).thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.TO)).thenReturn(null);
    when(message.getRecipients(Message.RecipientType.CC)).thenReturn(null);
    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenReturn("Plain text content.");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Null Recipients Subject", emailModel.getSubject());
    assertEquals("<EMAIL>", emailModel.getSender());
    assertEquals(List.of(), emailModel.getTo());
    assertEquals(List.of(), emailModel.getCc());
    assertEquals("Plain text content.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_emptyRecipients() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("Empty Recipients Subject");
    when(message.getFrom()).thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.TO)).thenReturn(new Address[]{});
    when(message.getRecipients(Message.RecipientType.CC)).thenReturn(new Address[]{});
    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenReturn("Plain text content.");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Empty Recipients Subject", emailModel.getSubject());
    assertEquals("<EMAIL>", emailModel.getSender());
    assertEquals(List.of(), emailModel.getTo());
    assertEquals(List.of(), emailModel.getCc());
    assertEquals("Plain text content.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_nullFromAddress() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("Null From Subject");
    when(message.getFrom()).thenReturn(new Address[0]);
    when(message.getRecipients(Message.RecipientType.TO))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.CC))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenReturn("Plain text content.");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Null From Subject", emailModel.getSubject());
    assertEquals("", emailModel.getSender());
    assertEquals(List.of("<EMAIL>"), emailModel.getTo());
    assertEquals(List.of("<EMAIL>"), emailModel.getCc());
    assertEquals("Plain text content.", emailModel.getTextBody());
  }

  @Test
  void parseEmail_emptyFromAddress() throws MessagingException, IOException {
    Message message = mock(Message.class);

    when(message.getSubject()).thenReturn("Empty From Subject");
    when(message.getFrom()).thenReturn(new Address[]{});
    when(message.getRecipients(Message.RecipientType.TO))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.getRecipients(Message.RecipientType.CC))
        .thenReturn(new Address[] {new InternetAddress("<EMAIL>")});
    when(message.isMimeType("text/plain")).thenReturn(true);
    when(message.getContent()).thenReturn("Plain text content.");

    EmailModel emailModel = EmailUtils.parseEmail(message);

    assertEquals("Empty From Subject", emailModel.getSubject());
    assertEquals("", emailModel.getSender());
    assertEquals(List.of("<EMAIL>"), emailModel.getTo());
    assertEquals(List.of("<EMAIL>"), emailModel.getCc());
    assertEquals("Plain text content.", emailModel.getTextBody());
  }
}
