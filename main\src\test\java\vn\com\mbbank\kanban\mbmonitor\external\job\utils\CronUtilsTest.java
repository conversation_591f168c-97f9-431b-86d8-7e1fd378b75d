package vn.com.mbbank.kanban.mbmonitor.external.job.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;

/**
 * Generated by K-tool
 * Created date: 2025-05-14
 */
class CronUtilsTest {

  private CronUtils cronUtils;

  @BeforeEach
  void setUp() {
    cronUtils = new CronUtils();
  }



  @Test
  void buildDailyCron_validTime_success() {
    // [EN] Input: A valid time string "10:30".
    // [VI] Input: Một chuỗi thời gian hợp lệ "10:30".
    // [EN] Expected: A Quartz cron expression for daily execution at 10:30:00 "0 30 10 * * ?".
    // [VI] Expected: <PERSON><PERSON><PERSON> biểu thức cron Quartz để thực thi hàng ngày lúc 10:30:00 "0 30 10 * * ?".
    String timeText = "10:30";
    String expectedCron = "0 30 10 * * ?";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertEquals(expectedCron, actualCron);
  }

  @Test
  void buildDailyCron_nullTime_returnsNull() {
    // [EN] Input: A null time string.
    // [VI] Input: Một chuỗi thời gian null.
    // [EN] Expected: Null should be returned.
    // [VI] Expected: Null sẽ được trả về.
    String timeText = null;

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertNull(actualCron);
  }

  @Test
  void buildDailyCron_emptyTime_returnsNull() {
    // [EN] Input: An empty time string.
    // [VI] Input: Một chuỗi thời gian trống.
    // [EN] Expected: Null should be returned.
    // [VI] Expected: Null sẽ được trả về.
    String timeText = "";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertNull(actualCron);
  }

  @Test
  void buildDailyCron_blankTime_returnsNull() {
    // [EN] Input: A blank time string.
    // [VI] Input: Một chuỗi thời gian trống.
    // [EN] Expected: Null should be returned.
    // [VI] Expected: Null should be returned.
    String timeText = "   ";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertNull(actualCron);
  }

  @Test
  void buildDailyCron_invalidTimeFormat_returnsNull() {
    // [EN] Input: An invalid time string "10:30:00".
    // [VI] Input: Một chuỗi thời gian không hợp lệ "10:30:00".
    // [EN] Expected: Null should be returned.
    // [VI] Expected: Null sẽ được trả về.
    String timeText = "10:30:00";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertNull(actualCron);
  }

  @Test
  void buildDailyCron_invalidTimeFormat2_returnsNull() {
    // [EN] Input: An invalid time string "1030".
    // [VI] Input: Một chuỗi thời gian không hợp lệ "1030".
    // [EN] Expected: Null should be returned.
    // [VI] Expected: Null sẽ được trả về.
    String timeText = "1030";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertNull(actualCron);
  }

  @Test
  void buildDailyCron_edgeCaseTime_success() {
    // [EN] Input: A valid time string "00:00".
    // [VI] Input: Một chuỗi thời gian hợp lệ "00:00".
    // [EN] Expected: A Quartz cron expression for daily execution at 00:00:00 "0 0 0 * * ?".
    // [VI] Expected: Một biểu thức cron Quartz để thực thi hàng ngày lúc 00:00:00 "0 0 0 * * ?".
    String timeText = "00:00";
    String expectedCron = "0 0 0 * * ?";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertEquals(expectedCron, actualCron);
  }

  @Test
  void buildDailyCron_edgeCaseTime2_success() {
    // [EN] Input: A valid time string "23:59".
    // [VI] Input: Một chuỗi thời gian hợp lệ "23:59".
    // [EN] Expected: A Quartz cron expression for daily execution at 23:59:00 "0 59 23 * * ?".
    // [VI] Expected: Một biểu thức cron Quartz để thực thi hàng ngày lúc 23:59:00 "0 59 23 * * ?".
    String timeText = "23:59";
    String expectedCron = "0 59 23 * * ?";

    String actualCron = CronUtils.buildDailyCron(timeText);

    assertEquals(expectedCron, actualCron);
  }
}