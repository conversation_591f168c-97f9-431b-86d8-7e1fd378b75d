package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;


import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;

/**
 * Repository table  CollectEmailConfig.
 */
@Repository
public interface CollectEmailConfigRepository
    extends JpaCommonRepository<CollectEmailConfigEntity, Long> {


  /**
   * find all collect email config by email config id.
   *
   * @param emailConfigId The id of email config .
   * @return The list of CollectEmailConfigEntity objects.
   */

  List<CollectEmailConfigEntity> findByEmailConfigIdAndActiveIsTrue(Long emailConfigId);
}
