package vn.com.mbbank.kanban.mbmonitor.common.mapper.execution;

import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;

/**
 * ExecutionResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExecutionScriptRequestMapper extends Kanban<PERSON>aseMapper<ExecutionScriptRequest, ExecutionEntity> {
  ExecutionScriptRequestMapper INSTANCE = Mappers.getMapper(ExecutionScriptRequestMapper.class);

  /**
   * Maps the provided ExecutionEntity to an ExecutionScriptRequest, sets the executionBy field,
   * and updates the params field if a non-empty list of parameters is provided.
   *
   * @param executionEntity   the ExecutionEntity to be mapped.
   * @param params            the list of ExecuteScriptParamModel parameters to be set in the request.
   * @param executionBy       the identifier of who is executing the request.
   * @param request           ExecuteScriptRequest
   * @return the mapped ExecutionScriptRequest populated with provided data.
   */
  default ExecutionScriptRequest map(ExecutionEntity executionEntity, List<ExecuteScriptParamModel> params,
                                     String executionBy, ExecuteScriptRequest request) {
    var res = new ExecutionScriptRequest();
    res.setExecutionId(executionEntity.getId());
    res.setPaginationRequest(request.getPaginationRequest());
    res.setVariables(request.getVariables());
    res.setExecutionBy(executionBy);
    if (CollectionUtils.isNotEmpty(params)) {
      res.setParams(params);
    }
    return res;
  }
}
