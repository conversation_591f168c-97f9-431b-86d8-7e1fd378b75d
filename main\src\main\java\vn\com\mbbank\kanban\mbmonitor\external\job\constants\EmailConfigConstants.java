package vn.com.mbbank.kanban.mbmonitor.external.job.constants;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * EmailConfigConstants.
 */
@FieldDefaults(level = AccessLevel.PUBLIC, makeFinal = true)
public class EmailConfigConstants {
  public static final String IMAP_PROTOCOL = "imap";
  public static final String DEFAULT_READ_FOLDER = "INBOX";
  public static final String KEY_IMAP_SSL_ENABLE = "mail.imap.ssl.enable";
  public static final String KEY_TRANSPORT_PROTOCOL =
      "mail.transport.protocol";
  public static final String KEY_DEBUG =
      "mail.debug";
  public static final String KEY_IMAP_CONNECTION_TIMEOUT = "mail.imap.connectiontimeout";
  public static final String KEY_IMAP_TIMEOUT = "mail.imap.timeout";
  public static final String VALUE_IMAP_CONNECTION_TIMEOUT = "600000";
  public static final String VALUE_IMAP_TIMEOUT = "60000";

}
