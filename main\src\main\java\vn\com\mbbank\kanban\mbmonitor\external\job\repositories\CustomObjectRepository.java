package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;

/**
 * Repository table  Custom object.
 */
@Repository
public interface CustomObjectRepository extends JpaCommonRepository<CustomObjectEntity, Long> {
  /**
   * find Custom object by alertGroupConfigId.
   *
   * @param alertGroupConfigId alertGroupConfigId.
   * @return list of custom object.
   */
  @Query(value = """
      SELECT customObject.*
      FROM CUSTOM_OBJECT customObject
          JOIN ALERT_GROUP_CONFIG_CONDITION alertGroupConfigCondition
               ON customObject.ID = alertGroupConfigCondition.CUSTOM_OBJECT_ID
      WHERE alertGroupConfigCondition.ALERT_GROUP_CONFIG_ID = :alertGroupConfigId
      """, nativeQuery = true)
  List<CustomObjectEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

  /**
   * Retrieves all custom objects that are not marked as deleted.
   *
   * @return a list of active {@link CustomObjectEntity} objects
   */
  List<CustomObjectEntity> findAllByDeletedIsFalse();
}
