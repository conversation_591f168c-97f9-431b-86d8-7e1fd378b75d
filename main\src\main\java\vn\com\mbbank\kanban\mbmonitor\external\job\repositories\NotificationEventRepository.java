package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;


import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;

/**
 * Repository for NotificationEventEntity.
 */
@Repository
public interface NotificationEventRepository
    extends JpaCommonRepository<NotificationEventEntity, String> {

  /**
   * find all by active true.
   *
   * @return list of NotificationEventEntity
   */
  List<NotificationEventEntity> findAllByActiveTrue();

  /**
   * find all by active true and schedule type.
   *
   * @param scheduleType scheduleType
   * @return list of NotificationEventEntity
   */
  List<NotificationEventEntity> findAllByActiveTrueAndScheduleType(NotificationEventScheduleTypeEnum scheduleType);

}