package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;

/**
 * interface logic AutoTriggerActionConfigDependencyService.
 */
public interface AutoTriggerActionConfigDependencyService
        extends BaseService<AutoTriggerActionConfigDependencyEntity, String> {

  /**
   * find all AutoTriggerActionConfigDependencyEntity.
   *
   * @param configIds list id of auto trigger action config
   * @return list of AutoTriggerActionConfigDependencyEntity
   */
  List<AutoTriggerActionConfigDependencyEntity> findAllByAutoTriggerActionIdIn(List<String> configIds);
}
