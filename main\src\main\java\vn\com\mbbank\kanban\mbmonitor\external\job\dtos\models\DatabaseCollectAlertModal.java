package vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models;

import java.sql.Timestamp;
import lombok.Builder;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/19/2024
 */
@Data
@Builder
public class DatabaseCollectAlertModal {
  private AlertBaseModel alertBaseModel;
  private String alertCollectId;
  private Timestamp alertCollectDate;
}
