package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.AlertPriorityConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.EntityUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertPriorityConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;

/**
 * Service Logic Alert service.
 */
@Service
@RequiredArgsConstructor
public class AlertPriorityConfigServiceImpl extends BaseSoftServiceImpl<AlertPriorityConfigEntity, Long>
    implements AlertPriorityConfigService {

  private final AlertPriorityConfigRepository alertPriorityConfigRepository;


  @Override
  public List<AlertPriorityConfigEntity> findAllMatchPriorityConfig(String rawPriority, String rawAlertContent) {
    return alertPriorityConfigRepository.findAllMatchPriorityConfig(rawPriority, rawAlertContent);
  }

  @Override
  public List<String> findAllNameByIdIn(List<Long> ids) {
    if (CollectionUtils.isNotEmpty(ids)) {
      List<AlertPriorityConfigEntity> entities = alertPriorityConfigRepository.findAllByIdIn(ids);
      return EntityUtils.getFieldsByOrder(ids, entities, AlertPriorityConfigEntity::getId,
          AlertPriorityConfigEntity::getName);
    }
    return Collections.emptyList();
  }


  @Override
  public Long getPriorityConfig(Long alertPriorityConfigId,
                                String alertContent) {
    var matchedPriorityConfigs = findAllMatchPriorityConfig("", alertContent);
    var temp = new ArrayList<>(matchedPriorityConfigs);
    var customPriorityConfig = findById(alertPriorityConfigId);
    if (Objects.nonNull(customPriorityConfig)) {
      temp.add(customPriorityConfig);
    }
    var priorityConfig = AlertPriorityConfigUtils.getHighestPositionPriorityConfig(temp);
    return priorityConfig.map(AlertPriorityConfigEntity::getId).orElse(-1L);
  }

  @Override
  protected BaseSoftRepository<AlertPriorityConfigEntity, Long> getRepository() {
    return alertPriorityConfigRepository;
  }

  @Override
  public List<AlertPriorityConfigEntity> findAllByIdIn(List<Long> ids) {
    return alertPriorityConfigRepository.findAllByIdIn(ids);
  }
}
