package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.JobHistoryRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 12/17/2024
 */
@Service
@RequiredArgsConstructor
public class JobHistoryServiceImpl extends BaseServiceImpl<JobHistoryEntity, Long>
    implements JobHistoryService {
  private final JobHistoryRepository repository;

  @Override
  protected JpaCommonRepository<JobHistoryEntity, Long> getRepository() {
    return repository;
  }

}
