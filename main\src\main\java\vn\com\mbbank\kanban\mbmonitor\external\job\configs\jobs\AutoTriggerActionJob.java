package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import java.util.stream.Collectors;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 24/07/2025
 */
@Component(JobNameConstants.AUTO_TRIGGER_ACTION)
public class AutoTriggerActionJob extends JobConfig implements CommonBaseConsumerService {
  @Autowired
  private AutoTriggerActionService autoTriggerActionService;

  private static final Long EXECUTION_TIME_OUT_MS = 15 * 60 * 1000L;

  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException {
    String key = context.getJobDetail().getKey().getName();
    autoTriggerActionService.collect(key);
  }

  @Override
  public Map<String, String> getMappingJobNameAndCronTime() {
    var lstConfig = autoTriggerActionService.findAllByActiveTrueAndTriggerType();
    return lstConfig.stream().collect(Collectors.toMap(AutoTriggerActionConfigEntity::getId,
        e -> CronUtils.convertUnixToQuartz(e.getCronExpression())));
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.AUTO_TRIGGER_ACTION;
  }


  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    return Map.of();
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    var kafkaJobModel = KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var configId = String.valueOf(kafkaJobModel.configId());
    var cronTime = CronUtils.convertUnixToQuartz(kafkaJobModel.cronTime());
    changeConfigJob(kafkaJobModel.type(), configId, cronTime);
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.AUTO_TRIGGER_ACTION;
  }

  @Override
  public Long getLockTimeoutMs() {
    return EXECUTION_TIME_OUT_MS;
  }
}