package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsConfigRepository;

@ExtendWith(MockitoExtension.class)
/**
 * Generate by K-tool
 * Create date: 2025-05-09
 */
class TeamsConfigServiceImplTest {

  @Mock private TeamsConfigRepository teamsConfigRepository;

  @InjectMocks private TeamsConfigServiceImpl teamsConfigServiceImpl;

  @BeforeEach
  void setUp() {
    // Initialize mocks and the service implementation before each test
  }

  @Test
  void getRepository_SuccessTest() {
    // Input: No input
    // Expected: Returns the teamsConfigRepository.
    JpaCommonRepository<TeamsConfigEntity, String> repository =
        teamsConfigServiceImpl.getRepository();
    assertEquals(teamsConfigRepository, repository);
  }

  @Test
  void findAlertConfig_SuccessTest() {
    // Input:  teamsConfigRepository returns a TeamsConfigEntity
    // Expected: Returns an Optional containing the TeamsConfigEntity.
    TeamsConfigEntity expectedEntity = new TeamsConfigEntity();
    when(teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT))
        .thenReturn(Optional.of(expectedEntity));

    Optional<TeamsConfigEntity> actualEntity = teamsConfigServiceImpl.findAlertConfig();

    assertTrue(actualEntity.isPresent());
    assertEquals(expectedEntity, actualEntity.get());
    verify(teamsConfigRepository, times(1)).findFirstByType(TeamsConfigTypeEnum.ALERT);
  }

  @Test
  void findAlertConfig_NoAlertConfigFoundTest() {
    // Input: teamsConfigRepository returns an empty Optional
    // Expected: Returns an empty Optional.
    when(teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT))
        .thenReturn(Optional.empty());

    Optional<TeamsConfigEntity> actualEntity = teamsConfigServiceImpl.findAlertConfig();

    assertFalse(actualEntity.isPresent());
    verify(teamsConfigRepository, times(1)).findFirstByType(TeamsConfigTypeEnum.ALERT);
  }

  @Test
  void findAlertConfig_RepositoryThrowsExceptionTest() {
    // Input: teamsConfigRepository throws an exception
    // Expected: The exception is propagated.  This depends on how findAlertConfig is used,
    //   but in this case, we can assume the exception is not caught internally.
    when(teamsConfigRepository.findFirstByType(TeamsConfigTypeEnum.ALERT))
        .thenThrow(new RuntimeException("Simulated repository exception"));

    assertThrows(
        RuntimeException.class, () -> teamsConfigServiceImpl.findAlertConfig(), "Exception should be re-thrown");
    verify(teamsConfigRepository, times(1)).findFirstByType(TeamsConfigTypeEnum.ALERT);
  }
}