package vn.com.mbbank.kanban.mbmonitor.external.job.services;

/**
 * Redis Lock Service.
 */
public interface RedisLockService {
  /**
   * Tries to acquire a lock for the specified key.
   *
   * @param key The lock key.
   * @return true if lock success and false if lock fail
   */
  boolean tryLock(String key);

  /**
   * lock with timeout.
   *
   * @param key     key
   * @param timeout timeout
   * @return true/false
   */
  boolean tryLock(String key, Long timeout);

  /**
   * Releases the lock for the specified key.
   *
   * @param key The lock key.
   */
  void unlock(String key);
}
