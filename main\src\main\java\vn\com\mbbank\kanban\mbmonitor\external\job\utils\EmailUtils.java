package vn.com.mbbank.kanban.mbmonitor.external.job.utils;


import jakarta.mail.BodyPart;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMultipart;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.jsoup.Jsoup;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models.EmailModel;

/**
 * Utils read content email.
 */
public class EmailUtils {

  /**
   * Utils parse content email into email model.
   *
   * @param message message of email.
   * @return email model.
   */
  public static EmailModel parseEmail(Message message) throws MessagingException, IOException {
    String subject = StringUtils.defaultString(message.getSubject());
    String sender = Arrays.stream(message.getFrom())
        .map(address -> ((InternetAddress) address).getAddress())
        .findFirst()
        .orElse("");
    String textBody = getTextFromMessage(message).trim();
    List<String> toRecipients = message.getRecipients(Message.RecipientType.TO) != null
        ? Arrays.stream(message.getRecipients(Message.RecipientType.TO))
        .map(address -> ((InternetAddress) address).getAddress())
        .collect(Collectors.toList())
        : Collections.emptyList();
    List<String> ccRecipients = message.getRecipients(Message.RecipientType.CC) != null
        ? Arrays.stream(message.getRecipients(Message.RecipientType.CC))
        .map(address -> ((InternetAddress) address).getAddress())
        .collect(Collectors.toList())
        : Collections.emptyList();

    return EmailModel.builder()
        .subject(subject)
        .textBody(textBody)
        .sender(sender)
        .to(toRecipients)
        .cc(ccRecipients)
        .build();
  }


  static String getTextFromMessage(Message message) throws MessagingException, IOException {
    if (message.isMimeType("text/html")) {
      return Jsoup
          .parse(message.getContent().toString())
          .text();
    }
    if (message.isMimeType("text/plain")) {
      return message.getContent().toString();
    }
    if (message.isMimeType("multipart/*")) {
      MimeMultipart mimeMultipart = (MimeMultipart) message.getContent();
      return getTextFromMimeMultipart(mimeMultipart);
    }
    return "";
  }

  static String getTextFromMimeMultipart(MimeMultipart mimeMultipart) throws MessagingException, IOException {
    // TH multipart/alternative, choose end part and html to make body content of email
    if (mimeMultipart.getContentType().contains("multipart/alternative")) {
      for (int i = mimeMultipart.getCount() - 1; i >= 0; i--) {
        BodyPart bodyPart = mimeMultipart.getBodyPart(i);
        if (bodyPart.isMimeType("text/html")) {
          return Jsoup.parse(bodyPart.getContent().toString()).text();
        } else if (bodyPart.isMimeType("text/plain")) {
          return bodyPart.getContent().toString();
        }
      }
    } else {
      List<String> textParts = new ArrayList<>();
      for (int i = 0; i < mimeMultipart.getCount(); i++) {
        BodyPart bodyPart = mimeMultipart.getBodyPart(i);
        String text = getTextFromBodyPart(bodyPart);
        if (text != null && !text.isEmpty()) {
          textParts.add(text);
        }
      }
      return String.join("\n", textParts);
    }
    return "";
  }

  static String getTextFromBodyPart(BodyPart bodyPart) throws MessagingException, IOException {
    if (bodyPart.isMimeType("text/plain")) {
      return bodyPart.getContent().toString();
    }
    if (bodyPart.isMimeType("text/html")) {
      return Jsoup.parse(bodyPart.getContent().toString()).text();
    }
    if (bodyPart.getContent() instanceof MimeMultipart) {
      return getTextFromMimeMultipart((MimeMultipart) bodyPart.getContent());
    }
    return null;
  }
}
