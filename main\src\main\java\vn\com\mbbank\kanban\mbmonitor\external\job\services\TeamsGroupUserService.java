package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupUserEntity;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:19:36
 */
public interface TeamsGroupUserService extends BaseService<TeamsGroupUserEntity, String> {
  /**
   * Delete user old.
   *
   * @param teamsGroupChatIds teamsGroupChatIds
   * @return total record
   */
  @Transactional
  int deleteAllByTeamsGroupChatIdIn(List<String> teamsGroupChatIds);
}
