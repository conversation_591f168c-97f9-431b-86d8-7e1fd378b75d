package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;


import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;

/**
 * Repository table  AlertPriorityConfig.
 */
@Repository
public interface AlertPriorityConfigRepository
    extends BaseSoftRepository<AlertPriorityConfigEntity, Long>, AlertPriorityConfigRepositoryCustom {
  /**
   * find all Alert Priority by ids.
   *
   * @param ids list ids.
   * @return List of AlertPriorityConfigEntity.
   */
  List<AlertPriorityConfigEntity> findAllByIdIn(List<Long> ids);
}
