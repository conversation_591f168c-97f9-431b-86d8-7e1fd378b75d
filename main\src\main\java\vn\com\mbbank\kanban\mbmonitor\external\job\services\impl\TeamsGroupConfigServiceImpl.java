package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.TeamsGroupConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupConfigService;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:17:12
 */
@Service
@RequiredArgsConstructor
public class TeamsGroupConfigServiceImpl extends BaseServiceImpl<TeamsGroupConfigEntity, String>
    implements TeamsGroupConfigService {

  private final TeamsGroupConfigRepository teamsGroupConfigRepository;

  @Override
  protected JpaCommonRepository<TeamsGroupConfigEntity, String> getRepository() {
    return teamsGroupConfigRepository;
  }

  @Override
  public int deleteAllByTeamsConfigId(String teamsConfigId) {
    return teamsGroupConfigRepository.deleteAllByTeamsConfigId(teamsConfigId);
  }
}
