package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Store;
import java.util.Properties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.EmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolSecurityTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.external.job.constants.EmailConfigConstants;

@ExtendWith(MockitoExtension.class)
class EmailServiceImplTest {
  @Mock
  private Store storeMock;

  @Mock
  private Session sessionMock;
  @InjectMocks
  private EmailServiceImpl emailService;

  private EmailConfigModel emailConfig;

  @BeforeEach
  void setUp() throws Exception {
    emailConfig = new EmailConfigModel();
    emailConfig.setEmail("<EMAIL>");
    emailConfig.setPassword("password");
    emailConfig.setHost("imap.example.com");
    emailConfig.setSecurityType(EmailProtocolSecurityTypeEnum.SSL_TLS);

  }


  @Test
  void connectImapServer_shouldThrowBusinessException_whenInvalidConfig()
      throws MessagingException {
    EmailServiceImpl emailServiceSpy = Mockito.spy(emailService);

    Properties testProperties = new Properties();
    doReturn(testProperties).when(emailServiceSpy).configureImapProperties(emailConfig);

    try (MockedStatic<Session> mockedSession = mockStatic(Session.class)) {
      mockedSession.when(() -> Session.getInstance(testProperties)).thenReturn(sessionMock);
      when(sessionMock.getStore(EmailConfigConstants.IMAP_PROTOCOL)).thenReturn(storeMock);

      doThrow(MessagingException.class).when(storeMock)
          .connect(anyString(), anyString(), anyString());

      BusinessException exception =
          assertThrows(BusinessException.class,
              () -> emailServiceSpy.connectImapServer(emailConfig));
      assertEquals(ErrorCode.EMAIL_IMAP_CONNECTION_FAIL.getMessage(), exception.getMessage());
    }
  }

  @Test
  void connectImapServer_shouldReturnStore_whenValidConfig() throws Exception {
    // Arrange
    emailConfig.setSecurityType(EmailProtocolSecurityTypeEnum.SSL_TLS);
    EmailServiceImpl emailServiceSpy = Mockito.spy(emailService);
    Properties testProperties = new Properties();
    testProperties.setProperty("mail.imap.ssl.enable", "true");
    doReturn(testProperties).when(emailServiceSpy).configureImapProperties(emailConfig);
    try (MockedStatic<Session> mockedSession = mockStatic(Session.class)) {
      mockedSession.when(() -> Session.getInstance(testProperties)).thenReturn(sessionMock);
      when(sessionMock.getStore(EmailConfigConstants.IMAP_PROTOCOL)).thenReturn(storeMock);
      doAnswer(invocation -> null).when(storeMock).connect(anyString(), anyString(), any());
      Store resultStore = emailServiceSpy.connectImapServer(emailConfig);
      assertEquals(storeMock, resultStore);
    }
  }


  @Test
  void configureImapProperties_shouldSetNone_whenNone() {
    emailConfig.setSecurityType(EmailProtocolSecurityTypeEnum.NONE);
    Properties props = ReflectionTestUtils.invokeMethod(
        emailService, "configureImapProperties", emailConfig
    );
    assertEquals(null, props.getProperty("mail.imap.ssl.enable"));
  }

  @Test
  void configureImapProperties_shouldSetSSL_whenSSLEnabled() {
    Properties props = ReflectionTestUtils.invokeMethod(
        emailService, "configureImapProperties", emailConfig
    );
    assertEquals("true", props.getProperty("mail.imap.ssl.enable"));
  }


  @Test
  void configureImapProperties_shouldSetSSL_whenStartTLSEnabled() {
    emailConfig.setSecurityType(EmailProtocolSecurityTypeEnum.STARTTLS);
    Properties props = ReflectionTestUtils.invokeMethod(
        emailService, "configureImapProperties", emailConfig
    );
    assertEquals(null, props.getProperty("mail.imap.starttls.enable"));
  }

}
