package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;

/**
 * Auto generate.
 *
 * <AUTHOR>
 * @created_date 2025-05-07 14:17:12
 */
@Repository
public interface TeamsGroupConfigRepository
    extends JpaCommonRepository<TeamsGroupConfigEntity, String> {
  /**
   * delete old group chat.
   *
   * @param teamsConfigId teamsConfigId
   * @return total record
   */
  int deleteAllByTeamsConfigId(String teamsConfigId);
}
