package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.NoteRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NoteService;

/**
 * Service Logic note service.
 */
@Service
@RequiredArgsConstructor
public class NoteServiceImpl extends BaseServiceImpl<NoteEntity, Long> implements NoteService {

  private final NoteRepository noteRepository;

  @Override
  protected JpaCommonRepository<NoteEntity, Long> getRepository() {
    return noteRepository;
  }

  @Override
  public List<NoteEntity> findAllByAlertGroupId(Long alertGroupId) {
    return noteRepository.findAllByAlertGroupIdOrderByCreatedDateDesc(alertGroupId);
  }

  @Override
  public List<NoteEntity> findAllByAlertGroupIdInOrderByCreatedDateDesc(List<Long> alertGroupIds) {
    return noteRepository.findAllByAlertGroupIdInOrderByCreatedDateDesc(alertGroupIds);
  }
}
