package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.SysUserLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysUserLogModelMapper extends
    KanbanBaseMapper<SysUserLogModel, SysUserEntity> {
  SysUserLogModelMapper INSTANCE = Mappers.getMapper(SysUserLogModelMapper.class);

  /**
   * map SysUserLogModel.
   *
   * @param sysUser SysUserEntity.
   * @param roles   list of SysRoleEntity
   * @param isUserLocal is local or keycloak
   * @return SysUserLogModel
   */
  default SysUserLogModel map(SysUserEntity sysUser, List<SysRoleEntity> roles, Boolean isUserLocal) {
    return new SysUserLogModel(sysUser.getUserName(), sysUser.getEmail(),
        roles.stream().map(SysRoleEntity::getName).toList(), isUserLocal);
  }
}
