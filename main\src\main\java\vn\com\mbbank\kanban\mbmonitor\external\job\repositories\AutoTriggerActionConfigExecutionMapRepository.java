package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;

/**
 * Repository interface for managing modify alert configuration ExecutionMap data in the system.
 */
@Repository
public interface AutoTriggerActionConfigExecutionMapRepository
    extends JpaCommonRepository<AutoTriggerActionConfigExecutionMapEntity, String> {

  /**
   * find all AutoTriggerActionConfigExecutionMapEntity.
   *
   * @param autoTriggerActionConfigId id of config
   * @return list of AutoTriggerActionConfigExecutionMapEntity
   */
  List<AutoTriggerActionConfigExecutionMapEntity> findAllByAutoTriggerActionConfigId(String autoTriggerActionConfigId);

  /**
   * find all AutoTriggerActionConfigExecutionMapEntity by list id auto trigger.
   *
   * @param autoTriggerActionConfigIds of config
   * @return list of AutoTriggerActionConfigExecutionMapEntity
   */
  List<AutoTriggerActionConfigExecutionMapEntity> findAllByAutoTriggerActionConfigIdIn(
          List<String> autoTriggerActionConfigIds);
}
