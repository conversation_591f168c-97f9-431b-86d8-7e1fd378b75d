package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;

/**
 * AutoTriggerActionService.
 */
public interface AutoTriggerActionService extends BaseService<AutoTriggerActionConfigEntity, String> {

  /**
   * trigger job.
   *
   * @param singleAlerts        for trigger
   * @param groupIds            for find alerts to trigger
   * @param isCondition         check if condition
   * @param autoTriggerAction   for job
   */
  void triggerJob(List<AlertEntity> singleAlerts, List<Long> groupIds,
                  boolean isCondition, AutoTriggerActionConfigEntity autoTriggerAction);

  /**
   * Collect trigger.
   *
   * @param id id collect config
   * @throws BusinessException ex
   */
  @Transactional(rollbackFor = {Exception.class})
  void collect(String id) throws BusinessException;

  /**
   * find all config active.
   *
   * @return lst
   */
  List<AutoTriggerActionConfigEntity> findAllByActiveTrueAndTriggerType();
}
