package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;

/**
 * interface logic FilterAlertConfigService.
 */
public interface FilterAlertConfigService extends BaseService<FilterAlertConfigEntity, Long> {
  /**
   * Remove the alerts that match active configurations.
   *
   * @param alerts the list of {@link AlertBaseModel} objects to evaluate
   * @return the list of {@link AlertBaseModel} objects if filter applies
   */
  List<AlertBaseModel> updateAlertsForFilter(List<AlertBaseModel> alerts);
}
