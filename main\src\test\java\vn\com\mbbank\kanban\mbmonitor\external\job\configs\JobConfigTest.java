package vn.com.mbbank.kanban.mbmonitor.external.job.configs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.RedisLockService;

@ExtendWith(MockitoExtension.class)
public class JobConfigTest {

  @Mock
  private Scheduler scheduler;

  @Mock
  private RedisLockService redisLockService;

  @Mock
  private JobExecutionContext jobExecutionContext;

  @InjectMocks
  private JobConfig jobConfig = new JobConfig() {
    @Override
    public void executeJob(JobExecutionContext context) {
    }

    @Override
    public Map<String, Long> getMappingJobNameAndIntervalTime() {
      return null;
    }

    @Override
    public String getGroupName() {
      return "test";
    }
  };

  @BeforeEach
  public void setUp() {
    ReflectionTestUtils.setField(jobConfig, "lockTimeoutMs", 500L);
  }

  @Test
  public void execute_success() {
    JobDetail jobDetail = mock(JobDetail.class);
    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
    when(jobDetail.getKey()).thenReturn(new JobKey("testJob"));
    when(redisLockService.tryLock(anyString(), anyLong())).thenReturn(true);
    jobConfig.execute(jobExecutionContext);

    verify(redisLockService).tryLock(anyString(), anyLong());
  }

  @Test
  public void execute_throwsSchedulerException() throws SchedulerException {
    JobDetail jobDetail = mock(JobDetail.class);
    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
    when(jobDetail.getKey()).thenReturn(new JobKey("testJob"));
    when(redisLockService.tryLock(anyString(), anyLong())).thenReturn(false);
    jobConfig.execute(jobExecutionContext);
    verify(redisLockService).tryLock(anyString(), anyLong());
  }

  @Test
  public void scheduleJobIfNotExists_createsNewJob_success() throws SchedulerException {
    String jobName = "testJob";
    when(scheduler.checkExists(any(JobKey.class))).thenReturn(false);

    jobConfig.scheduleJobIfNotExists(jobName, 1000L);

    verify(scheduler).scheduleJob(any(JobDetail.class), any(Trigger.class));
  }

  @Test
  public void scheduleJobIfNotExists_jobAlreadyExists_success() throws SchedulerException {
    String jobName = "testJob";
    when(scheduler.checkExists(any(JobKey.class))).thenReturn(true);

    jobConfig.scheduleJobIfNotExists(jobName, 1000L);

    verify(scheduler, never()).scheduleJob(any(JobDetail.class), any(Trigger.class));
  }

  @Test
  public void rescheduleJob_success() throws SchedulerException {
    String jobName = "testJob";
    String jobGroup = "group1";
    JobKey jobKey = new JobKey(jobName, jobGroup);
    TriggerKey triggerKey = new TriggerKey(jobName + "Trigger", jobGroup);

    JobDetail jobDetail = mock(JobDetail.class);
    lenient().when(scheduler.getJobDetail(any())).thenReturn(jobDetail);

    jobConfig.rescheduleJob(jobName, 1000L);
  }

  @Test
  public void rescheduleJob_jobNotFound_success() throws SchedulerException {
    String jobName = "testJob";

    jobConfig.rescheduleJob(jobName, 1000L);

    verify(scheduler).scheduleJob(any(JobDetail.class), any(Trigger.class));
  }

  @Test
  public void buildTrigger_withDifferentIntervalTimes_success() {
    String jobName = "testJob";

    Trigger trigger = jobConfig.buildTrigger(jobName, 5000L);
    SimpleScheduleBuilder scheduleBuilder =
        (SimpleScheduleBuilder) ((SimpleTrigger) trigger).getScheduleBuilder();

    assertNotNull(trigger);
    assertEquals(5000L, ((SimpleTrigger) trigger).getRepeatInterval());
  }
}
