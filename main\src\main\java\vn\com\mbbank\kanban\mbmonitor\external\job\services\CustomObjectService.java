package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;

/**
 * interface logic service.
 */
public interface CustomObjectService extends BaseService<CustomObjectEntity, Long> {

  /**
   * calculator value custom object.
   *
   * @param content constains value of custom object.
   * @param id      id of custom object.
   * @return string value .
   */
  String calculatorCustomObjectValue(String content, String id);

  /**
   * find all custom object by alert group config.
   *
   * @param alertGroupConfigId alertGroupConfig.
   * @return list of custom object id.
   */
  List<CustomObjectEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId);

  /**
   * Retrieves all custom objects that are not marked as deleted.
   *
   * @return a list of active {@link CustomObjectEntity} objects
   */
  List<CustomObjectEntity> findAllByDeletedIsFalse();

  /**
   * Replaces custom object IDs (e.g., "@123") in the content with their corresponding values.
   * The value is computed based on the given textBody and the custom object ID.
   * If no value is found, "null" is used instead.
   * The final result is truncated to a maximum length defined by AlertConstants.CONTENT_MAX_LENGTH.
   *
   * @param data the text used for calculating custom object values
   * @param content  the content containing custom object IDs to replace
   * @return the content with custom object IDs replaced by their computed values
   */
  public String replaceCustomObjectIdsWithCustomObjectValues(String data, String content);


}
