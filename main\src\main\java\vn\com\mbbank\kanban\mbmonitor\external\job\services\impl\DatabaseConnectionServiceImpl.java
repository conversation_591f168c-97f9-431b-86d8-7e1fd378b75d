package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseConnectionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseConnectionService;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@AllArgsConstructor
@Service
public class DatabaseConnectionServiceImpl extends BaseServiceImpl<DatabaseConnectionEntity, Long>
    implements DatabaseConnectionService {
  private final DatabaseConnectionRepository databaseConnectionRepository;
  private final CommonDatabaseConnectionService commonDatabaseConnectionService;

  @Override
  protected JpaCommonRepository<DatabaseConnectionEntity, Long> getRepository() {
    return databaseConnectionRepository;
  }


  @Override
  public void checkConnection(DatabaseConnectionRequest connectInfo)
      throws BusinessException {
    if (!KanbanCommonUtil.isEmpty(connectInfo.getId())
        &&
        KanbanCommonUtil.isEmpty(connectInfo.getPassword())) {
      var connectInfoFromDb = databaseConnectionRepository.findById(connectInfo.getId())
          .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_EXISTS));
      connectInfo.setPassword(KanbanEncryptorUtils.decrypt(connectInfoFromDb.getPassword()));
    }
    if (!commonDatabaseConnectionService.testConnection(connectInfo)) {
      throw new BusinessException(ErrorCode.DATABASE_CONNECT_FALSE);
    }
  }

  @Override
  public void checkConnection(Long connectionId) throws BusinessException {
    var connectionFromDb = databaseConnectionRepository.findById(connectionId)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_FOUND));
    var connectionRequest = DatabaseConnectionRequestToEntityMapper.INSTANCE.map(connectionFromDb);
    connectionRequest.setPassword(KanbanEncryptorUtils.decrypt(connectionRequest.getPassword()));
    checkConnection(connectionRequest);

  }

  @Override
  public DatabaseConnectionEntity saveDataValid(DatabaseConnectionRequest request)
      throws BusinessException {
    if (!commonDatabaseConnectionService.testConnection(request)) {
      throw new BusinessException(ErrorCode.DATABASE_CONNECT_FALSE);
    }
    DatabaseConnectionEntity databaseConnectionEntity =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.mapTo(request);
    databaseConnectionEntity.setName(
        KanbanStringUtils.formatStandardName(databaseConnectionEntity.getName()));

    if (!KanbanCommonUtil.isEmpty(databaseConnectionEntity.getId())) {
      var configFromDb = Optional.ofNullable(findById(request.getId()))
          .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_FOUND));

      if (databaseConnectionRepository.existsByNameAndIdNot(configFromDb.getName(),
          configFromDb.getId())) {
        throw new BusinessException(ErrorCode.DATABASE_CONNECTION_EXISTS);
      }
    } else if (databaseConnectionRepository.existsByName(request.getName())) {
      throw new BusinessException(ErrorCode.DATABASE_CONNECTION_EXISTS);
    }
    databaseConnectionEntity.setType(request.getType());
    databaseConnectionEntity.setOracleConnectType(request.getOracleConnectType());
    databaseConnectionEntity.setUserName(request.getUserName());
    databaseConnectionEntity.setPassword(KanbanEncryptorUtils.encrypt(request.getPassword()));
    databaseConnectionEntity.setIsActive(true);
    return save(databaseConnectionEntity);
  }

  @Override
  public int setActiveById(Long id, boolean active) {
    return databaseConnectionRepository.setActiveById(id, active);
  }

}