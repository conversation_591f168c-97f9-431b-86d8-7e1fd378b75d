package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;

/**
 * interface logic note.
 */
public interface NoteService extends BaseService<NoteEntity, Long> {

  /**
   * Find comment by alertId.
   *
   * @param alertGroupId alertGroupId
   * @return list CommentEntity
   */
  List<NoteEntity> findAllByAlertGroupId(Long alertGroupId);

  /**
   * Find comment by alertId.
   *
   * @param alertGroupIds list alert group id.
   * @return list CommentEntity
   */
  List<NoteEntity> findAllByAlertGroupIdInOrderByCreatedDateDesc(List<Long> alertGroupIds);

}
