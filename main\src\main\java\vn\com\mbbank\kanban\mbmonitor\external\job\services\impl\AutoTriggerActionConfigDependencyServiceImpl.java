package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigDependencyRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionConfigDependencyService;

@Service
@RequiredArgsConstructor
public class AutoTriggerActionConfigDependencyServiceImpl
        extends BaseServiceImpl<AutoTriggerActionConfigDependencyEntity, String>
        implements AutoTriggerActionConfigDependencyService {

  private final AutoTriggerActionConfigDependencyRepository autoTriggerActionConfigDependencyRepository;

  @Override
  protected JpaCommonRepository<AutoTriggerActionConfigDependencyEntity, String> getRepository() {
    return autoTriggerActionConfigDependencyRepository;
  }


  @Override
  public List<AutoTriggerActionConfigDependencyEntity> findAllByAutoTriggerActionIdIn(List<String> configIds) {
    return autoTriggerActionConfigDependencyRepository.findAllByAutoTriggerActionConfigIdIn(configIds);
  }
}
