package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;

/**
 * Repository interface for managing modify alert configuration dependency data in the system.
 */
@Repository
public interface ModifyAlertConfigModifyRepository
    extends JpaCommonRepository<ModifyAlertConfigModifyEntity, Long> {


  /**
   * find all ModifyAlertConfigModifyEntity.
   *
   * @param modifyAlertConfigId id of config
   * @return list of ModifyAlertConfigDependencyEntity
   */
  List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigId(Long modifyAlertConfigId);

  /**
   * delete all modification.
   *
   * @param modifyAlertConfigId id of config
   */
  void deleteAllByModifyAlertConfigId(Long modifyAlertConfigId);

  /**
   * find All By list ModifyAlertConfig Id.
   *
   * @param modifyAlertConfigIds list id of config
   * @return list entity modifyAlertConfig
   */
  List<ModifyAlertConfigModifyEntity> findAllByModifyAlertConfigIdIn(List<Long> modifyAlertConfigIds);
}
