package vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports;

/**
 * Represents a function that accepts three arguments and produces a result.
 * This functional interface allows for checked exceptions.
 *
 * @param <T> the type of the first argument
 * @param <U> the type of the second argument
 * @param <V> the type of the third argument
 * @param <R> the type of the result
 */
@FunctionalInterface
public interface TriFunction<T, U, V, R> {

  /**
   * Applies this function to the given arguments.
   *
   * @param t the first input argument
   * @param u the second input argument
   * @param v the third input argument
   * @return the function result
   * @throws Exception if unable to compute a result
   */
  R apply(T t, U u, V v) throws Exception;
}
