package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysLogEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 */
@Service
public interface SysLogService extends BaseService<SysLogEntity, String>,
    CommonBaseConsumerService {
  /**
   * save log.
   *
   * @param log log
   * @return SysLogEntity
   */
  SysLogEntity saveLog(SysLogModel log) throws BusinessException;
}
