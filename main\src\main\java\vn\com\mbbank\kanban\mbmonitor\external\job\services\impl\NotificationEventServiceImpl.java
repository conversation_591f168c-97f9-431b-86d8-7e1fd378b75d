package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationEventResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventTargetEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventTargetTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.NotificationEventResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.PushNotificationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.NotificationEventRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NotificationEventService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NotificationEventTargetService;

/**
 * Implementation of NotificationEventService.
 */
@Service
@RequiredArgsConstructor
public class NotificationEventServiceImpl extends BaseServiceImpl<NotificationEventEntity, String>
    implements NotificationEventService {

  private static final Logger logger = LoggerFactory.getLogger(NotificationEventServiceImpl.class);
  private final NotificationEventRepository notificationEventRepository;
  private final NotificationEventTargetService notificationEventTargetService;
  private final PushNotificationService pushNotificationService;
  private final NotificationEventResponseMapper notificationEventResponseMapper =
      NotificationEventResponseMapper.INSTANCE;

  @Override
  public List<NotificationEventResponse> findAllByActiveTrue() {
    var response = notificationEventRepository.findAllByActiveTrue();
    var targets = notificationEventTargetService.findAllByNotificationEventIdIn(
        response.stream().map(NotificationEventEntity::getId).toList());
    var targetMap =
        targets.stream().collect(Collectors.groupingBy(NotificationEventTargetEntity::getNotificationEventId));
    return response.stream().map(
            entity -> notificationEventResponseMapper.map(entity, targetMap.getOrDefault(entity.getId(), List.of())))
        .toList();
  }

  @Override
  public void pushNotification(String notificationId) throws BusinessException {
    var notification = notificationEventRepository.findById(notificationId).orElseThrow(() -> new BusinessException(
        ErrorCode.NOTIFICATION_EVENT_NOT_FOUND));
    var targets = notificationEventTargetService.findAllByNotificationEventId(notificationId);
    var userNames = new ArrayList<String>();
    var rolesId = new ArrayList<Long>();
    for (NotificationEventTargetEntity target : targets) {
      if (NotificationEventTargetTypeEnum.USER.equals(target.getType())) {
        userNames.add(target.getTarget());
      } else if (NotificationEventTargetTypeEnum.ROLE.equals(target.getType())) {
        rolesId.add(Long.parseLong(target.getTarget()));
      }
    }
    var notificationRequest = NotificationRequest.builder()
        .title(notification.getTitle())
        .content(notification.getContent())
        .type(notification.getNotificationType())
        .sourceId(notificationId)
        .sourceType(NotificationSourceTypeEnum.EVENTS)
        .userNames(userNames)
        .roleIds(rolesId)
        .build();
    try {
      pushNotificationService.pushSync(notificationRequest);
    } catch (Exception e) {
      logger.error("Error when push notification with id: {} with message: {}", notificationId, e.getMessage(), e);
    }
  }

  @Override
  public List<NotificationEventEntity> findAllByActiveTrueAndScheduleType(
      NotificationEventScheduleTypeEnum scheduleType) {
    return notificationEventRepository.findAllByActiveTrueAndScheduleType(scheduleType);
  }

  @Override
  protected JpaCommonRepository<NotificationEventEntity, String> getRepository() {
    return notificationEventRepository;
  }
}