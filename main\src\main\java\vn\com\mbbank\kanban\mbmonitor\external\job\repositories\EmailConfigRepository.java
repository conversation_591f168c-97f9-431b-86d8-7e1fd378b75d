package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

/**
 * Repository table  EmailConfig.
 */
@Repository
public interface EmailConfigRepository extends JpaCommonRepository<EmailConfigEntity, Long> {
  /**
   * Returns the count of EmailConfigEntity entries that match the provided username
   * and protocol types, excluding the specified ID.
   *
   * @param protocolType the list of protocol types to filter by
   * @return the list of matching EmailConfigEntity entries
   */
  List<EmailConfigEntity> findAllByProtocolTypeInAndIntervalTimeIsNotNull(
      Collection<EmailProtocolTypeEnum> protocolType);

}
