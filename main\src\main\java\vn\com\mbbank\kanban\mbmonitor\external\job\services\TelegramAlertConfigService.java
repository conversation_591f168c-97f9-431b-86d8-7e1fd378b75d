package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 */
@Service
public interface TelegramAlertConfigService extends BaseService<TelegramAlertConfigEntity, String>,
    CommonBaseConsumerService {
  /**
   * send message to telegram.
   *
   * @param alert alert
   */
  void sendMessageToTelegram(AlertEntity alert);
}
