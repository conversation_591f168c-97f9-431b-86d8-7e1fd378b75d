package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;

/**
 * interface logic EmailPartner.
 */
public interface FileStorageService extends BaseSoftService<FileStorageEntity, Long> {


  /**
   * Registers a file in the storage system.
   *
   * @param filePath       the path of the file to be stored.
   * @param dependencyName the name of the related dependency.
   * @param dependencyId   the identifier of the related dependency.
   * @return the registered {@link FileStorageEntity}.
   * @throws BusinessException if the file registration fails due to business constraints.
   */
  FileStorageEntity registerFile(String filePath, String dependencyName, String dependencyId)
      throws BusinessException;

  /**
   * Retrieves a list of files by their IDs.
   *
   * @param list the list of file IDs to search for.
   * @return a list of matching {@link FileStorageEntity} instances.
   */
  List<FileStorageEntity> findByIdIn(List<Long> list);

  /**
   * Retrieves files that do not match the given IDs but belong to a specific dependency.
   *
   * @param list           the list of file IDs to exclude.
   * @param dependencyName the name of the dependency to filter by.
   * @return a list of {@link FileStorageEntity} that match the criteria.
   */
  List<FileStorageEntity> findByIdNotInAndDependencyName(List<Long> list, String dependencyName);

  /**
   * Deletes files that have timed out.
   *
   * @param folder            the folder where the files are stored.
   * @param filePathsToDelete the list of file paths to remove.
   */
  void deleteExpiredFile(String folder, List<String> filePathsToDelete);

}