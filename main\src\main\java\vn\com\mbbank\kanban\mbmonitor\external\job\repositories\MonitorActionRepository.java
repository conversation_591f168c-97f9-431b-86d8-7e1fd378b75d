package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;

/**
 * Repository table monitor action.
 */
@Repository
public interface MonitorActionRepository extends JpaCommonRepository<MonitorActionEntity, String> {
  
  /**
   *  Find all monitor action by actionId.
   *
   * @param actionId actionId
   * @return list MonitorActionEntity.
   */
  List<MonitorActionEntity> findByActionIdOrderByOrdersAsc(String actionId);
  
}
