package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanDateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CronUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NotificationEventService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/22/2024
 */
@Component(JobNameConstants.NOTIFICATION_EVENT)
public class NotificationEventJob extends JobConfig implements CommonBaseConsumerService {
  private static final Logger logger = LoggerFactory.getLogger(NotificationEventJob.class);
  @Autowired
  private NotificationEventService notificationEventService;

  @Value("${monitor.redis.lock.database.timeout:10000}")
  private Long lockTimeoutMs;

  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException {
    String key = context.getJobDetail().getKey().getName();
    notificationEventService.pushNotification(key);
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    return Map.of();
  }

  @Override
  public Map<String, String> getMappingJobNameAndCronTime() {
    var configs = notificationEventService.findAllByActiveTrueAndScheduleType(
        NotificationEventScheduleTypeEnum.CRON_EXPRESSION);
    return configs.stream().collect(Collectors.toMap(NotificationEventEntity::getId,
        notificationEvent -> CronUtils.convertUnixToQuartz(notificationEvent.getCronExpression())));
  }

  @Override
  public Map<String, Date> getMappingJobNameAndTriggeredDate() {
    var configs = notificationEventService.findAllByActiveTrueAndScheduleType(
        NotificationEventScheduleTypeEnum.ONE_TIME);
    return configs.stream().collect(Collectors.toMap(NotificationEventEntity::getId,
        NotificationEventEntity::getTriggeredDate));
  }

  @Override
  public String getGroupName() {
    return JobNameConstants.NOTIFICATION_EVENT;
  }

  @Override
  public Long getLockTimeoutMs() {
    return this.lockTimeoutMs;
  }

  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.NOTIFICATION_EVENT;
  }

  @Override
  public boolean isKafkaMultipleGroup() {
    return true;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    KafkaJobModel kafkaJobModel =
        KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var id = kafkaJobModel.configId().toString();
    if (!KanbanCommonUtil.isEmpty(kafkaJobModel.triggeredDate())) {
      logger.info("Change config job for notification event with id: onetime {}", id);
      changeConfigJob(kafkaJobModel.type(), id,
          KanbanDateUtils.convertStringToDate(kafkaJobModel.triggeredDate(), KanbanDateUtils.FORMAT_DDMMYYYY_HHMMSS));
    } else if (!KanbanCommonUtil.isEmpty(kafkaJobModel.cronTime())) {
      logger.info("Change config job for notification event with id: cronjob {}", id);
      var cronTime = CronUtils.convertUnixToQuartz(kafkaJobModel.cronTime());
      changeConfigJob(kafkaJobModel.type(), id, cronTime);
    }
  }
}
