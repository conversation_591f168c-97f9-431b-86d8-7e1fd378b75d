package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.persistence.EntityManager;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ServiceWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ServicePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ServiceResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import vn.com.mbbank.kanban.mbmonitor.external.job.ApplicationTest;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ApplicationRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ServiceRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.BatchFetcher;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.ExportFileProcessor;

@ExtendWith(MockitoExtension.class)
class ServiceServiceImplTest extends ApplicationTest {

  @Mock
  ServiceRepository serviceRepository;

  @Mock
  ApplicationRepository applicationRepository;
  @InjectMocks
  ServiceServiceImpl serviceServiceImpl;

  @Mock
  EntityManager entityManager;

  @Mock
  ExportFileProcessor exportFileProcessor;
  @TempDir
  Path tempDir;


  @Test
  void getRepository_success() {
    JpaCommonRepository<ServiceEntity, String> result = serviceServiceImpl.getRepository();
    assertEquals(serviceRepository, result);
  }

  @Test
  void findFirstByNameIgnoreCase_success() {
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1L");
    when(serviceRepository.findFirstByNameIgnoreCaseAndDeletedFalse(any())).thenReturn(
        Optional.of(serviceEntity));
    var result = serviceServiceImpl.findFirstByNameIgnoreCase("1L");
    assertEquals("1L", result.get().getId());
  }

  @Test
  void findServiceWithPriorityByAlertStatus_success() {
    List<ServiceWithPriorityModel> services =
        List.of(
            new ServiceWithPriorityModel("1L", "test",
                AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID,
                1),
            new ServiceWithPriorityModel("1L", "test",
                AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID,
                1));
    when(serviceRepository.findServiceWithPriorityByAlertGroupStatus(
        any(AlertGroupStatusEnum.class))).thenReturn(services);

    var result =
        serviceServiceImpl.findServiceWithPriorityByAlertGroupStatus(AlertGroupStatusEnum.CLOSE);

    assertEquals(1, result.size());
    verify(serviceRepository).findServiceWithPriorityByAlertGroupStatus(
        any(AlertGroupStatusEnum.class));
  }


  @Test
  void findWithPaging_success_withDeleted() {
    // Mock JpaEntityInformation
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);

    // Mock getIdAttributeNames to return a set with a single value "id"
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));

    // Mock static method JpaEntityInformationSupport.getEntityInformation
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      // Mock the static method to return mockEntityInformation
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);

      // Now, when serviceServiceImpl.getEntityInformation() is called, it will return the mock
      ServicePaginationRequest servicePaginationRequest = new ServicePaginationRequest();
      servicePaginationRequest.setWithDeleted(true);
      servicePaginationRequest.setSortOrder(SortType.ASC);
      servicePaginationRequest.setSortBy("123");

      // Call the method under test
      var res = serviceServiceImpl.findWithPaging(servicePaginationRequest);

      // Assertions (Adjust based on your expectations)
      assertNull(res);  // Example assertion, adapt as needed
    }
  }

  @Test
  void findWithPaging_success() {
    // Mock JpaEntityInformation
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);

    // Mock getIdAttributeNames to return a set with a single value "id"
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));

    // Mock static method JpaEntityInformationSupport.getEntityInformation
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      // Mock the static method to return mockEntityInformation
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      ServicePaginationRequest servicePaginationRequest = new ServicePaginationRequest();
      servicePaginationRequest.setWithDeleted(false);
      servicePaginationRequest.setSortOrder(SortType.ASC);
      servicePaginationRequest.setSortBy("123");
      when(serviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          null);
      var res = serviceServiceImpl.findWithPaging(servicePaginationRequest);
      assertNull(res);
    }


  }

  @Test
  void findWithPaging_success_nameNotEmpty() {
    // Mock JpaEntityInformation
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);

    // Mock getIdAttributeNames to return a set with a single value "id"
    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));

    // Mock static method JpaEntityInformationSupport.getEntityInformation
    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      // Mock the static method to return mockEntityInformation
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      ServicePaginationRequest servicePaginationRequest = new ServicePaginationRequest();
      servicePaginationRequest.setWithDeleted(false);
      servicePaginationRequest.setSortOrder(SortType.ASC);
      servicePaginationRequest.setSortBy("123");
      servicePaginationRequest.setName("123");
      when(serviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          null);
      var res = serviceServiceImpl.findWithPaging(servicePaginationRequest);
      assertNull(res);
    }

  }

  @Test
  void generateId_success() {
    when(serviceRepository.getNextSequenceValue()).thenReturn(1L);
    var res = serviceServiceImpl.generateId();
    assertEquals("S00001", res);
  }


  @Test
  public void genTitleForFileExport_WithSearchKeyword_success() {
    ServicePaginationRequest request = new ServicePaginationRequest();
    request.setSearch("testKeyword");
    String result = serviceServiceImpl.genTitleForFileExport(request);
    assertEquals(
        "Service - Filter by condition: ServiceName/Description contain keyword testKeyword.",
        result);
  }

  @Test
  void findAllNameByIdIn_WithEmptyIds() {
    List<String> actualNames = serviceServiceImpl.findAllNameByIdIn(List.of());
    assertNotNull(actualNames);
    assertTrue(actualNames.isEmpty());
  }

  @Test
  public void findAllNameByIdIn_WithValidIds_success() {
    List<String> ids = Arrays.asList("1", "2", "3");
    List<ServiceEntity> entities = Arrays.asList(
        ServiceEntity.builder().id("1").name("Service A").build(),
        ServiceEntity.builder().id("2").name("Service B").build(),
        ServiceEntity.builder().id("3").name("Service C").build()
    );
    when(serviceRepository.findAllByIdIn(ids)).thenReturn(entities);
    List<String> result = serviceServiceImpl.findAllNameByIdIn(ids);
    List<String> expected = Arrays.asList("Service A", "Service B", "Service C");
    assertEquals(expected, result);
    verify(serviceRepository, times(1)).findAllByIdIn(ids);
  }

  @Test
  public void findAllNameByIdIn_WithEmptyIds_success() {
    List<String> ids = Collections.emptyList();
    List<String> result = serviceServiceImpl.findAllNameByIdIn(ids);
    assertTrue(result.isEmpty());
    verify(serviceRepository, never()).findAllByIdIn(anyList());
  }

  @Test
  public void findAllNameByIdIn_WithNonExistentIds_success() {
    List<String> ids = Arrays.asList("4", "5");
    when(serviceRepository.findAllByIdIn(ids)).thenReturn(Collections.emptyList());
    List<String> result = serviceServiceImpl.findAllNameByIdIn(ids);
    assertTrue(result.isEmpty());
    verify(serviceRepository, times(1)).findAllByIdIn(ids);
  }

  @Test
  public void findAllByIdIn_WithNonExistentIds_success() {
    List<String> ids = Arrays.asList("4", "5");
    when(serviceRepository.findAllById(anyList())).thenReturn(Collections.emptyList());
    List<ServiceEntity> result = serviceServiceImpl.findAllByIdIn(ids);
    assertTrue(result.isEmpty());
    verify(serviceRepository).findAllById(anyList());
  }

  @Test
  public void findAllByIdIn_WithNonExistentIds_success_caseIdEmpty() {
    List<String> ids = List.of();
    List<ServiceEntity> result = serviceServiceImpl.findAllByIdIn(ids);
    assertTrue(result.isEmpty());
  }

  @Test
  void findAllByNameIgnoreCaseInOrServiceIdInAndDeletedFalse_Success() {
    when(serviceRepository.findAllByNameIgnoreCaseInOrServiceIdInAndDeletedFalse(any(),
        any())).thenReturn(
        new ArrayList<>());

    List<ServiceEntity> result =
        serviceServiceImpl.findAllByNameIgnoreCaseInOrServiceIdIn(List.of("serviceNameSource"),
            List.of("123"));
    Assertions.assertEquals(0, result.size());
  }

  @Test
  void exportFile_success_emptyPage() throws IOException, BusinessException {
    ExportFileServiceRequest request = new ExportFileServiceRequest();
    request.setNumberOfResults(10);
    ServicePaginationRequest paginationRequest = new ServicePaginationRequest();
    paginationRequest.setWithDeleted(false);
    paginationRequest.setSortOrder(SortType.ASC);
    paginationRequest.setSortBy("123");
    request.setPaginationRequest(paginationRequest);
    ExportDataModel exportDataModel = new ExportDataModel();
    exportDataModel.setExtension(ExportFileTypeEnum.CSV);
    request.setExportDataModel(exportDataModel);
    String userName = "testUser";
    JpaEntityInformation mockEntityInformation = mock(JpaEntityInformation.class);

    when(mockEntityInformation.getIdAttributeNames()).thenReturn(Set.of("id"));

    try (MockedStatic<JpaEntityInformationSupport> mockedStatic = Mockito.mockStatic(
        JpaEntityInformationSupport.class)) {
      mockedStatic.when(() -> JpaEntityInformationSupport.getEntityInformation(any(), any()))
          .thenReturn(mockEntityInformation);
      ServicePaginationRequest servicePaginationRequest = new ServicePaginationRequest();
      servicePaginationRequest.setWithDeleted(false);
      servicePaginationRequest.setSortOrder(SortType.ASC);
      servicePaginationRequest.setSortBy("123");
      servicePaginationRequest.setName("123");
      when(serviceRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(
          Page.empty());
      String filePathStr = tempDir.resolve("export_empty.csv").toString();
      FileStorageEntity fileStorageEntity = new FileStorageEntity();
      fileStorageEntity.setPath(filePathStr);
      when(exportFileProcessor.exportFileCommon(any(), any(), any(), any(), any())).thenAnswer(invocation -> {
        BatchFetcher<ServiceResponse> batchFetcher = invocation.getArgument(4);
        List<ServiceResponse> fetched = batchFetcher.fetch(0, 10);
        return fileStorageEntity;
      });
      FileStorageEntity result = serviceServiceImpl.exportFile(request, userName, filePathStr);
      assertNotNull(result);
      assertEquals(filePathStr, result.getPath());
    }
  }

}

