package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigExecutionMapRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionConfigExecutionMapService;

@Service
@RequiredArgsConstructor
public class AutoTriggerActionConfigExecutionMapServiceImpl
        extends BaseServiceImpl<AutoTriggerActionConfigExecutionMapEntity, String>
        implements AutoTriggerActionConfigExecutionMapService {

  private final AutoTriggerActionConfigExecutionMapRepository repository;

  @Override
  protected JpaCommonRepository<AutoTriggerActionConfigExecutionMapEntity, String> getRepository() {
    return repository;
  }

  @Override
  public List<AutoTriggerActionConfigExecutionMapEntity> findAllByAutoTriggerActionConfigId(
          String autoTriggerActionConfigId) {
    return repository.findAllByAutoTriggerActionConfigId(autoTriggerActionConfigId);
  }

  @Override
  public List<AutoTriggerActionConfigExecutionMapEntity> findAllByAutoTriggerActionConfigIdIn(List<String> configIds) {
    return repository.findAllByAutoTriggerActionConfigIdIn(configIds);
  }
}
