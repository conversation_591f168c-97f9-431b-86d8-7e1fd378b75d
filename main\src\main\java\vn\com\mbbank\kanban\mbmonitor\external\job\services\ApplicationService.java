package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationWithPriorityResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonApplicationService;


/**
 * interface logic application.
 */
public interface ApplicationService extends BaseSoftService<ApplicationEntity, String>,
    CommonApplicationService {
  /**
   * Finds all applications by a list of service IDs.
   *
   * @param applicationPaginationRequest the pagination request containing paging details
   *                                     and the list of service IDs to filter applications
   * @return a paginated list of ApplicationResponseDto
   */
  Page<ApplicationResponse> findAll(
      ApplicationPaginationRequest applicationPaginationRequest);

  /**
   * Finds all applications by  service ID.
   *
   * @param serviceId service ID
   * @return a list of ApplicationResponseDto
   */

  List<ApplicationEntity> findAllByServiceId(String serviceId);

  /**
   * Find by Name.
   *
   * @param applicationNameSource applicationNameSource
   * @param serviceId             serviceId
   * @return ApplicationEntity
   */
  Optional<ApplicationEntity> findFirstByNameIgnoreCaseAndServiceId(String applicationNameSource,
                                                                    String serviceId);

  /**
   * Find application by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list Application
   */
  List<ApplicationWithPriorityResponse> findApplicationWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus);

  /**
   * Find application by id.
   *
   * @param id applicationId
   * @return ApplicationResponse
   */
  ApplicationResponse findApplicationById(String id);


  /**
   * Checks if any ApplicationEntity record exists with the given name.
   *
   * @param name      The name of the view to check for existence.
   * @param serviceId id of service
   * @return true if at least one ApplicationEntity record with the specified name exists, false otherwise.
   */
  boolean existByNameAndServiceId(String name, String serviceId);

  /**
   * Checks if any ApplicationEntity record exists with the given name and ID.
   *
   * @param name      The name of ApplicationEntity.
   * @param serviceId id of service
   * @param id        The ID of the ApplicationEntity record.
   * @return true if a ApplicationEntity record with the specified name and ID exists, false otherwise.
   */
  boolean existByIdNotAndNameAndServiceId(String id, String name, String serviceId);


  /**
   * Generates an ID with a prefix 'A' followed by a sequence number
   * formatted to a length of 5 digits with leading zeros.
   *
   * @return A formatted ID string.
   */
  String generateId();


  /**
   * Counts the number of ApplicationEntity objects with the specified name.
   *
   * @param name      The name to count by.
   * @param serviceId id of service
   * @return The number of ApplicationEntity objects with the specified name.
   */
  long countByNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String name, String serviceId);

  /**
   * Counts the number of ApplicationEntity objects with the specified name and excluding the specified id.
   *
   * @param id        The id to exclude.
   * @param name      The name to count by.
   * @param serviceId id of service
   * @return The number of ApplicationEntity objects with the specified name and excluding the specified id.
   */
  long countByIdNotAndNameIgnoreCaseAndServiceIdAndDeletedIsFalse(String id, String name,
                                                                  String serviceId);

  /**
   * Retrieves a list of names from the `APPLICATION` table where the IDs match the provided list.
   *
   * @param ids a list of IDs to match against the `ID` field.
   * @return a list of names corresponding to the provided IDs.
   */
  List<String> findAllNameByIdIn(List<String> ids);

  /**
   * Find application by list applicationId.
   *
   * @param ids applicationIds
   * @return ApplicationResponse
   */
  List<ApplicationResponse> findAllByIdIn(List<String> ids);

  /**
   * find all application by serviceIds.
   *
   * @param serviceIds list Service ids.
   * @param deleted    deleted status
   * @return List of ApplicationEntity.
   */
  List<ApplicationEntity> findAllByServiceIdInAndDeleted(List<String> serviceIds, boolean deleted);

  /**
   * Find all by list applicationNames or applicationIds and serviceIds.
   *
   * @param applicationNames applicationNames
   * @param applicationIds   applicationIds
   * @param serviceIds       serviceIds
   * @return list application
   */
  List<ApplicationEntity> findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
      List<String> applicationNames, List<String> applicationIds, List<String> serviceIds);

  /**
   * find all application by serviceIds.
   *
   * @param request expport data request of alert.
   * @param userName    userName
   * @param path  path of file.
   * @return List of ApplicationEntity.
   */
  FileStorageEntity exportFile(ExportFileApplicationRequest request, String userName, String path) throws IOException,
      BusinessException;
}
