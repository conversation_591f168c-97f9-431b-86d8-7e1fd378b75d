package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;

/**
 * Custom Repo table Alert.
 */
public interface AlertRepositoryCustom {

  /**
   * find all single alert by condition.
   *
   * @param serviceIds                      list of serviceId.
   * @param applicationIds                  list of applicationId
   * @param alertGroupHandleTriggerInterval createTime range
   * @param status                          status
   * @return list of alert.
   */
  List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status);

  /**
   * find all alert by ids.
   *
   * @param alertGroupId   alertGroupId
   * @param status         status.
   * @param numberOfResult numberOfResult
   * @return List of AlertEntity.
   */
  List<AlertEntity> findTopAlertsByAlertGroupIdAndStatus(Long alertGroupId, AlertStatusEnum status, int numberOfResult);

  /**
   * find all alert by paginationRequest.
   *
   * @param paginationRequest Pagination parameters for the request.
   * @return a page of AlertResponseDto.
   */
  CursorPageResponse<AlertResponse, AlertCursor> findAll(AlertPaginationRequest paginationRequest);

  /**
   * find all alert by alertGroupId.
   *
   * @param alertGroupIds to find alert
   * @return list of AlertEntity
   */
  List<AlertEntity> findAlertKeyByGroupIdIn(List<Long> alertGroupIds);

}
