package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;

/**
 * Repository table monitor web config.
 */
@Repository
public interface MonitorWebConfigRepository extends JpaCommonRepository<MonitorWebConfigEntity, String> {
  /**
   * find all monitor web configs are active.
   *
   * @return lst
   */
  List<MonitorWebConfigEntity> findAllByActiveTrue();
}
