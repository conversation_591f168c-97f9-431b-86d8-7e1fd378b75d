package vn.com.mbbank.kanban.mbmonitor.external.job.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.utils.KanbanSqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.impl.AlertGroupConfigRepositoryCustomImpl;

@ExtendWith({MockitoExtension.class})
class AlertGroupConfigRepositoryCustomImplTest {
  @Mock
  SqlQueryUtil sqlQueryUtil;
  @Mock
  KanbanSqlQueryUtil.SqlQueryModel sqlQueryModel;
  @InjectMocks
  AlertGroupConfigRepositoryCustomImpl alertGroupConfigRepositoryCustomImpl;

  @Test
  void findAll_success() {
    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), any())).thenReturn(List.of());
    var res = alertGroupConfigRepositoryCustomImpl.findAllByServiceIdAndApplicationIdAndDeletedAndActive(
        "123", "123", true, true);
    assertEquals(res.size(), 0);
  }

  @Test
  void findAllByDeletedAndSearch_success() {
    when(sqlQueryUtil.queryModel()).thenReturn(sqlQueryModel);
    when(sqlQueryModel.queryForList(anyString(), anyMap(), any())).thenReturn(List.of());
    var res = alertGroupConfigRepositoryCustomImpl.findAllByDeletedAndSearch(
        true, "abc");
    assertEquals(res.size(), 0);
  }

  @Test
  void buildServiceIdsLike_success() {
    String serviceId = "123";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildServiceIdsLike", serviceId
    );
    assertNotNull(result);
  }


  @Test
  void buildServiceIdsLike_success_caseServiceIdBlank() {
    String serviceId = "";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildServiceIdsLike", serviceId
    );
    assertNull(result);
  }

  @Test
  void buildNameOrDescriptionLike_success() {
    String search = "123";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildNameOrDescriptionLike", search
    );
    assertNotNull(result);
  }


  @Test
  void buildNameOrDescriptionLike_success_caseServiceIdBlank() {
    String search = "";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildNameOrDescriptionLike", search
    );
    assertNull(result);
  }

  @Test
  void buildApplicationIdsLike_success() {
    String applicationId = "123";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildApplicationIdsLike", applicationId
    );
    assertNotNull(result);
  }

  @Test
  void buildApplicationIdsLike_success_caseApplicationIdBlank() {
    String applicationId = "";
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildApplicationIdsLike", applicationId
    );
    assertNull(result);
  }

  @Test
  void buildDeletedEqual_success() {
    Boolean deleted = true;
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildDeletedEqual", deleted
    );
    assertNotNull(result);
  }

  @Test
  void buildDeletedEqual_success_caseDeletedNull() {
    Boolean deleted = null;
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildDeletedEqual", deleted
    );
    assertNull(result);
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void buildActiveEqual_success() {
    Boolean active = true;
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildActiveEqual", active
    );
    assertNotNull(result);
  }

  @Test
  void buildActiveEqual_success_caseActiveNull() {
    Boolean active = null;
    PrepareQuery result = ReflectionTestUtils.invokeMethod(
        alertGroupConfigRepositoryCustomImpl, "buildActiveEqual", active
    );
    assertNull(result);
  }
}
