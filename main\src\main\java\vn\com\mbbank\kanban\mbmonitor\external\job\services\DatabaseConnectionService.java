package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@Service
public interface DatabaseConnectionService extends BaseService<DatabaseConnectionEntity, Long> {

  /**
   * Check connection valid.
   *
   * @param connectInfo connectInfo
   * @throws BusinessException ex
   */
  void checkConnection(DatabaseConnectionRequest connectInfo)
      throws BusinessException;


  /**
   * check connection by connection id.
   *
   * @param connectionId connectionId
   * @throws BusinessException ex
   */
  void checkConnection(Long connectionId)
      throws BusinessException;

  /**
   * Save connection valid.
   *
   * @param request connection info
   * @return connection info
   * @throws BusinessException ex
   */
  DatabaseConnectionEntity saveDataValid(DatabaseConnectionRequest request)
      throws BusinessException;

  /**
   * set active role.
   *
   * @param id     id
   * @param active active
   * @return total row update
   */
  int setActiveById(Long id, boolean active);

}
