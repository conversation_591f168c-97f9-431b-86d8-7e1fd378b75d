package vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs;

import java.util.Map;
import java.util.stream.Collectors;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonBaseConsumerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DatabaseCollectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.configs.jobs.core.JobConfig;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/22/2024
 */
@Component(JobNameConstants.DATABASE_COLLECT)
public class DatabaseJob extends JobConfig implements CommonBaseConsumerService {
  private static final Logger logger = LoggerFactory.getLogger(DatabaseJob.class);
  @Autowired
  private DatabaseCollectService databaseCollectService;

  @Value("${monitor.redis.lock.database.timeout:10000}")
  private Long lockTimeoutMs;

  @Override
  public void executeJob(JobExecutionContext context) throws BusinessException {
    Long key = DatabaseCollectUtils.getId(context.getJobDetail().getKey().getName());
    databaseCollectService.collect(key);
  }

  @Override
  public Map<String, Long> getMappingJobNameAndIntervalTime() {
    var lstConfig = databaseCollectService.findAllByIsActiveTrue();
    return lstConfig.stream()
        .collect(Collectors.toMap(
            obj -> String.valueOf(obj.getId()),
            obj -> obj.getInterval() * 1000
        ));
  }


  @Override
  public String getGroupName() {
    return JobNameConstants.DATABASE_COLLECT;
  }

  @Override
  public Long getLockTimeoutMs() {
    return this.lockTimeoutMs;
  }


  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.DATABASE_COLLECT;
  }

  @Override
  public boolean isKafkaMultipleGroup() {
    return true;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    KafkaJobModel kafkaJobModel =
        KanbanMapperUtils.jsonToObject(data.getValue().toString(), KafkaJobModel.class);
    var collectId = kafkaJobModel.configId().toString();
    var config = databaseCollectService.findById(Long.valueOf(collectId));
    if (!KanbanCommonUtil.isEmpty(config)) {
      var interval = config.getInterval() * 1000;
      boolean isUpdate = KafkaJobTypeEnum.NEW_OR_UPDATE.equals(kafkaJobModel.type());
      changeConfigJob(isUpdate, config.getIsActive(), collectId, interval);
    }
  }
}
