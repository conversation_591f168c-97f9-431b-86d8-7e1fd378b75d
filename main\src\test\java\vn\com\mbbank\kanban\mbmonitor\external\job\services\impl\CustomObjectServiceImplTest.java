package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.CustomObjectModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.CustomObjectModelCustomObjectEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.CustomObjectRepository;

@ExtendWith(MockitoExtension.class)
public class CustomObjectServiceImplTest {

  @Mock
  private CustomObjectRepository customObjectRepository;

  @Mock
  private CustomObjectModelCustomObjectEntityMapper customObjectModelCustomObjectEntityMapper;

  @Mock
  private CustomObjectUtils customObjectUtils;

  @InjectMocks
  private CustomObjectServiceImpl customObjectService;

  private static final Long ID = 1L;
  private static final String CONTENT = "some content";
  private static final String ID_STRING = "1";
  private CustomObjectModel customObjectModel;

  @BeforeEach
  public void setUp() {
    customObjectModel = new CustomObjectModel();
    customObjectModel.setId(ID);
    customObjectModel.setType(CustomObjectTypeEnum.REGEX);
    customObjectModel.setRegex("/Service \\d+/");
  }

  @Test
  public void getRepository_test() {
    // Verifying the repository method returns the correct repository
    JpaCommonRepository<CustomObjectEntity, Long> repository = customObjectService.getRepository();
    assertNotNull(repository, "Repository should not be null");
  }

  @Test
  public void calculatorCustomObjectValue_success() {
    when(customObjectRepository.findById(ID)).thenReturn(Optional.empty());
    String result = customObjectService.calculatorCustomObjectValue(CONTENT, ID_STRING);
    assertEquals(result, "");
    verify(customObjectRepository, times(1)).findById(ID);
  }

  @Test
  void findAllByMaintenanceTimeConfigId_success() {
    Mockito.when(customObjectRepository.findAllByDeletedIsFalse()).thenReturn(List.of());
    var res = customObjectService.findAllByDeletedIsFalse();
    Assertions.assertEquals(0, res.size());
  }
  @Test
  void findAllByAlertGroupConfigId_success() {
    Mockito.when(customObjectRepository.findAllByAlertGroupConfigId(any())).thenReturn(List.of());
    var res = customObjectService.findAllByAlertGroupConfigId(1L);
    Assertions.assertEquals(0, res.size());
  }
  @Test
  void replaceCustomObjectIdsWithCustomObjectValues_success2(){
    var customObj = new CustomObjectEntity();
    customObj.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObj.setFromIndex(1);
    customObj.setToIndex(3);
    when(customObjectRepository.findById(any())).thenReturn(Optional.empty());
    var res1 = customObjectService.replaceCustomObjectIdsWithCustomObjectValues("bao @123 @1", "Nguyen hOnag Bao @12");
    assertNotNull(res1);
  }
  @Test
  void replaceCustomObjectIdsWithCustomObjectValues_success(){
    var customObj = new CustomObjectEntity();
    customObj.setType(CustomObjectTypeEnum.INDEX_TO_INDEX);
    customObj.setFromIndex(1);
    customObj.setToIndex(3);
    when(customObjectRepository.findById(any())).thenReturn(Optional.of(customObj));
    var res1 = customObjectService.replaceCustomObjectIdsWithCustomObjectValues("bao @123 @1", "Nguyen hOnag Bao @12");
    assertNotNull(res1);
  }
  @Test
  void replaceCustomObjectIdsWithCustomObjectValues_success_notCustomObject(){
    var res1 = customObjectService.replaceCustomObjectIdsWithCustomObjectValues("bao", "Nguyen hOnag Bao 2");
    assertNotNull(res1);
  }
}
