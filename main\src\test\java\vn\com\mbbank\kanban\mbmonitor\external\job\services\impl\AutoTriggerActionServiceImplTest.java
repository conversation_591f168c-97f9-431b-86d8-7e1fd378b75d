package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.*;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.*;

class AutoTriggerActionServiceImplTest {

  @InjectMocks
  private AutoTriggerActionServiceImpl autoTriggerActionService;

  @Mock
  private AutoTriggerActionConfigRepository autoTriggerActionConfigRepository;

  @Mock
  private CustomObjectService customObjectService;

  @Mock
  private AlertService alertService;

  @Mock
  private AutoTriggerActionConfigDependencyService autoTriggerActionConfigDependencyService;

  @Mock
  private AutoTriggerActionConfigExecutionMapService triggerActionMapService;

  @Mock
  private ExecutionService executionService;

  @Mock
  private ExecutionParamService executionParamService;

  @Mock
  private VariableService variableService;

  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private Executor commonTaskExecutor;
  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    commonTaskExecutor = mock(Executor.class);
    ReflectionTestUtils.setField(autoTriggerActionService, "commonTaskExecutor", commonTaskExecutor);
  }

  @Test
  void testTriggerJob_noAlertsOrConfigs_shouldReturnEarly() {
    when(alertService.findAlertKeyByGroupIdIn(anyList())).thenReturn(Collections.emptyList());
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION)).thenReturn(Collections.emptyList());

    autoTriggerActionService.triggerJob(Collections.emptyList(), List.of(1L, 2L), true, null);

    verify(autoTriggerActionConfigRepository, times(1)).findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION);
    verifyNoMoreInteractions(autoTriggerActionConfigRepository);
    verifyNoInteractions(executionService);
  }


  @Test
  void testDependenciesMatch_variousCases() throws Exception {
    var method = AutoTriggerActionServiceImpl.class.getDeclaredMethod(
            "dependenciesMatch", AutoTriggerActionConfigEntity.class, AlertEntity.class, Map.class);
    method.setAccessible(true);

    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setId("c1");

    AlertEntity alert = new AlertEntity();
    alert.setApplicationId("app1");
    alert.setServiceId("svc1");

    AutoTriggerActionConfigDependencyEntity depApp = new AutoTriggerActionConfigDependencyEntity();
    depApp.setType(DependencyTypeEnum.APPLICATION);
    depApp.setDependencyId("app1");
    depApp.setAutoTriggerActionConfigId("c1");

    AutoTriggerActionConfigDependencyEntity depSvc = new AutoTriggerActionConfigDependencyEntity();
    depSvc.setType(DependencyTypeEnum.SERVICE);
    depSvc.setDependencyId("svc1");
    depSvc.setAutoTriggerActionConfigId("c1");

    AutoTriggerActionConfigDependencyEntity depSvcAllApp = new AutoTriggerActionConfigDependencyEntity();
    depSvcAllApp.setType(DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    depSvcAllApp.setDependencyId("svc1");
    depSvcAllApp.setAutoTriggerActionConfigId("c1");

    // Case empty dependencies = true
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> emptyDeps = Collections.emptyMap();
    boolean resultEmpty = (boolean) method.invoke(autoTriggerActionService, config, alert, emptyDeps);
    assertTrue(resultEmpty);

    // Case application + service match
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> deps = Map.of(
            "c1", List.of(depApp, depSvc));
    boolean resultMatch = (boolean) method.invoke(autoTriggerActionService, config, alert, deps);
    assertTrue(resultMatch);

    // Case only SERVICE_WITH_ALL_APPLICATION match
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> depsSvcAllApp = Map.of(
            "c1", List.of(depSvcAllApp));
    boolean resultSvcAllApp = (boolean) method.invoke(autoTriggerActionService, config, alert, depsSvcAllApp);
    assertTrue(resultSvcAllApp);

    // Case no match
    AutoTriggerActionConfigDependencyEntity depAppNotMatch = new AutoTriggerActionConfigDependencyEntity();
    depAppNotMatch.setType(DependencyTypeEnum.APPLICATION);
    depAppNotMatch.setDependencyId("app2");
    depAppNotMatch.setAutoTriggerActionConfigId("c1");

    Map<String, List<AutoTriggerActionConfigDependencyEntity>> depsNoMatch = Map.of(
            "c1", List.of(depAppNotMatch, depSvc));
    boolean resultNoMatch = (boolean) method.invoke(autoTriggerActionService, config, alert, depsNoMatch);
    assertFalse(resultNoMatch);
  }
  @Test
  void triggerJob_shouldDoNothing_whenNoActiveConfigs() throws BusinessException {
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION)).thenReturn(Collections.emptyList());

    autoTriggerActionService.triggerJob(List.of(new AlertEntity()), List.of(), true, null);

    verify(executionService, never()).process(any(), any());
    verify(autoTriggerActionConfigRepository, never()).save(any());
  }
  @Test
  void testTriggerJob_matchingConfigExecutionFlow() throws BusinessException {
    AlertEntity alertFromService = new AlertEntity();
    alertFromService.setApplicationId("app1");
    alertFromService.setServiceId("svc1");
    alertFromService.setContent("test content");

    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setId("config1");
    config.setActive(true);
    config.setName("Config 1");
    config.setTimeSinceLastTrigger(100L);
    config.setLastRun(null);

    RuleElement alwaysTrueRule = new RuleElement() {
      @Override public boolean check(Object object) { return true; }
      @Override public boolean check(Map<String, Object> object) { return true; }
      @Override public <V> boolean check(Object object, Function<Object, V> func) { return true; }
      @Override public boolean checkPriority(Long id) { return true; }
      @Override public boolean checkCustomObject(Long id) { return true; }
      
      @Override
      public List<Long> getCustomObjectIds(Class<?> clazz) {
        return List.of();
      }
      
      @Override
      public List<Long> getCustomObjectIds(Set<String> fieldNames) {
        return List.of();
      }
      
      @Override
      public void setValueFromExternal(Object newVal) {
      
      }
    };

    RuleGroupType ruleGroup = RuleGroupType.builder()
            .combinator(ConditionCombinatorEnum.AND)
            .rules(List.of(alwaysTrueRule))
            .build();
    config.setRuleGroup(ruleGroup);

    Executor commonTaskExecutor = mock(Executor.class);
    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));
    ReflectionTestUtils.setField(autoTriggerActionService, "commonTaskExecutor", commonTaskExecutor);

    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION)).thenReturn(List.of(config));
    when(alertService.findAlertKeyByGroupIdIn(anyList())).thenReturn(new ArrayList<>(List.of(alertFromService)));
    when(customObjectService.calculatorCustomObjectValue(anyString(), anyString())).thenReturn("abc");
    when(customObjectService.findAllByDeletedIsFalse()).thenReturn(List.of());
    when(autoTriggerActionConfigDependencyService.findAllByAutoTriggerActionIdIn(anyList()))
            .thenReturn(Collections.emptyList());
    AutoTriggerActionConfigExecutionMapEntity mapEntity = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity.setExecutionId("exec1");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config1")).thenReturn(List.of(mapEntity));

    ExecutionEntity executionEntity = new ExecutionEntity();
    executionEntity.setId("exec1");
    executionEntity.setName("Execution 1");
    when(executionService.findAllByIdIn(anyList())).thenReturn(List.of(executionEntity));

    doNothing().when(executionService).process(any(ExecutionEntity.class), anyString());

    autoTriggerActionService.triggerJob(Collections.emptyList(), List.of(1L), true, null);

    verify(executionService, times(1)).process(any(ExecutionEntity.class), eq("Config 1"));
    verify(autoTriggerActionConfigRepository, times(1)).save(argThat(conf -> conf.getLastRun() != null));
  }
}
