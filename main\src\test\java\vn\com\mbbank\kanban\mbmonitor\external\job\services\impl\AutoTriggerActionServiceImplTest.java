package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.*;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.*;

@ExtendWith(MockitoExtension.class)
class AutoTriggerActionServiceImplTest {

  @InjectMocks
  private AutoTriggerActionServiceImpl autoTriggerActionService;

  @Mock
  private AutoTriggerActionConfigRepository autoTriggerActionConfigRepository;

  @Mock
  private CustomObjectService customObjectService;

  @Mock
  private AlertService alertService;

  @Mock
  private AutoTriggerActionConfigDependencyService autoTriggerActionConfigDependencyService;

  @Mock
  private AutoTriggerActionConfigExecutionMapService triggerActionMapService;

  @Mock
  private ExecutionService executionService;

  @Mock
  private ExecutionParamService executionParamService;

  @Mock
  private VariableService variableService;

  @Mock
  private SysLogKafkaProducerService sysLogKafkaProducerService;
  @Mock
  private Executor commonTaskExecutor;
  private AutoTriggerActionConfigEntity createTestConfig(String id, String name, AutoTriggerTypeEnum triggerType, boolean active) {
    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setId(id);
    config.setName(name);
    config.setTriggerType(triggerType);
    config.setActive(active);
    config.setTimeSinceLastTrigger(3600L);

    // Create a mock RuleGroupType
    RuleGroupType ruleGroup = mock(RuleGroupType.class);
    when(ruleGroup.check(any())).thenReturn(true);
    config.setRuleGroup(ruleGroup);

    return config;
  }

  private AlertEntity createTestAlert(Long id, String serviceId, String applicationId) {
    AlertEntity alert = new AlertEntity();
    alert.setId(id);
    alert.setServiceId(serviceId);
    alert.setApplicationId(applicationId);
    return alert;
  }

  private ExecutionEntity createTestExecution(String id, String name) {
    ExecutionEntity execution = new ExecutionEntity();
    execution.setId(id);
    execution.setName(name);
    return execution;
  }

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    commonTaskExecutor = mock(Executor.class);
    ReflectionTestUtils.setField(autoTriggerActionService, "commonTaskExecutor", commonTaskExecutor);
  }

  @Test
  void testGetRepository_shouldReturnAutoTriggerActionConfigRepository() {
    // When & Then
    assertEquals(autoTriggerActionConfigRepository, autoTriggerActionService.getRepository());
  }

  @Test
  void testTriggerJob_noAlertsOrConfigs_shouldReturnEarly() {
    // Given
    when(alertService.findAlertKeyByGroupIdIn(anyList())).thenReturn(Collections.emptyList());
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION))
            .thenReturn(Collections.emptyList());

    // When
    autoTriggerActionService.triggerJob(Collections.emptyList(), List.of(1L, 2L), true, null);

    // Then
    verify(autoTriggerActionConfigRepository, times(1))
            .findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION);
    verifyNoMoreInteractions(autoTriggerActionConfigRepository);
    verifyNoInteractions(executionService);
  }

  @Test
  void testTriggerJob_noCondition_shouldProcessDirectly() throws BusinessException {
    // Given
    AutoTriggerActionConfigEntity config = createTestConfig("config1", "Test Config", AutoTriggerTypeEnum.TIME, true);

    AutoTriggerActionConfigExecutionMapEntity mapEntity = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity.setExecutionId("exec1");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config1")).thenReturn(List.of(mapEntity));

    ExecutionEntity execution = createTestExecution("exec1", "Test Execution");
    when(executionService.findAllByIdIn(List.of("exec1"))).thenReturn(List.of(execution));

    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));

    doNothing().when(executionService).process(any(ExecutionEntity.class), anyString());

    // When
    autoTriggerActionService.triggerJob(null, null, false, config);

    // Then
    verify(executionService, times(1)).process(execution, "Test Config");
    verify(autoTriggerActionConfigRepository, times(1)).save(argThat(conf -> conf.getLastRun() != null));
  }


  @Test
  void testDependenciesMatch_variousCases() throws Exception {
    var method = AutoTriggerActionServiceImpl.class.getDeclaredMethod(
            "dependenciesMatch", AutoTriggerActionConfigEntity.class, AlertEntity.class, Map.class);
    method.setAccessible(true);

    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setId("c1");

    AlertEntity alert = new AlertEntity();
    alert.setApplicationId("app1");
    alert.setServiceId("svc1");

    AutoTriggerActionConfigDependencyEntity depApp = new AutoTriggerActionConfigDependencyEntity();
    depApp.setType(DependencyTypeEnum.APPLICATION);
    depApp.setDependencyId("app1");
    depApp.setAutoTriggerActionConfigId("c1");

    AutoTriggerActionConfigDependencyEntity depSvc = new AutoTriggerActionConfigDependencyEntity();
    depSvc.setType(DependencyTypeEnum.SERVICE);
    depSvc.setDependencyId("svc1");
    depSvc.setAutoTriggerActionConfigId("c1");

    AutoTriggerActionConfigDependencyEntity depSvcAllApp = new AutoTriggerActionConfigDependencyEntity();
    depSvcAllApp.setType(DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
    depSvcAllApp.setDependencyId("svc1");
    depSvcAllApp.setAutoTriggerActionConfigId("c1");

    // Case empty dependencies = true
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> emptyDeps = Collections.emptyMap();
    boolean resultEmpty = (boolean) method.invoke(autoTriggerActionService, config, alert, emptyDeps);
    assertTrue(resultEmpty);

    // Case application + service match
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> deps = Map.of(
            "c1", List.of(depApp, depSvc));
    boolean resultMatch = (boolean) method.invoke(autoTriggerActionService, config, alert, deps);
    assertTrue(resultMatch);

    // Case only SERVICE_WITH_ALL_APPLICATION match
    Map<String, List<AutoTriggerActionConfigDependencyEntity>> depsSvcAllApp = Map.of(
            "c1", List.of(depSvcAllApp));
    boolean resultSvcAllApp = (boolean) method.invoke(autoTriggerActionService, config, alert, depsSvcAllApp);
    assertTrue(resultSvcAllApp);

    // Case no match
    AutoTriggerActionConfigDependencyEntity depAppNotMatch = new AutoTriggerActionConfigDependencyEntity();
    depAppNotMatch.setType(DependencyTypeEnum.APPLICATION);
    depAppNotMatch.setDependencyId("app2");
    depAppNotMatch.setAutoTriggerActionConfigId("c1");

    Map<String, List<AutoTriggerActionConfigDependencyEntity>> depsNoMatch = Map.of(
            "c1", List.of(depAppNotMatch, depSvc));
    boolean resultNoMatch = (boolean) method.invoke(autoTriggerActionService, config, alert, depsNoMatch);
    assertFalse(resultNoMatch);
  }
  @Test
  void triggerJob_shouldDoNothing_whenNoActiveConfigs() throws BusinessException {
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION)).thenReturn(Collections.emptyList());

    autoTriggerActionService.triggerJob(List.of(new AlertEntity()), List.of(), true, null);

    verify(executionService, never()).process(any(), any());
    verify(autoTriggerActionConfigRepository, never()).save(any());
  }
  @Test
  void testTriggerJob_matchingConfigExecutionFlow() throws BusinessException {
    AlertEntity alertFromService = new AlertEntity();
    alertFromService.setApplicationId("app1");
    alertFromService.setServiceId("svc1");
    alertFromService.setContent("test content");

    AutoTriggerActionConfigEntity config = new AutoTriggerActionConfigEntity();
    config.setId("config1");
    config.setActive(true);
    config.setName("Config 1");
    config.setTimeSinceLastTrigger(100L);
    config.setLastRun(null);

    RuleElement alwaysTrueRule = new RuleElement() {
      @Override public boolean check(Object object) { return true; }
      @Override public boolean check(Map<String, Object> object) { return true; }
      @Override public <V> boolean check(Object object, Function<Object, V> func) { return true; }
      @Override public boolean checkPriority(Long id) { return true; }
      @Override public boolean checkCustomObject(Long id) { return true; }
      
      @Override
      public List<Long> getCustomObjectIds(Class<?> clazz) {
        return List.of();
      }
      
      @Override
      public List<Long> getCustomObjectIds(Set<String> fieldNames) {
        return List.of();
      }
      
      @Override
      public void setValueFromExternal(Object newVal) {
      
      }
    };

    RuleGroupType ruleGroup = RuleGroupType.builder()
            .combinator(ConditionCombinatorEnum.AND)
            .rules(List.of(alwaysTrueRule))
            .build();
    config.setRuleGroup(ruleGroup);

    Executor commonTaskExecutor = mock(Executor.class);
    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));
    ReflectionTestUtils.setField(autoTriggerActionService, "commonTaskExecutor", commonTaskExecutor);

    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION)).thenReturn(List.of(config));
    when(alertService.findAlertKeyByGroupIdIn(anyList())).thenReturn(new ArrayList<>(List.of(alertFromService)));
    when(customObjectService.calculatorCustomObjectValue(anyString(), anyString())).thenReturn("abc");
    when(customObjectService.findAllByDeletedIsFalse()).thenReturn(List.of());
    when(autoTriggerActionConfigDependencyService.findAllByAutoTriggerActionIdIn(anyList()))
            .thenReturn(Collections.emptyList());
    AutoTriggerActionConfigExecutionMapEntity mapEntity = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity.setExecutionId("exec1");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config1")).thenReturn(List.of(mapEntity));

    ExecutionEntity executionEntity = new ExecutionEntity();
    executionEntity.setId("exec1");
    executionEntity.setName("Execution 1");
    when(executionService.findAllByIdIn(anyList())).thenReturn(List.of(executionEntity));

    doNothing().when(executionService).process(any(ExecutionEntity.class), anyString());

    autoTriggerActionService.triggerJob(Collections.emptyList(), List.of(1L), true, null);

    verify(executionService, times(1)).process(any(ExecutionEntity.class), eq("Config 1"));
    verify(autoTriggerActionConfigRepository, times(1)).save(argThat(conf -> conf.getLastRun() != null));
  }

  @Test
  void testCollect_shouldThrowException_whenConfigNotFound() {
    // Given
    String configId = "nonexistent";
    when(autoTriggerActionConfigRepository.findById(configId)).thenReturn(Optional.empty());

    // When & Then
    BusinessException exception = assertThrows(BusinessException.class,
            () -> autoTriggerActionService.collect(configId));
    assertEquals(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void testCollect_shouldThrowException_whenConfigInactive() {
    // Given
    String configId = "config1";
    AutoTriggerActionConfigEntity config = createTestConfig(configId, "Test Config", AutoTriggerTypeEnum.TIME, false);
    when(autoTriggerActionConfigRepository.findById(configId)).thenReturn(Optional.of(config));

    // When & Then
    BusinessException exception = assertThrows(BusinessException.class,
            () -> autoTriggerActionService.collect(configId));
    assertEquals(ErrorCode.AUTO_TRIGGER_APPLICATION_INVALID.getCode(), exception.getCode());
  }

  @Test
  void testCollect_shouldThrowException_whenConfigNotTimeType() {
    // Given
    String configId = "config1";
    AutoTriggerActionConfigEntity config = createTestConfig(configId, "Test Config", AutoTriggerTypeEnum.CONDITION, true);
    when(autoTriggerActionConfigRepository.findById(configId)).thenReturn(Optional.of(config));

    // When & Then
    BusinessException exception = assertThrows(BusinessException.class,
            () -> autoTriggerActionService.collect(configId));
    assertEquals(ErrorCode.AUTO_TRIGGER_APPLICATION_INVALID.getCode(), exception.getCode());
  }

  @Test
  void testCollect_shouldTriggerJob_whenValidConfig() throws BusinessException {
    // Given
    String configId = "config1";
    AutoTriggerActionConfigEntity config = createTestConfig(configId, "Test Config", AutoTriggerTypeEnum.TIME, true);
    when(autoTriggerActionConfigRepository.findById(configId)).thenReturn(Optional.of(config));

    AutoTriggerActionConfigExecutionMapEntity mapEntity = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity.setExecutionId("exec1");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId(configId)).thenReturn(List.of(mapEntity));

    ExecutionEntity execution = createTestExecution("exec1", "Test Execution");
    when(executionService.findAllByIdIn(List.of("exec1"))).thenReturn(List.of(execution));

    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));

    doNothing().when(executionService).process(any(ExecutionEntity.class), anyString());

    // When
    autoTriggerActionService.collect(configId);

    // Then
    verify(autoTriggerActionConfigRepository, times(1)).findById(configId);
    verify(executionService, times(1)).process(execution, "Test Config");
    verify(autoTriggerActionConfigRepository, times(1)).save(argThat(conf -> conf.getLastRun() != null));
  }

  @Test
  void testFindAllByActiveTrueAndTriggerType_shouldReturnTimeTypeConfigs() {
    // Given
    List<AutoTriggerActionConfigEntity> expectedConfigs = List.of(
            createTestConfig("config1", "Config 1", AutoTriggerTypeEnum.TIME, true),
            createTestConfig("config2", "Config 2", AutoTriggerTypeEnum.TIME, true)
    );
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.TIME))
            .thenReturn(expectedConfigs);

    // When
    List<AutoTriggerActionConfigEntity> result = autoTriggerActionService.findAllByActiveTrueAndTriggerType();

    // Then
    assertEquals(expectedConfigs, result);
    verify(autoTriggerActionConfigRepository, times(1))
            .findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.TIME);
  }

  @Test
  void testTriggerJob_withEmptyExecutions_shouldNotProcessAnyExecution() throws BusinessException {
    // Given
    AutoTriggerActionConfigEntity config = createTestConfig("config1", "Test Config", AutoTriggerTypeEnum.TIME, true);

    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config1")).thenReturn(Collections.emptyList());

    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));

    // When
    autoTriggerActionService.triggerJob(null, null, false, config);

    // Then
    verify(executionService, never()).process(any(ExecutionEntity.class), anyString());
    verify(autoTriggerActionConfigRepository, never()).save(any());
  }

  @Test
  void testTriggerJob_withMultipleConfigs_shouldProcessAllMatching() throws BusinessException {
    // Given
    AlertEntity alert = createTestAlert(1L, "service1", "app1");

    AutoTriggerActionConfigEntity config1 = createTestConfig("config1", "Config 1", AutoTriggerTypeEnum.CONDITION, true);
    AutoTriggerActionConfigEntity config2 = createTestConfig("config2", "Config 2", AutoTriggerTypeEnum.CONDITION, true);

    when(alertService.findAlertKeyByGroupIdIn(anyList())).thenReturn(List.of(alert));
    when(autoTriggerActionConfigRepository.findAllWithActiveTrueAndTriggerType(AutoTriggerTypeEnum.CONDITION))
            .thenReturn(List.of(config1, config2));
    when(customObjectService.findAllByDeletedIsFalse()).thenReturn(Collections.emptyList());
    when(autoTriggerActionConfigDependencyService.findAllByAutoTriggerActionIdIn(anyList()))
            .thenReturn(Collections.emptyList());

    AutoTriggerActionConfigExecutionMapEntity mapEntity1 = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity1.setExecutionId("exec1");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config1")).thenReturn(List.of(mapEntity1));

    AutoTriggerActionConfigExecutionMapEntity mapEntity2 = new AutoTriggerActionConfigExecutionMapEntity();
    mapEntity2.setExecutionId("exec2");
    when(triggerActionMapService.findAllByAutoTriggerActionConfigId("config2")).thenReturn(List.of(mapEntity2));

    ExecutionEntity execution1 = createTestExecution("exec1", "Execution 1");
    ExecutionEntity execution2 = createTestExecution("exec2", "Execution 2");
    when(executionService.findAllByIdIn(anyList())).thenReturn(List.of(execution1, execution2));

    doAnswer(invocation -> {
      Runnable r = invocation.getArgument(0);
      r.run();
      return null;
    }).when(commonTaskExecutor).execute(any(Runnable.class));

    doNothing().when(executionService).process(any(ExecutionEntity.class), anyString());

    // When
    autoTriggerActionService.triggerJob(Collections.emptyList(), List.of(1L), true, null);

    // Then
    verify(executionService, times(1)).process(execution1, "Config 1");
    verify(executionService, times(1)).process(execution2, "Config 2");
    verify(autoTriggerActionConfigRepository, times(2)).save(any(AutoTriggerActionConfigEntity.class));
  }
}
